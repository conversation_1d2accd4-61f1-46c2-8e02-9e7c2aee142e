// ========================================
// 🎭 أمر إدارة الرتب - CS Discord
// ========================================
// 📺 يوتيوب: https://www.youtube.com/@CS_Discord
// 💬 ديسكورد: https://discord.gg/yqTn2EwVsd
// ========================================

const { EmbedBuilder, PermissionFlagsBits } = require('discord.js');

module.exports = {
  name: 'role',
  aliases: ['رتبة', 'دور'],
  description: 'إدارة رتب الأعضاء (إضافة/حذف)',
  usage: '<add/remove> <@المستخدم> <@الرتبة>',
  category: 'moderation',
  permissions: [PermissionFlagsBits.ManageRoles],
  cooldown: 3,
  
  async execute(message, args) {
    try {
      // التحقق من الصلاحيات
      if (!message.member.permissions.has(PermissionFlagsBits.ManageRoles)) {
        return await message.reply('❌ ليس لديك صلاحية لإدارة الرتب!');
      }
      
      // التحقق من وجود المعاملات
      if (args.length < 3) {
        return await message.reply('❌ استخدام خاطئ!\nالاستخدام: `role <add/remove> <@المستخدم> <@الرتبة>`\nمثال: `role add @أحمد @VIP`');
      }
      
      // تحديد العملية
      const action = args[0].toLowerCase();
      if (!['add', 'remove', 'اضافة', 'حذف'].includes(action)) {
        return await message.reply('❌ العملية يجب أن تكون `add` أو `remove` (أو `اضافة` أو `حذف`)');
      }
      
      // تحديد المستخدم
      const targetUser = message.mentions.members.first();
      if (!targetUser) {
        return await message.reply('❌ يجب ذكر المستخدم!\nمثال: `role add @أحمد @VIP`');
      }
      
      // تحديد الرتبة
      const targetRole = message.mentions.roles.first();
      if (!targetRole) {
        return await message.reply('❌ يجب ذكر الرتبة!\nمثال: `role add @أحمد @VIP`');
      }
      
      // التحقق من صلاحيات البوت
      if (!message.guild.members.me.permissions.has(PermissionFlagsBits.ManageRoles)) {
        return await message.reply('❌ البوت لا يملك صلاحية إدارة الرتب!');
      }
      
      // التحقق من ترتيب الرتب
      if (targetRole.position >= message.guild.members.me.roles.highest.position) {
        return await message.reply('❌ لا يمكنني إدارة هذه الرتبة! رتبة البوت يجب أن تكون أعلى من الرتبة المطلوبة.');
      }
      
      // التحقق من ترتيب رتبة المشرف
      if (targetRole.position >= message.member.roles.highest.position && message.author.id !== message.guild.ownerId) {
        return await message.reply('❌ لا يمكنك إدارة رتبة أعلى من رتبتك!');
      }
      
      // تنفيذ العملية
      const isAdd = ['add', 'اضافة'].includes(action);
      
      try {
        if (isAdd) {
          // إضافة الرتبة
          if (targetUser.roles.cache.has(targetRole.id)) {
            return await message.reply(`❌ ${targetUser.user.username} يملك الرتبة ${targetRole.name} بالفعل!`);
          }
          
          await targetUser.roles.add(targetRole, `Role added by ${message.author.tag}`);
          
          // إنشاء embed النجاح
          const successEmbed = new EmbedBuilder()
            .setColor('#00ff00')
            .setTitle('✅ تم إضافة الرتبة بنجاح!')
            .addFields(
              {
                name: '👤 العضو',
                value: `${targetUser}`,
                inline: true
              },
              {
                name: '🎭 الرتبة',
                value: `${targetRole}`,
                inline: true
              },
              {
                name: '👮 بواسطة',
                value: `${message.author}`,
                inline: true
              }
            )
            .setFooter({
              text: 'CS Discord Moderation System',
              iconURL: message.client.user.displayAvatarURL()
            })
            .setTimestamp();
          
          await message.reply({ embeds: [successEmbed] });
          
        } else {
          // حذف الرتبة
          if (!targetUser.roles.cache.has(targetRole.id)) {
            return await message.reply(`❌ ${targetUser.user.username} لا يملك الرتبة ${targetRole.name}!`);
          }
          
          await targetUser.roles.remove(targetRole, `Role removed by ${message.author.tag}`);
          
          // إنشاء embed النجاح
          const successEmbed = new EmbedBuilder()
            .setColor('#ff6b6b')
            .setTitle('✅ تم حذف الرتبة بنجاح!')
            .addFields(
              {
                name: '👤 العضو',
                value: `${targetUser}`,
                inline: true
              },
              {
                name: '🎭 الرتبة',
                value: `${targetRole}`,
                inline: true
              },
              {
                name: '👮 بواسطة',
                value: `${message.author}`,
                inline: true
              }
            )
            .setFooter({
              text: 'CS Discord Moderation System',
              iconURL: message.client.user.displayAvatarURL()
            })
            .setTimestamp();
          
          await message.reply({ embeds: [successEmbed] });
        }
        
        // إرسال لوج إذا كان مفعل
        const logChannel = message.guild.channels.cache.find(ch => ch.name === 'mod-logs');
        if (logChannel) {
          const logEmbed = new EmbedBuilder()
            .setColor(isAdd ? '#00ff00' : '#ff6b6b')
            .setTitle(`📝 سجل الإدارة - ${isAdd ? 'إضافة' : 'حذف'} رتبة`)
            .addFields(
              {
                name: 'المشرف',
                value: `${message.author.tag} (${message.author.id})`,
                inline: true
              },
              {
                name: 'العضو',
                value: `${targetUser.user.tag} (${targetUser.id})`,
                inline: true
              },
              {
                name: 'الرتبة',
                value: `${targetRole.name} (${targetRole.id})`,
                inline: true
              },
              {
                name: 'العملية',
                value: isAdd ? 'إضافة رتبة' : 'حذف رتبة',
                inline: true
              }
            )
            .setTimestamp();
          
          await logChannel.send({ embeds: [logEmbed] });
        }
        
        // إرسال رسالة خاصة للعضو (اختياري)
        try {
          const dmEmbed = new EmbedBuilder()
            .setColor(isAdd ? '#00ff00' : '#ff6b6b')
            .setTitle(`🎭 تحديث الرتبة في ${message.guild.name}`)
            .setDescription(isAdd ? 
              `تم إضافة رتبة **${targetRole.name}** إليك!` : 
              `تم حذف رتبة **${targetRole.name}** منك!`)
            .addFields({
              name: 'بواسطة',
              value: message.author.tag,
              inline: true
            })
            .setTimestamp();
          
          await targetUser.send({ embeds: [dmEmbed] });
        } catch (error) {
          // تجاهل خطأ الرسائل الخاصة إذا كانت مغلقة
        }
        
      } catch (error) {
        console.error('خطأ في إدارة الرتبة:', error);
        
        let errorMessage = '❌ حدث خطأ أثناء إدارة الرتبة!';
        
        if (error.code === 50013) {
          errorMessage = '❌ ليس لدي صلاحية كافية لإدارة هذه الرتبة!';
        } else if (error.code === 50001) {
          errorMessage = '❌ ليس لدي وصول لهذا العضو أو الرتبة!';
        }
        
        await message.reply(errorMessage);
      }
      
    } catch (error) {
      console.error('خطأ في أمر إدارة الرتب:', error);
      await message.reply('❌ حدث خطأ أثناء تنفيذ الأمر!');
    }
  }
};
