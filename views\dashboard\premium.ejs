<%- include('../partials/header') %>

<div class="main-content">
    <div class="content-header">
        <h1 class="page-title">
            <i class="fas fa-crown"></i>
            إدارة Premium
        </h1>
        <p class="page-description">
            منح وإدارة اشتراكات Premium للأعضاء
        </p>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="stats-grid">
        <div class="stat-card premium">
            <div class="stat-number" id="total-premium">0</div>
            <div class="stat-label">مستخدمين Premium</div>
        </div>
        <div class="stat-card active">
            <div class="stat-number" id="active-premium">0</div>
            <div class="stat-label">نشط حالياً</div>
        </div>
        <div class="stat-card expiring">
            <div class="stat-number" id="expiring-soon">0</div>
            <div class="stat-label">ينتهي قريباً</div>
        </div>
        <div class="stat-card revenue">
            <div class="stat-number" id="total-revenue">$0</div>
            <div class="stat-label">إجمالي الإيرادات</div>
        </div>
    </div>

    <!-- أدوات سريعة -->
    <div class="quick-actions">
        <button class="btn btn-success" onclick="openGrantModal()">
            <i class="fas fa-plus"></i>
            منح Premium
        </button>
        <button class="btn btn-info" onclick="openBulkModal()">
            <i class="fas fa-users"></i>
            منح جماعي
        </button>
        <button class="btn btn-warning" onclick="checkExpiring()">
            <i class="fas fa-clock"></i>
            فحص المنتهية
        </button>
        <button class="btn btn-secondary" onclick="exportData()">
            <i class="fas fa-download"></i>
            تصدير البيانات
        </button>
    </div>

    <!-- بطاقات الإدارة -->
    <div class="cards-grid">
        <!-- منح Premium -->
        <div class="card premium-card">
            <div class="card-header">
                <div class="card-icon">
                    <i class="fas fa-crown"></i>
                </div>
                <div>
                    <h3 class="card-title">منح Premium</h3>
                </div>
            </div>
            <div class="card-description">
                منح اشتراك Premium لعضو معين
            </div>
            <div class="premium-types">
                <div class="premium-type" data-type="basic">
                    <div class="type-icon">🥉</div>
                    <div class="type-name">Basic</div>
                    <div class="type-price">مجاني</div>
                </div>
                <div class="premium-type" data-type="premium">
                    <div class="type-icon">🥈</div>
                    <div class="type-name">Premium</div>
                    <div class="type-price">$5/شهر</div>
                </div>
                <div class="premium-type" data-type="vip">
                    <div class="type-icon">🥇</div>
                    <div class="type-name">VIP</div>
                    <div class="type-price">$10/شهر</div>
                </div>
                <div class="premium-type" data-type="ultimate">
                    <div class="type-icon">💎</div>
                    <div class="type-name">Ultimate</div>
                    <div class="type-price">$20/شهر</div>
                </div>
            </div>
        </div>

        <!-- إعدادات البوت المخصص -->
        <div class="card">
            <div class="card-header">
                <div class="card-icon">
                    <i class="fas fa-robot"></i>
                </div>
                <div>
                    <h3 class="card-title">البوت المخصص</h3>
                </div>
            </div>
            <div class="card-description">
                تخصيص اسم وصورة البوت للأعضاء المميزين
            </div>
            <div class="card-actions">
                <button class="btn btn-primary" onclick="openBotCustomization()">
                    <i class="fas fa-edit"></i>
                    تخصيص البوت
                </button>
            </div>
        </div>

        <!-- إدارة الميزات -->
        <div class="card">
            <div class="card-header">
                <div class="card-icon">
                    <i class="fas fa-cogs"></i>
                </div>
                <div>
                    <h3 class="card-title">إدارة الميزات</h3>
                </div>
            </div>
            <div class="card-description">
                تفعيل وإلغاء ميزات Premium للأعضاء
            </div>
            <div class="card-actions">
                <button class="btn btn-primary" onclick="openFeaturesManager()">
                    <i class="fas fa-sliders-h"></i>
                    إدارة الميزات
                </button>
            </div>
        </div>

        <!-- تقارير الاستخدام -->
        <div class="card">
            <div class="card-header">
                <div class="card-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div>
                    <h3 class="card-title">تقارير الاستخدام</h3>
                </div>
            </div>
            <div class="card-description">
                عرض إحصائيات استخدام ميزات Premium
            </div>
            <div class="card-actions">
                <button class="btn btn-primary" onclick="showUsageReports()">
                    <i class="fas fa-chart-bar"></i>
                    عرض التقارير
                </button>
            </div>
        </div>
    </div>

    <!-- جدول المستخدمين Premium -->
    <div class="card">
        <div class="card-header">
            <div class="card-icon">
                <i class="fas fa-users-crown"></i>
            </div>
            <div>
                <h3 class="card-title">مستخدمين Premium</h3>
            </div>
            <div class="card-actions">
                <button class="btn btn-sm btn-primary" onclick="refreshPremiumList()">
                    <i class="fas fa-sync"></i>
                    تحديث
                </button>
            </div>
        </div>
        <div class="card-description">
            <div class="table-responsive">
                <table class="table" id="premium-table">
                    <thead>
                        <tr>
                            <th>المستخدم</th>
                            <th>النوع</th>
                            <th>الحالة</th>
                            <th>تاريخ الانتهاء</th>
                            <th>الأيام المتبقية</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td colspan="6" class="text-center">جاري التحميل...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Modal منح Premium -->
<div class="modal fade" id="grantPremiumModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">👑 منح Premium</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="grantPremiumForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">معرف المستخدم</label>
                                <input type="text" class="form-control" id="userId" placeholder="123456789" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">نوع Premium</label>
                                <select class="form-control" id="premiumType" required>
                                    <option value="basic">🥉 Basic - مجاني</option>
                                    <option value="premium">🥈 Premium - $5/شهر</option>
                                    <option value="vip">🥇 VIP - $10/شهر</option>
                                    <option value="ultimate">💎 Ultimate - $20/شهر</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">المدة (بالأيام)</label>
                                <input type="number" class="form-control" id="duration" value="30" min="1" max="365" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">السبب</label>
                                <input type="text" class="form-control" id="reason" placeholder="سبب منح Premium">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">ملاحظات إضافية</label>
                        <textarea class="form-control" id="notes" rows="3" placeholder="ملاحظات للمشرفين..."></textarea>
                    </div>
                    
                    <div class="premium-preview">
                        <h6>الميزات المتضمنة:</h6>
                        <div id="featuresPreview"></div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" onclick="grantPremium()">
                    <i class="fas fa-crown"></i>
                    منح Premium
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal تخصيص البوت -->
<div class="modal fade" id="botCustomizationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">🤖 تخصيص البوت</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="botCustomizationForm">
                    <div class="mb-3">
                        <label class="form-label">اسم البوت</label>
                        <input type="text" class="form-control" id="botName" placeholder="اسم البوت المخصص">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">صورة البوت (URL)</label>
                        <input type="url" class="form-control" id="botAvatar" placeholder="https://example.com/avatar.png">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">حالة البوت</label>
                        <select class="form-control" id="botStatus">
                            <option value="online">🟢 متصل</option>
                            <option value="idle">🟡 خامل</option>
                            <option value="dnd">🔴 مشغول</option>
                            <option value="invisible">⚫ غير مرئي</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">نشاط البوت</label>
                        <input type="text" class="form-control" id="botActivity" placeholder="يلعب مع الأعضاء">
                    </div>
                    
                    <div class="bot-preview">
                        <h6>معاينة:</h6>
                        <div class="bot-preview-card">
                            <img id="previewAvatar" src="/static/images/bot-logo.svg" alt="Bot Avatar">
                            <div class="bot-info">
                                <div class="bot-name" id="previewName">CS Discord Bot</div>
                                <div class="bot-status" id="previewStatus">🟢 متصل</div>
                                <div class="bot-activity" id="previewActivity">يساعد الأعضاء</div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveBotCustomization()">
                    <i class="fas fa-save"></i>
                    حفظ التخصيص
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 25px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.stat-card.premium {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #333;
}

.stat-card.active {
    background: linear-gradient(135deg, #00ff88, #00d4aa);
    color: white;
}

.stat-card.expiring {
    background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
    color: white;
}

.stat-card.revenue {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 10px;
}

.stat-label {
    font-size: 1rem;
    opacity: 0.9;
}

.quick-actions {
    display: flex;
    gap: 15px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.premium-types {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.premium-type {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 15px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.premium-type:hover {
    transform: translateY(-3px);
    border-color: rgba(255, 255, 255, 0.3);
}

.premium-type.selected {
    border-color: #ffd700;
    background: rgba(255, 215, 0, 0.2);
}

.type-icon {
    font-size: 2rem;
    margin-bottom: 10px;
}

.type-name {
    font-weight: bold;
    margin-bottom: 5px;
}

.type-price {
    font-size: 0.9rem;
    opacity: 0.8;
}

.premium-preview {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 15px;
    margin-top: 15px;
}

.bot-preview-card {
    display: flex;
    align-items: center;
    gap: 15px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 15px;
}

.bot-preview-card img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
}

.bot-info {
    flex: 1;
}

.bot-name {
    font-weight: bold;
    font-size: 1.1rem;
    margin-bottom: 5px;
}

.bot-status, .bot-activity {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-bottom: 3px;
}

.premium-card {
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.2), rgba(255, 193, 7, 0.1));
    border: 2px solid rgba(255, 215, 0, 0.3);
}

@media (max-width: 768px) {
    .quick-actions {
        flex-direction: column;
    }
    
    .premium-types {
        grid-template-columns: repeat(2, 1fr);
    }
}
</style>

<script src="/static/js/premium.js"></script>

<%- include('../partials/footer') %>
