// ========================================
// 🔊 أمر نقل العضو الصوتي - CS Discord
// ========================================
// 📺 يوتيوب: https://www.youtube.com/@CS_Discord
// 💬 ديسكورد: https://discord.gg/yqTn2EwVsd
// ========================================

const { EmbedBuilder, PermissionFlagsBits, ChannelType } = require('discord.js');

module.exports = {
  name: 'move',
  aliases: ['نقل', 'mv', 'voice_move'],
  description: 'نقل عضو إلى روم صوتي آخر',
  usage: '<@المستخدم> <#الروم_الصوتي>',
  category: 'moderation',
  permissions: [PermissionFlagsBits.MoveMembers],
  cooldown: 3,
  
  async execute(message, args) {
    try {
      // التحقق من الصلاحيات
      if (!message.member.permissions.has(PermissionFlagsBits.MoveMembers)) {
        return await message.reply('❌ ليس لديك صلاحية نقل الأعضاء!');
      }
      
      // التحقق من صلاحيات البوت
      if (!message.guild.members.me.permissions.has(PermissionFlagsBits.MoveMembers)) {
        return await message.reply('❌ البوت لا يملك صلاحية نقل الأعضاء!');
      }
      
      // التحقق من وجود المعاملات
      if (args.length < 2) {
        return await message.reply('❌ استخدام خاطئ!\nالاستخدام: `move <@المستخدم> <#الروم_الصوتي>`\nمثال: `move @أحمد #General`');
      }
      
      // تحديد المستخدم
      const targetMember = message.mentions.members.first();
      if (!targetMember) {
        return await message.reply('❌ يجب ذكر المستخدم المراد نقله!\nمثال: `move @أحمد #General`');
      }
      
      // تحديد الروم الصوتي
      const targetChannel = message.mentions.channels.first();
      if (!targetChannel) {
        return await message.reply('❌ يجب ذكر الروم الصوتي!\nمثال: `move @أحمد #General`');
      }
      
      // التحقق من نوع الروم
      if (targetChannel.type !== ChannelType.GuildVoice) {
        return await message.reply('❌ الروم المذكور ليس روم صوتي!');
      }
      
      // التحقق من وجود العضو في روم صوتي
      if (!targetMember.voice.channel) {
        return await message.reply(`❌ ${targetMember.user.username} ليس في أي روم صوتي حالياً!`);
      }
      
      // التحقق من أن العضو ليس في نفس الروم المطلوب
      if (targetMember.voice.channel.id === targetChannel.id) {
        return await message.reply(`❌ ${targetMember.user.username} موجود بالفعل في ${targetChannel.name}!`);
      }
      
      // التحقق من صلاحيات الوصول للروم الهدف
      if (!targetChannel.permissionsFor(targetMember).has(PermissionFlagsBits.Connect)) {
        return await message.reply(`❌ ${targetMember.user.username} لا يملك صلاحية الدخول إلى ${targetChannel.name}!`);
      }
      
      // حفظ معلومات الروم الأصلي
      const originalChannel = targetMember.voice.channel;
      
      try {
        // نقل العضو
        await targetMember.voice.setChannel(targetChannel, `Moved by ${message.author.tag}`);
        
        // إنشاء embed النجاح
        const successEmbed = new EmbedBuilder()
          .setColor('#00ff00')
          .setTitle('🔊 تم نقل العضو بنجاح!')
          .addFields(
            {
              name: '👤 العضو',
              value: `${targetMember}`,
              inline: true
            },
            {
              name: '📤 من',
              value: `🔊 ${originalChannel.name}`,
              inline: true
            },
            {
              name: '📥 إلى',
              value: `🔊 ${targetChannel.name}`,
              inline: true
            },
            {
              name: '👮 بواسطة',
              value: `${message.author}`,
              inline: true
            },
            {
              name: '⏰ الوقت',
              value: `<t:${Math.floor(Date.now() / 1000)}:R>`,
              inline: true
            }
          )
          .setFooter({
            text: 'CS Discord Moderation System',
            iconURL: message.client.user.displayAvatarURL()
          })
          .setTimestamp();
        
        await message.reply({ embeds: [successEmbed] });
        
        // إرسال لوج إذا كان مفعل
        const logChannel = message.guild.channels.cache.find(ch => ch.name === 'mod-logs');
        if (logChannel) {
          const logEmbed = new EmbedBuilder()
            .setColor('#0099ff')
            .setTitle('📝 سجل الإدارة - نقل عضو صوتي')
            .addFields(
              {
                name: 'المشرف',
                value: `${message.author.tag} (${message.author.id})`,
                inline: true
              },
              {
                name: 'العضو المنقول',
                value: `${targetMember.user.tag} (${targetMember.id})`,
                inline: true
              },
              {
                name: 'من الروم',
                value: `${originalChannel.name} (${originalChannel.id})`,
                inline: true
              },
              {
                name: 'إلى الروم',
                value: `${targetChannel.name} (${targetChannel.id})`,
                inline: true
              }
            )
            .setTimestamp();
          
          await logChannel.send({ embeds: [logEmbed] });
        }
        
        // إرسال رسالة خاصة للعضو (اختياري)
        try {
          const dmEmbed = new EmbedBuilder()
            .setColor('#0099ff')
            .setTitle(`🔊 تم نقلك في ${message.guild.name}`)
            .setDescription(`تم نقلك من **${originalChannel.name}** إلى **${targetChannel.name}**`)
            .addFields({
              name: 'بواسطة',
              value: message.author.tag,
              inline: true
            })
            .setTimestamp();
          
          await targetMember.send({ embeds: [dmEmbed] });
        } catch (error) {
          // تجاهل خطأ الرسائل الخاصة إذا كانت مغلقة
        }
        
      } catch (error) {
        console.error('خطأ في نقل العضو:', error);
        
        let errorMessage = '❌ حدث خطأ أثناء نقل العضو!';
        
        if (error.code === 50013) {
          errorMessage = '❌ ليس لدي صلاحية كافية لنقل هذا العضو!';
        } else if (error.code === 50001) {
          errorMessage = '❌ ليس لدي وصول لهذا العضو أو الروم!';
        } else if (error.code === 40032) {
          errorMessage = '❌ الروم الصوتي ممتلئ!';
        }
        
        await message.reply(errorMessage);
      }
      
    } catch (error) {
      console.error('خطأ في أمر نقل العضو:', error);
      await message.reply('❌ حدث خطأ أثناء تنفيذ الأمر!');
    }
  }
};
