<%- include('../partials/header') %>

<div class="main-content">
    <div class="content-header">
        <h1 class="page-title">
            <i class="fas fa-code"></i>
            منشئ الأوامر المخصصة
        </h1>
        <p class="page-description">
            أنشئ أوامر مخصصة لبوتك بسهولة باستخدام واجهة السحب والإفلات
        </p>
    </div>

    <!-- شريط الأدوات -->
    <div class="toolbar">
        <div class="toolbar-section">
            <button class="btn btn-success" onclick="saveCommand()">
                <i class="fas fa-save"></i>
                حفظ الأمر
            </button>
            <button class="btn btn-primary" onclick="testCommand()">
                <i class="fas fa-play"></i>
                اختبار الأمر
            </button>
            <button class="btn btn-secondary" onclick="clearWorkspace()">
                <i class="fas fa-trash"></i>
                مسح الكل
            </button>
        </div>
        <div class="toolbar-section">
            <input type="text" id="commandName" class="form-control" placeholder="اسم الأمر" style="width: 200px;">
            <input type="text" id="commandDescription" class="form-control" placeholder="وصف الأمر" style="width: 250px;">
        </div>
    </div>

    <div class="command-builder-container">
        <!-- قائمة البلوكات -->
        <div class="blocks-palette">
            <h3>🧩 البلوكات المتاحة</h3>
            
            <!-- بلوكات الأحداث -->
            <div class="block-category">
                <h4>🎯 الأحداث</h4>
                <div class="block-item" draggable="true" data-type="trigger" data-subtype="command">
                    <i class="fas fa-play"></i>
                    عند تشغيل الأمر
                </div>
                <div class="block-item" draggable="true" data-type="trigger" data-subtype="mention">
                    <i class="fas fa-at"></i>
                    عند ذكر مستخدم
                </div>
            </div>

            <!-- بلوكات الرسائل -->
            <div class="block-category">
                <h4>💬 الرسائل</h4>
                <div class="block-item" draggable="true" data-type="action" data-subtype="send_message">
                    <i class="fas fa-comment"></i>
                    إرسال رسالة
                </div>
                <div class="block-item" draggable="true" data-type="action" data-subtype="send_embed">
                    <i class="fas fa-window-maximize"></i>
                    إرسال Embed
                </div>
                <div class="block-item" draggable="true" data-type="action" data-subtype="reply">
                    <i class="fas fa-reply"></i>
                    الرد على الرسالة
                </div>
            </div>

            <!-- بلوكات الشروط -->
            <div class="block-category">
                <h4>🔀 الشروط</h4>
                <div class="block-item" draggable="true" data-type="condition" data-subtype="if">
                    <i class="fas fa-question"></i>
                    إذا كان
                </div>
                <div class="block-item" draggable="true" data-type="condition" data-subtype="has_role">
                    <i class="fas fa-user-tag"></i>
                    إذا كان لديه رتبة
                </div>
                <div class="block-item" draggable="true" data-type="condition" data-subtype="has_permission">
                    <i class="fas fa-key"></i>
                    إذا كان لديه صلاحية
                </div>
            </div>

            <!-- بلوكات الإجراءات -->
            <div class="block-category">
                <h4>⚡ الإجراءات</h4>
                <div class="block-item" draggable="true" data-type="action" data-subtype="add_role">
                    <i class="fas fa-plus"></i>
                    إضافة رتبة
                </div>
                <div class="block-item" draggable="true" data-type="action" data-subtype="remove_role">
                    <i class="fas fa-minus"></i>
                    حذف رتبة
                </div>
                <div class="block-item" draggable="true" data-type="action" data-subtype="kick">
                    <i class="fas fa-door-open"></i>
                    طرد العضو
                </div>
                <div class="block-item" draggable="true" data-type="action" data-subtype="ban">
                    <i class="fas fa-hammer"></i>
                    حظر العضو
                </div>
            </div>

            <!-- بلوكات المتغيرات -->
            <div class="block-category">
                <h4>📊 المتغيرات</h4>
                <div class="block-item" draggable="true" data-type="variable" data-subtype="user">
                    <i class="fas fa-user"></i>
                    المستخدم
                </div>
                <div class="block-item" draggable="true" data-type="variable" data-subtype="channel">
                    <i class="fas fa-hashtag"></i>
                    الروم
                </div>
                <div class="block-item" draggable="true" data-type="variable" data-subtype="server">
                    <i class="fas fa-server"></i>
                    الخادم
                </div>
                <div class="block-item" draggable="true" data-type="variable" data-subtype="random">
                    <i class="fas fa-dice"></i>
                    رقم عشوائي
                </div>
            </div>

            <!-- بلوكات الوقت -->
            <div class="block-category">
                <h4>⏰ الوقت</h4>
                <div class="block-item" draggable="true" data-type="action" data-subtype="wait">
                    <i class="fas fa-clock"></i>
                    انتظار
                </div>
                <div class="block-item" draggable="true" data-type="variable" data-subtype="current_time">
                    <i class="fas fa-calendar"></i>
                    الوقت الحالي
                </div>
            </div>
        </div>

        <!-- منطقة العمل -->
        <div class="workspace">
            <h3>🎨 منطقة العمل</h3>
            <div class="workspace-area" id="workspace">
                <div class="workspace-placeholder">
                    <i class="fas fa-mouse-pointer"></i>
                    <p>اسحب البلوكات هنا لبناء أمرك المخصص</p>
                </div>
            </div>
        </div>

        <!-- معاينة الكود -->
        <div class="code-preview">
            <h3>👁️ معاينة الكود</h3>
            <div class="code-container">
                <pre id="generatedCode">// سيتم إنشاء الكود هنا تلقائياً
// اسحب البلوكات لبناء الأمر</pre>
            </div>
            <div class="code-actions">
                <button class="btn btn-info" onclick="copyCode()">
                    <i class="fas fa-copy"></i>
                    نسخ الكود
                </button>
                <button class="btn btn-warning" onclick="downloadCode()">
                    <i class="fas fa-download"></i>
                    تحميل الملف
                </button>
            </div>
        </div>
    </div>

    <!-- قائمة الأوامر المحفوظة -->
    <div class="saved-commands">
        <h3>💾 الأوامر المحفوظة</h3>
        <div class="commands-grid" id="savedCommands">
            <!-- سيتم تحميل الأوامر هنا -->
        </div>
    </div>
</div>

<!-- Modal إعدادات البلوك -->
<div class="modal fade" id="blockSettingsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">⚙️ إعدادات البلوك</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="blockSettingsBody">
                <!-- سيتم إنشاء الإعدادات هنا -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveBlockSettings()">حفظ</button>
            </div>
        </div>
    </div>
</div>

<style>
.command-builder-container {
    display: grid;
    grid-template-columns: 300px 1fr 350px;
    gap: 20px;
    height: 600px;
    margin-bottom: 30px;
}

.blocks-palette {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 15px;
    padding: 20px;
    overflow-y: auto;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.block-category {
    margin-bottom: 20px;
}

.block-category h4 {
    color: #667eea;
    margin-bottom: 10px;
    font-size: 0.9rem;
}

.block-item {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 10px;
    margin: 5px 0;
    border-radius: 8px;
    cursor: grab;
    transition: all 0.3s ease;
    font-size: 0.85rem;
}

.block-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.block-item:active {
    cursor: grabbing;
}

.workspace {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 15px;
    padding: 20px;
    border: 2px dashed rgba(255, 255, 255, 0.2);
}

.workspace-area {
    min-height: 500px;
    position: relative;
}

.workspace-placeholder {
    text-align: center;
    color: rgba(255, 255, 255, 0.5);
    margin-top: 200px;
}

.workspace-placeholder i {
    font-size: 3rem;
    margin-bottom: 10px;
}

.code-preview {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 15px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.code-container {
    background: #1a1a1a;
    border-radius: 8px;
    padding: 15px;
    margin: 10px 0;
    height: 400px;
    overflow-y: auto;
}

.code-container pre {
    color: #00ff00;
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
    margin: 0;
}

.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(0, 0, 0, 0.2);
    padding: 15px 20px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.toolbar-section {
    display: flex;
    gap: 10px;
    align-items: center;
}

.saved-commands {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 15px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.commands-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.command-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    padding: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.command-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.dropped-block {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 15px;
    margin: 10px 0;
    border-radius: 10px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    cursor: pointer;
    position: relative;
}

.dropped-block:hover {
    border-color: rgba(255, 255, 255, 0.5);
}

.block-settings-btn {
    position: absolute;
    top: 5px;
    right: 5px;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    width: 25px;
    height: 25px;
    color: white;
    cursor: pointer;
}

.block-delete-btn {
    position: absolute;
    top: 5px;
    right: 35px;
    background: rgba(255, 0, 0, 0.7);
    border: none;
    border-radius: 50%;
    width: 25px;
    height: 25px;
    color: white;
    cursor: pointer;
}

@media (max-width: 1200px) {
    .command-builder-container {
        grid-template-columns: 1fr;
        height: auto;
    }
    
    .blocks-palette, .workspace, .code-preview {
        height: 400px;
    }
}
</style>

<script src="/static/js/command-builder.js"></script>

<%- include('../partials/footer') %>
