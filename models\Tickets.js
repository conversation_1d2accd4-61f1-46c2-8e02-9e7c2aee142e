const mongoose = require('mongoose');

const ticketsSchema = new mongoose.Schema({
  guildId: {
    type: String,
    required: true,
    index: true
  },

  // معلومات التذكرة
  ticketId: {
    type: Number,
    required: true
  },

  channelId: {
    type: String,
    required: true,
    unique: true
  },

  // معلومات منشئ التذكرة
  creator: {
    id: {
      type: String,
      required: true
    },
    username: String,
    discriminator: String,
    avatar: String
  },

  // موضوع التذكرة
  subject: {
    type: String,
    default: 'طلب دعم عام'
  },

  // وصف المشكلة
  description: String,

  // حالة التذكرة
  status: {
    type: String,
    default: 'open',
    enum: ['open', 'pending', 'resolved', 'closed']
  },

  // أولوية التذكرة
  priority: {
    type: String,
    default: 'medium',
    enum: ['low', 'medium', 'high', 'urgent']
  },

  // تصنيف التذكرة
  category: {
    type: String,
    default: 'general',
    enum: ['general', 'technical', 'billing', 'report', 'suggestion', 'other']
  },

  // الموظفون المعينون
  assignedStaff: [{
    id: String,
    username: String,
    assignedAt: {
      type: Date,
      default: Date.now
    }
  }],

  // المشاركون في التذكرة
  participants: [{
    id: String,
    username: String,
    joinedAt: {
      type: Date,
      default: Date.now
    }
  }],

  // الرسائل (للنسخة الاحتياطية)
  messages: [{
    messageId: String,
    authorId: String,
    authorUsername: String,
    content: String,
    attachments: [String],
    timestamp: Date,
    edited: Boolean,
    editedAt: Date
  }],

  // معلومات الإغلاق
  closedBy: {
    id: String,
    username: String,
    reason: String,
    closedAt: Date
  },

  // تقييم الخدمة
  rating: {
    score: {
      type: Number,
      min: 1,
      max: 5
    },
    feedback: String,
    ratedAt: Date
  },

  // الملفات المرفقة
  attachments: [{
    filename: String,
    url: String,
    size: Number,
    uploadedBy: String,
    uploadedAt: {
      type: Date,
      default: Date.now
    }
  }],

  // العلامات
  tags: [String],

  // ملاحظات الموظفين
  staffNotes: [{
    authorId: String,
    authorUsername: String,
    note: String,
    createdAt: {
      type: Date,
      default: Date.now
    }
  }],

  // إعدادات التذكرة
  settings: {
    autoClose: {
      enabled: {
        type: Boolean,
        default: true
      },
      afterHours: {
        type: Number,
        default: 24
      }
    },
    saveTranscript: {
      type: Boolean,
      default: true
    },
    notifyOnMessage: {
      type: Boolean,
      default: true
    }
  },

  // تاريخ آخر نشاط
  lastActivity: {
    type: Date,
    default: Date.now
  },

  // تاريخ الإغلاق المتوقع
  expectedCloseDate: Date,

  // معلومات إضافية
  metadata: {
    source: {
      type: String,
      default: 'discord',
      enum: ['discord', 'dashboard', 'api']
    },
    ip: String,
    userAgent: String,
    referrer: String
  }
}, {
  timestamps: true,
  versionKey: false
});

// إنشاء فهارس للبحث السريع
ticketsSchema.index({ guildId: 1, ticketId: 1 }, { unique: true });
ticketsSchema.index({ guildId: 1, 'creator.id': 1 });
ticketsSchema.index({ guildId: 1, status: 1 });
ticketsSchema.index({ guildId: 1, priority: 1 });
ticketsSchema.index({ guildId: 1, category: 1 });
ticketsSchema.index({ channelId: 1 });
ticketsSchema.index({ lastActivity: 1 });
ticketsSchema.index({ createdAt: 1 });

// إنشاء معرف فريد للتذكرة تلقائياً
ticketsSchema.pre('save', async function(next) {
  if (this.isNew) {
    const lastTicket = await this.constructor.findOne(
      { guildId: this.guildId },
      {},
      { sort: { ticketId: -1 } }
    );
    this.ticketId = lastTicket ? lastTicket.ticketId + 1 : 1;
  }
  next();
});

// تحديث آخر نشاط عند إضافة رسالة
ticketsSchema.methods.addMessage = function(messageData) {
  this.messages.push(messageData);
  this.lastActivity = new Date();
  return this.save();
};

// دالة للحصول على التذاكر المفتوحة للعضو
ticketsSchema.statics.getOpenTicketsForUser = function(guildId, userId) {
  return this.find({
    guildId: guildId,
    'creator.id': userId,
    status: { $in: ['open', 'pending'] }
  });
};

// دالة للحصول على إحصائيات التذاكر
ticketsSchema.statics.getStats = function(guildId, days = 30) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  return this.aggregate([
    {
      $match: {
        guildId: guildId,
        createdAt: { $gte: startDate }
      }
    },
    {
      $group: {
        _id: null,
        total: { $sum: 1 },
        open: {
          $sum: {
            $cond: [{ $eq: ['$status', 'open'] }, 1, 0]
          }
        },
        closed: {
          $sum: {
            $cond: [{ $eq: ['$status', 'closed'] }, 1, 0]
          }
        },
        avgRating: { $avg: '$rating.score' }
      }
    }
  ]);
};

// دالة للحصول على التذاكر التي تحتاج إغلاق تلقائي
ticketsSchema.statics.getTicketsForAutoClose = function() {
  const cutoffDate = new Date();
  cutoffDate.setHours(cutoffDate.getHours() - 24); // 24 ساعة

  return this.find({
    status: { $in: ['open', 'pending'] },
    lastActivity: { $lt: cutoffDate },
    'settings.autoClose.enabled': true
  });
};

module.exports = mongoose.model('Tickets', ticketsSchema);
