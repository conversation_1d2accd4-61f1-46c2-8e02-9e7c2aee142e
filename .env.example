# ========================================
# 🤖 إعدادات لوحة تحكم بوت ديسكورد
# ========================================
# 
# 📺 يوتيوب: https://www.youtube.com/@CS_Discord
# 💬 ديسكورد: https://discord.gg/yqTn2EwVsd
# 
# ⚠️  تحذير: لا تشارك هذا الملف مع أي شخص!
# يحتوي على معلومات حساسة وسرية
# ========================================

# ========================================
# 🤖 إعدادات البوت الأساسية
# ========================================

# توكن البوت من Discord Developer Portal
# كيفية الحصول عليه:
# 1. اذهب إلى https://discord.com/developers/applications
# 2. اختر تطبيقك أو أنشئ واحد جديد
# 3. اذهب إلى قسم "Bot"
# 4. انسخ التوكن من "Token"
BOT_TOKEN=your_bot_token_here

# معرف التطبيق (Client ID)
# من نفس الصفحة في Discord Developer Portal
# قسم "General Information" -> "Application ID"
CLIENT_ID=your_client_id_here

# سر التطبيق (Client Secret)
# من قسم "General Information" -> "Client Secret"
# اضغط "Reset Secret" إذا لم تحفظه من قبل
CLIENT_SECRET=your_client_secret_here

# البريفكس الافتراضي للأوامر
# يمكن تغييره لاحقاً من لوحة التحكم
PREFIX=!

# معرف مالك البوت (Discord User ID)
# كيفية الحصول عليه:
# 1. فعّل Developer Mode في Discord
# 2. اضغط بالزر الأيمن على اسمك
# 3. اختر "Copy User ID"
OWNER_ID=your_discord_user_id

# معرف خادم الدعم (اختياري)
# معرف الخادم الذي سيستخدم للدعم الفني
SUPPORT_SERVER_ID=your_support_server_id

# ========================================
# 🗄️ إعدادات قاعدة البيانات
# ========================================

# رابط الاتصال بقاعدة بيانات MongoDB
# 
# الخيار 1: قاعدة بيانات محلية بدون مصادقة (الأسهل للتطوير)
MONGO_URI=mongodb://localhost:27017/discord-bot-dashboard
#
# الخيار 2: قاعدة بيانات محلية مع مصادقة
# MONGO_URI=*****************************************************************
#
# الخيار 3: MongoDB Atlas (السحابي - مجاني)
# MONGO_URI=mongodb+srv://username:<EMAIL>/discord-bot-dashboard
#
# الخيار 4: قاعدة بيانات محلية مع إعدادات متقدمة
# MONGO_URI=mongodb://localhost:27017/discord-bot-dashboard?authSource=admin
#
# ملاحظة: إذا كنت تستخدم MongoDB محلي بدون مصادقة، استخدم الخيار 1

# ========================================
# 🌐 إعدادات الخادم
# ========================================

# المنفذ الذي سيعمل عليه الخادم
# القيمة الافتراضية: 3000
PORT=3000

# عنوان الخادم
# للاستخدام المحلي: localhost
# للاستخدام العام: 0.0.0.0
HOST=localhost

# الرابط الأساسي للتطبيق
# يجب تغييره عند النشر على خادم حقيقي
BASE_URL=http://localhost:3000

# بيئة التشغيل
# development = للتطوير (يظهر تفاصيل الأخطاء)
# production = للإنتاج (يخفي تفاصيل الأخطاء)
NODE_ENV=development

# ========================================
# 🔒 إعدادات الأمان
# ========================================

# مفتاح سري للجلسات
# يجب أن يكون نص عشوائي طويل ومعقد
# لا تستخدم النص الافتراضي في الإنتاج!
SESSION_SECRET=your-super-secret-session-key-here

# مفتاح سري لـ JWT
# يستخدم لتشفير رموز المصادقة
# يجب أن يكون مختلف عن SESSION_SECRET
JWT_SECRET=your-jwt-secret-key

# ========================================
# 🔐 إعدادات OAuth2 (تسجيل الدخول)
# ========================================

# رابط إعادة التوجيه بعد تسجيل الدخول
# يجب إضافته في Discord Developer Portal
# قسم "OAuth2" -> "Redirects"
REDIRECT_URI=http://localhost:3000/auth/discord/callback

# ========================================
# 🛠️ إعدادات التطوير
# ========================================

# مستوى تسجيل الأحداث
# error = الأخطاء فقط
# warn = التحذيرات والأخطاء
# info = معلومات عامة (افتراضي)
# debug = تفاصيل مفصلة للتطوير
LOG_LEVEL=info

# تفعيل البيانات التجريبية
# true = يضيف بيانات تجريبية للاختبار
# false = يستخدم البيانات الحقيقية فقط
ENABLE_MOCK_DATA=false

# ========================================
# 📋 ملاحظات مهمة
# ========================================
#
# 1. احرص على عدم مشاركة هذا الملف مع أي شخص
# 2. لا ترفع هذا الملف على GitHub أو أي منصة عامة
# 3. استخدم كلمات مرور قوية ومعقدة
# 4. غيّر المفاتيح السرية بانتظام
# 5. استخدم HTTPS في الإنتاج
#
# للحصول على الدعم:
# 📺 يوتيوب: https://www.youtube.com/@CS_Discord
# 💬 ديسكورد: https://discord.gg/yqTn2EwVsd
#
# ========================================

# ========================================
# 🔧 حلول مشاكل قاعدة البيانات الشائعة
# ========================================
#
# مشكلة "Authentication failed":
# - تأكد من صحة اسم المستخدم وكلمة المرور
# - أو استخدم قاعدة بيانات بدون مصادقة للتطوير
#
# مشكلة "Connection refused":
# - تأكد من تشغيل MongoDB على جهازك
# - أو استخدم MongoDB Atlas (مجاني)
#
# للحصول على MongoDB Atlas مجاناً:
# 1. اذهب إلى https://www.mongodb.com/atlas
# 2. أنشئ حساب مجاني
# 3. أنشئ cluster مجاني
# 4. احصل على connection string
# 5. استبدل MONGO_URI بالرابط الجديد
#
# ========================================
