const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const dashboardUsersSchema = new mongoose.Schema({
  // معلومات المستخدم من ديسكورد
  discordId: {
    type: String,
    required: true,
    unique: true,
    index: true
  },

  username: {
    type: String,
    required: true
  },

  discriminator: String,

  email: String,

  avatar: String,

  // معلومات الحساب
  account: {
    isVerified: {
      type: Boolean,
      default: false
    },
    verificationToken: String,
    verificationExpires: Date,
    
    // كلمة مرور إضافية (اختيارية)
    password: String,
    
    // إعادة تعيين كلمة المرور
    resetPasswordToken: String,
    resetPasswordExpires: Date,
    
    // تسجيل الدخول بعاملين
    twoFactorEnabled: {
      type: Boolean,
      default: false
    },
    twoFactorSecret: String,
    backupCodes: [String]
  },

  // الصلاحيات العامة
  permissions: {
    // صلاحيات النظام
    system: {
      isAdmin: {
        type: Boolean,
        default: false
      },
      isModerator: {
        type: Boolean,
        default: false
      },
      canAccessLogs: {
        type: Boolean,
        default: false
      },
      canManageUsers: {
        type: Boolean,
        default: false
      },
      canViewAnalytics: {
        type: Boolean,
        default: false
      }
    },

    // صلاحيات الخوادم
    guilds: [{
      guildId: String,
      guildName: String,
      permissions: {
        owner: {
          type: Boolean,
          default: false
        },
        admin: {
          type: Boolean,
          default: false
        },
        moderator: {
          type: Boolean,
          default: false
        },
        canManageSettings: {
          type: Boolean,
          default: false
        },
        canManageModeration: {
          type: Boolean,
          default: false
        },
        canManageTickets: {
          type: Boolean,
          default: false
        },
        canManageAutoResponses: {
          type: Boolean,
          default: false
        },
        canManageLeveling: {
          type: Boolean,
          default: false
        },
        canViewLogs: {
          type: Boolean,
          default: false
        },
        canCreateBackups: {
          type: Boolean,
          default: false
        }
      },
      addedAt: {
        type: Date,
        default: Date.now
      },
      addedBy: String
    }]
  },

  // إعدادات المستخدم
  preferences: {
    language: {
      type: String,
      default: 'ar',
      enum: ['ar', 'en']
    },
    theme: {
      type: String,
      default: 'dark',
      enum: ['light', 'dark', 'auto']
    },
    timezone: {
      type: String,
      default: 'Asia/Riyadh'
    },
    dateFormat: {
      type: String,
      default: 'DD/MM/YYYY',
      enum: ['DD/MM/YYYY', 'MM/DD/YYYY', 'YYYY-MM-DD']
    },
    timeFormat: {
      type: String,
      default: '24h',
      enum: ['12h', '24h']
    },
    notifications: {
      email: {
        type: Boolean,
        default: true
      },
      discord: {
        type: Boolean,
        default: true
      },
      browser: {
        type: Boolean,
        default: true
      }
    },
    dashboard: {
      itemsPerPage: {
        type: Number,
        default: 10,
        min: 5,
        max: 100
      },
      defaultView: {
        type: String,
        default: 'cards',
        enum: ['cards', 'table', 'list']
      },
      showTutorials: {
        type: Boolean,
        default: true
      }
    }
  },

  // إحصائيات الاستخدام
  usage: {
    totalLogins: {
      type: Number,
      default: 0
    },
    lastLogin: Date,
    lastActivity: Date,
    totalTimeSpent: {
      type: Number,
      default: 0 // بالدقائق
    },
    sessionsThisMonth: {
      type: Number,
      default: 0
    },
    actionsPerformed: {
      settingsChanged: {
        type: Number,
        default: 0
      },
      moderationActions: {
        type: Number,
        default: 0
      },
      ticketsManaged: {
        type: Number,
        default: 0
      },
      backupsCreated: {
        type: Number,
        default: 0
      }
    }
  },

  // سجل النشاطات
  activityLog: [{
    action: String,
    details: String,
    guildId: String,
    ip: String,
    userAgent: String,
    timestamp: {
      type: Date,
      default: Date.now
    }
  }],

  // الجلسات النشطة
  sessions: [{
    sessionId: String,
    ip: String,
    userAgent: String,
    createdAt: {
      type: Date,
      default: Date.now
    },
    lastActivity: {
      type: Date,
      default: Date.now
    },
    isActive: {
      type: Boolean,
      default: true
    }
  }],

  // معلومات الأمان
  security: {
    lastPasswordChange: Date,
    failedLoginAttempts: {
      type: Number,
      default: 0
    },
    lockedUntil: Date,
    trustedIPs: [String],
    suspiciousActivity: [{
      type: String,
      details: String,
      ip: String,
      timestamp: {
        type: Date,
        default: Date.now
      }
    }]
  },

  // حالة الحساب
  status: {
    type: String,
    default: 'active',
    enum: ['active', 'suspended', 'banned', 'pending']
  },

  // ملاحظات الإدارة
  adminNotes: [{
    note: String,
    addedBy: String,
    addedAt: {
      type: Date,
      default: Date.now
    }
  }],

  // معلومات إضافية
  metadata: {
    registrationIP: String,
    registrationUserAgent: String,
    referrer: String,
    source: {
      type: String,
      default: 'discord',
      enum: ['discord', 'invite', 'direct']
    }
  }
}, {
  timestamps: true,
  versionKey: false
});

// إنشاء فهارس للبحث السريع
dashboardUsersSchema.index({ discordId: 1 }, { unique: true });
dashboardUsersSchema.index({ email: 1 });
dashboardUsersSchema.index({ username: 1 });
dashboardUsersSchema.index({ 'permissions.guilds.guildId': 1 });
dashboardUsersSchema.index({ status: 1 });
dashboardUsersSchema.index({ 'usage.lastLogin': 1 });
dashboardUsersSchema.index({ createdAt: 1 });

// تشفير كلمة المرور قبل الحفظ
dashboardUsersSchema.pre('save', async function(next) {
  if (!this.isModified('account.password') || !this.account.password) {
    return next();
  }
  
  try {
    const salt = await bcrypt.genSalt(12);
    this.account.password = await bcrypt.hash(this.account.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// مقارنة كلمة المرور
dashboardUsersSchema.methods.comparePassword = async function(candidatePassword) {
  if (!this.account.password) {
    return false;
  }
  return bcrypt.compare(candidatePassword, this.account.password);
};

// التحقق من صلاحيات الخادم
dashboardUsersSchema.methods.hasGuildPermission = function(guildId, permission) {
  const guild = this.permissions.guilds.find(g => g.guildId === guildId);
  if (!guild) return false;
  
  // المالك لديه جميع الصلاحيات
  if (guild.permissions.owner) return true;
  
  // الأدمن لديه معظم الصلاحيات
  if (guild.permissions.admin && permission !== 'owner') return true;
  
  return guild.permissions[permission] || false;
};

// إضافة صلاحيات خادم
dashboardUsersSchema.methods.addGuildPermissions = function(guildId, guildName, permissions, addedBy) {
  const existingGuild = this.permissions.guilds.find(g => g.guildId === guildId);
  
  if (existingGuild) {
    // تحديث الصلاحيات الموجودة
    Object.assign(existingGuild.permissions, permissions);
  } else {
    // إضافة خادم جديد
    this.permissions.guilds.push({
      guildId,
      guildName,
      permissions,
      addedBy
    });
  }
  
  return this.save();
};

// تسجيل نشاط
dashboardUsersSchema.methods.logActivity = function(action, details, guildId, req) {
  this.activityLog.push({
    action,
    details,
    guildId,
    ip: req?.ip || req?.connection?.remoteAddress,
    userAgent: req?.get('User-Agent')
  });
  
  // الاحتفاظ بآخر 1000 نشاط فقط
  if (this.activityLog.length > 1000) {
    this.activityLog = this.activityLog.slice(-1000);
  }
  
  this.usage.lastActivity = new Date();
  return this.save();
};

// تحديث إحصائيات تسجيل الدخول
dashboardUsersSchema.methods.updateLoginStats = function(req) {
  this.usage.totalLogins += 1;
  this.usage.lastLogin = new Date();
  this.usage.lastActivity = new Date();
  
  // إضافة جلسة جديدة
  const sessionId = require('crypto').randomBytes(32).toString('hex');
  this.sessions.push({
    sessionId,
    ip: req?.ip || req?.connection?.remoteAddress,
    userAgent: req?.get('User-Agent')
  });
  
  // إزالة الجلسات القديمة (أكثر من 30 يوم)
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
  this.sessions = this.sessions.filter(session => session.createdAt > thirtyDaysAgo);
  
  return this.save();
};

// الحصول على المستخدمين حسب الخادم
dashboardUsersSchema.statics.getUsersByGuild = function(guildId) {
  return this.find({
    'permissions.guilds.guildId': guildId,
    status: 'active'
  }).select('discordId username avatar permissions.guilds.$');
};

// البحث عن المستخدمين
dashboardUsersSchema.statics.searchUsers = function(query, limit = 20) {
  const searchRegex = new RegExp(query, 'i');
  
  return this.find({
    $or: [
      { username: searchRegex },
      { email: searchRegex },
      { discordId: query }
    ],
    status: { $ne: 'banned' }
  })
  .limit(limit)
  .select('discordId username avatar email status');
};

module.exports = mongoose.model('DashboardUsers', dashboardUsersSchema);
