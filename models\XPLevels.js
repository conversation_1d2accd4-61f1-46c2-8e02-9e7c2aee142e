const mongoose = require('mongoose');

const xpLevelsSchema = new mongoose.Schema({
  guildId: {
    type: String,
    required: true,
    index: true
  },

  userId: {
    type: String,
    required: true,
    index: true
  },

  // معلومات المستخدم (للعرض)
  userInfo: {
    username: String,
    discriminator: String,
    avatar: String,
    displayName: String
  },

  // نقاط الخبرة
  xp: {
    total: {
      type: Number,
      default: 0,
      min: 0
    },
    current: {
      type: Number,
      default: 0,
      min: 0
    }, // النقاط في المستوى الحالي
    daily: {
      type: Number,
      default: 0,
      min: 0
    },
    weekly: {
      type: Number,
      default: 0,
      min: 0
    },
    monthly: {
      type: Number,
      default: 0,
      min: 0
    }
  },

  // المستوى
  level: {
    type: Number,
    default: 0,
    min: 0
  },

  // إحصائيات النشاط
  activity: {
    totalMessages: {
      type: Number,
      default: 0
    },
    messagesThisWeek: {
      type: Number,
      default: 0
    },
    messagesThisMonth: {
      type: Number,
      default: 0
    },
    voiceMinutes: {
      type: Number,
      default: 0
    },
    voiceMinutesThisWeek: {
      type: Number,
      default: 0
    },
    voiceMinutesThisMonth: {
      type: Number,
      default: 0
    },
    reactionsGiven: {
      type: Number,
      default: 0
    },
    reactionsReceived: {
      type: Number,
      default: 0
    }
  },

  // تواريخ مهمة
  dates: {
    lastMessageAt: Date,
    lastXpGain: Date,
    lastLevelUp: Date,
    firstMessageAt: Date,
    lastVoiceActivity: Date,
    weeklyReset: Date,
    monthlyReset: Date
  },

  // المكافآت المستلمة
  rewards: {
    claimed: [{
      level: Number,
      roleId: String,
      claimedAt: {
        type: Date,
        default: Date.now
      },
      type: {
        type: String,
        enum: ['role', 'currency', 'item', 'badge'],
        default: 'role'
      }
    }],
    pending: [{
      level: Number,
      roleId: String,
      type: {
        type: String,
        enum: ['role', 'currency', 'item', 'badge'],
        default: 'role'
      }
    }]
  },

  // الإنجازات
  achievements: [{
    id: String,
    name: String,
    description: String,
    unlockedAt: {
      type: Date,
      default: Date.now
    },
    rarity: {
      type: String,
      enum: ['common', 'rare', 'epic', 'legendary'],
      default: 'common'
    }
  }],

  // إعدادات شخصية
  settings: {
    showProfile: {
      type: Boolean,
      default: true
    },
    showInLeaderboard: {
      type: Boolean,
      default: true
    },
    dmOnLevelUp: {
      type: Boolean,
      default: false
    },
    preferredCardColor: {
      type: String,
      default: '#7289da'
    },
    customBackground: String
  },

  // إحصائيات متقدمة
  stats: {
    averageXpPerMessage: {
      type: Number,
      default: 0
    },
    longestStreak: {
      days: {
        type: Number,
        default: 0
      },
      startDate: Date,
      endDate: Date
    },
    currentStreak: {
      days: {
        type: Number,
        default: 0
      },
      startDate: Date
    },
    topChannels: [{
      channelId: String,
      channelName: String,
      messageCount: Number,
      xpGained: Number
    }],
    weeklyRank: Number,
    monthlyRank: Number,
    allTimeRank: Number
  },

  // معلومات إضافية
  metadata: {
    isBot: {
      type: Boolean,
      default: false
    },
    isBanned: {
      type: Boolean,
      default: false
    },
    isOptedOut: {
      type: Boolean,
      default: false
    },
    multiplier: {
      type: Number,
      default: 1.0,
      min: 0,
      max: 10
    }, // مضاعف XP مخصص
    notes: String
  }
}, {
  timestamps: true,
  versionKey: false
});

// إنشاء فهارس للبحث السريع
xpLevelsSchema.index({ guildId: 1, userId: 1 }, { unique: true });
xpLevelsSchema.index({ guildId: 1, level: -1 });
xpLevelsSchema.index({ guildId: 1, 'xp.total': -1 });
xpLevelsSchema.index({ guildId: 1, 'xp.weekly': -1 });
xpLevelsSchema.index({ guildId: 1, 'xp.monthly': -1 });
xpLevelsSchema.index({ guildId: 1, 'activity.totalMessages': -1 });
xpLevelsSchema.index({ 'dates.lastMessageAt': 1 });

// حساب المستوى المطلوب للنقاط
xpLevelsSchema.statics.calculateLevel = function(totalXp) {
  // معادلة المستوى: level = floor(sqrt(totalXp / 100))
  return Math.floor(Math.sqrt(totalXp / 100));
};

// حساب النقاط المطلوبة للمستوى
xpLevelsSchema.statics.calculateXpForLevel = function(level) {
  // معادلة النقاط: xp = level^2 * 100
  return Math.pow(level, 2) * 100;
};

// حساب النقاط المطلوبة للمستوى التالي
xpLevelsSchema.methods.getXpForNextLevel = function() {
  const nextLevel = this.level + 1;
  return this.constructor.calculateXpForLevel(nextLevel);
};

// إضافة نقاط خبرة
xpLevelsSchema.methods.addXp = function(amount, source = 'message') {
  const oldLevel = this.level;
  
  // إضافة النقاط
  this.xp.total += amount;
  this.xp.daily += amount;
  this.xp.weekly += amount;
  this.xp.monthly += amount;
  
  // حساب المستوى الجديد
  const newLevel = this.constructor.calculateLevel(this.xp.total);
  const leveledUp = newLevel > oldLevel;
  
  if (leveledUp) {
    this.level = newLevel;
    this.dates.lastLevelUp = new Date();
  }
  
  // حساب النقاط الحالية في المستوى
  const xpForCurrentLevel = this.constructor.calculateXpForLevel(this.level);
  this.xp.current = this.xp.total - xpForCurrentLevel;
  
  // تحديث تاريخ آخر نشاط
  this.dates.lastXpGain = new Date();
  
  // تحديث الإحصائيات
  if (source === 'message') {
    this.activity.totalMessages += 1;
    this.activity.messagesThisWeek += 1;
    this.activity.messagesThisMonth += 1;
    this.dates.lastMessageAt = new Date();
    
    if (!this.dates.firstMessageAt) {
      this.dates.firstMessageAt = new Date();
    }
  }
  
  // حساب متوسط النقاط لكل رسالة
  if (this.activity.totalMessages > 0) {
    this.stats.averageXpPerMessage = this.xp.total / this.activity.totalMessages;
  }
  
  return { leveledUp, oldLevel, newLevel: this.level };
};

// الحصول على لوحة المتصدرين
xpLevelsSchema.statics.getLeaderboard = function(guildId, type = 'total', limit = 10, page = 1) {
  const skip = (page - 1) * limit;
  let sortField;
  
  switch (type) {
    case 'weekly':
      sortField = { 'xp.weekly': -1 };
      break;
    case 'monthly':
      sortField = { 'xp.monthly': -1 };
      break;
    case 'level':
      sortField = { level: -1, 'xp.total': -1 };
      break;
    default:
      sortField = { 'xp.total': -1 };
  }
  
  return this.find({
    guildId: guildId,
    'settings.showInLeaderboard': true,
    'metadata.isBot': false,
    'metadata.isBanned': false
  })
  .sort(sortField)
  .skip(skip)
  .limit(limit);
};

// الحصول على ترتيب المستخدم
xpLevelsSchema.methods.getRank = function(type = 'total') {
  let sortField;
  
  switch (type) {
    case 'weekly':
      sortField = { 'xp.weekly': -1 };
      break;
    case 'monthly':
      sortField = { 'xp.monthly': -1 };
      break;
    case 'level':
      sortField = { level: -1, 'xp.total': -1 };
      break;
    default:
      sortField = { 'xp.total': -1 };
  }
  
  return this.constructor.countDocuments({
    guildId: this.guildId,
    'settings.showInLeaderboard': true,
    'metadata.isBot': false,
    'metadata.isBanned': false,
    $or: [
      { [Object.keys(sortField)[0]]: { $gt: this[Object.keys(sortField)[0].split('.').join('.')] } },
      {
        [Object.keys(sortField)[0]]: this[Object.keys(sortField)[0].split('.').join('.')],
        _id: { $lt: this._id }
      }
    ]
  }).then(count => count + 1);
};

// إعادة تعيين الإحصائيات الأسبوعية/الشهرية
xpLevelsSchema.statics.resetPeriodic = function(guildId, type = 'weekly') {
  const update = {};
  const dateField = type === 'weekly' ? 'weeklyReset' : 'monthlyReset';
  
  if (type === 'weekly') {
    update['xp.weekly'] = 0;
    update['activity.messagesThisWeek'] = 0;
    update['activity.voiceMinutesThisWeek'] = 0;
  } else {
    update['xp.monthly'] = 0;
    update['activity.messagesThisMonth'] = 0;
    update['activity.voiceMinutesThisMonth'] = 0;
  }
  
  update[`dates.${dateField}`] = new Date();
  
  return this.updateMany({ guildId: guildId }, { $set: update });
};

module.exports = mongoose.model('XPLevels', xpLevelsSchema);
