# 🚀 دليل البدء السريع

## 📋 خطوات سريعة للتشغيل

### 1. تثبيت المكتبات
```bash
npm install
```

### 2. تعديل الإعدادات
افتح ملف `config.js` وعدّل:
- `bot.token` - توكن البوت
- `bot.clientId` - معرف التطبيق
- `oauth.clientSecret` - سر التطبيق
- `bot.ownerId` - معرف المالك
- `database.mongoUri` - رابط قاعدة البيانات

### 3. تشغيل المشروع
```bash
# للويندوز
start.bat

# للليونكس/ماك  
./start.sh

# أو مباشرة
npm start
```

### 4. فتح اللوحة
```
http://localhost:3000
```

## 🔧 إعدادات مهمة في config.js

```javascript
// إعدادات البوت
bot: {
  token: 'ضع_توكن_البوت_هنا',
  clientId: 'ضع_معرف_التطبيق_هنا',
  ownerId: 'ضع_معرف_المالك_هنا'
},

// إعدادات قاعدة البيانات
database: {
  mongoUri: 'ضع_رابط_قاعدة_البيانات_هنا'
},

// إعدادات OAuth2
oauth: {
  clientSecret: 'ضع_سر_التطبيق_هنا'
}
```

## 📞 الدعم

📺 **يوتيوب**: https://www.youtube.com/@CS_Discord  
💬 **ديسكورد**: https://discord.gg/yqTn2EwVsd

---
**تطوير: CS Discord** ❤️
