// ========================================
// 🛡️ ملف الإعدادات المحمي - CS Discord
// ========================================
// 📺 يوتيوب: https://www.youtube.com/@CS_Discord
// 💬 ديسكورد: https://discord.gg/yqTn2EwVsd
// 
// ⚠️ تحذير: لا تشارك هذا الملف مع أي شخص!
// ========================================

// حماية ضد التعديل
const CS_PROTECTION_HASH = 'cs-discord-2024-protected-credentials';

// التحقق من سلامة النظام
function validateSystemIntegrity() {
  const requiredHash = 'cs-discord-2024-protected-credentials';
  if (CS_PROTECTION_HASH !== requiredHash) {
    console.error('🚨 تم اكتشاف تعديل في ملف الإعدادات المحمي!');
    process.exit(1);
  }
}

// تشفير بسيط للإعدادات (لإخفائها عن العيون)
function decodeCredential(encoded) {
  return Buffer.from(encoded, 'base64').toString('utf-8');
}

// الإعدادات المشفرة (مخفية عن العيون)
const ENCRYPTED_CREDENTIALS = {
  // توكن البوت (مشفر)
  botToken: 'TVRNNE1UazFNakl6TlRnMU1UYzBOek0wT0E.GvAHbj.HwjrInpTqp_WBPPA8-MhEcwHTEbI_YKdH134U4',
  
  // معرف التطبيق (مشفر)
  clientId: 'MTM4MTk1MjIzNTg1MTc0NzM0OA==',
  
  // سر التطبيق (مشفر) 
  clientSecret: 'M2JxZ19LZXZTX0VZWktNby02b20zdE1uQklFcVItd3M=',
  
  // معرف المالك (مشفر)
  ownerId: 'NzYxMjE4NDA0ODMzMDM0MjUx',
  
  // رابط قاعدة البيانات (مشفر)
  mongoUri: '****************************************************************************************************',
  
  // مفتاح الجلسة (مشفر)
  sessionSecret: 'ZGhzZGdvcGZta2hrbHNmb2doa3NvcHJ0aHA=',
  
  // مفتاح JWT (مشفر)
  jwtSecret: 'aHNmbGdocHNvZnBnb2hzZmdoaWtvc3BmZ2hvcw=='
};

// دالة فك التشفير الآمنة
function getSecureCredentials() {
  validateSystemIntegrity();
  
  try {
    return {
      botToken: ENCRYPTED_CREDENTIALS.botToken, // التوكن غير مشفر (طبيعي من Discord)
      clientId: decodeCredential(ENCRYPTED_CREDENTIALS.clientId),
      clientSecret: decodeCredential(ENCRYPTED_CREDENTIALS.clientSecret),
      ownerId: decodeCredential(ENCRYPTED_CREDENTIALS.ownerId),
      mongoUri: decodeCredential(ENCRYPTED_CREDENTIALS.mongoUri),
      sessionSecret: decodeCredential(ENCRYPTED_CREDENTIALS.sessionSecret),
      jwtSecret: decodeCredential(ENCRYPTED_CREDENTIALS.jwtSecret)
    };
  } catch (error) {
    console.error('🚨 خطأ في فك تشفير الإعدادات!');
    process.exit(1);
  }
}

// حماية إضافية ضد التلاعب
Object.freeze(ENCRYPTED_CREDENTIALS);
Object.freeze(CS_PROTECTION_HASH);

// حماية ضد تعديل الملف
const originalCredentials = JSON.stringify(ENCRYPTED_CREDENTIALS);

// التحقق من سلامة الإعدادات
function checkCredentialsIntegrity() {
  const currentCredentials = JSON.stringify(ENCRYPTED_CREDENTIALS);
  if (currentCredentials !== originalCredentials) {
    console.error('🚨 تم اكتشاف تعديل في الإعدادات المحمية!');
    console.error('📺 يوتيوب: https://www.youtube.com/@CS_Discord');
    console.error('💬 ديسكورد: https://discord.gg/yqTn2EwVsd');
    process.exit(1);
  }
}

// التحقق الدوري من سلامة الملف
setInterval(() => {
  validateSystemIntegrity();
  checkCredentialsIntegrity();
}, 30000); // كل 30 ثانية

// حماية ضد محاولات الوصول المباشر
const protectedExports = {
  getSecureCredentials: function() {
    validateSystemIntegrity();
    checkCredentialsIntegrity();
    return getSecureCredentials();
  },
  validateSystemIntegrity
};

// منع تعديل الـ exports
Object.freeze(protectedExports);

module.exports = protectedExports;
