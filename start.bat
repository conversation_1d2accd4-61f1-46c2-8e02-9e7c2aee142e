@echo off
chcp 65001 >nul
title لوحة تحكم بوت ديسكورد - CS Discord

echo.
echo ========================================
echo    🤖 لوحة تحكم بوت ديسكورد
echo    📺 CS Discord - يوتيوب
echo    💬 https://discord.gg/yqTn2EwVsd
echo ========================================
echo.

:: التحقق من وجود Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ خطأ: Node.js غير مثبت على النظام
    echo يرجى تحميل وتثبيت Node.js من: https://nodejs.org
    pause
    exit /b 1
)

:: التحقق من وجود ملف package.json
if not exist package.json (
    echo ❌ خطأ: ملف package.json غير موجود
    echo تأكد من أنك في المجلد الصحيح للمشروع
    pause
    exit /b 1
)

:: التحقق من وجود ملف config.js
if not exist config.js (
    echo ❌ خطأ: ملف config.js غير موجود
    echo تأكد من وجود ملف config.js في المجلد
    pause
    exit /b 1
)

echo ℹ️  ملاحظة: جميع الإعدادات موجودة في ملف config.js
echo 🔧 لتعديل الإعدادات، افتح ملف config.js
echo 📖 راجع ملف QUICK_START.md للتفاصيل

:: التحقق من وجود مجلد node_modules
if not exist node_modules (
    echo 📦 تثبيت المكتبات المطلوبة...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ خطأ في تثبيت المكتبات
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت المكتبات بنجاح
    echo.
)

:: عرض معلومات المشروع
echo 📋 معلومات المشروع:
echo    - الاسم: لوحة تحكم بوت ديسكورد
echo    - الإصدار: 1.0.0
echo    - المنفذ: 3000
echo    - الرابط: http://localhost:3000
echo.

:: بدء التشغيل
echo 🚀 بدء تشغيل البوت ولوحة التحكم...
echo 🤖 البوت سيتصل بـ Discord
echo 🌐 لوحة التحكم ستعمل على المنفذ 3000
echo 🛡️ حماية CS Discord مفعلة
echo ⚠️  يرجى عدم تعديل حقوق المطور
echo.
echo للإيقاف اضغط Ctrl+C
echo.

:: تشغيل التطبيق
npm start

:: في حالة الخروج
echo.
echo تم إيقاف الخادم
pause
