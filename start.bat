@echo off
chcp 65001 >nul
title لوحة تحكم بوت ديسكورد

echo.
echo ========================================
echo    🤖 لوحة تحكم بوت ديسكورد
echo ========================================
echo.

:: التحقق من وجود Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ خطأ: Node.js غير مثبت على النظام
    echo يرجى تحميل وتثبيت Node.js من: https://nodejs.org
    pause
    exit /b 1
)

:: التحقق من وجود ملف package.json
if not exist package.json (
    echo ❌ خطأ: ملف package.json غير موجود
    echo تأكد من أنك في المجلد الصحيح للمشروع
    pause
    exit /b 1
)

:: التحقق من وجود ملف .env
if not exist .env (
    echo ⚠️  تحذير: ملف .env غير موجود
    echo سيتم إنشاء ملف .env من القالب...
    if exist .env.example (
        copy .env.example .env >nul
        echo ✅ تم إنشاء ملف .env
        echo يرجى تعديل ملف .env وإضافة المعلومات المطلوبة
        echo.
        pause
    ) else (
        echo ❌ خطأ: ملف .env.example غير موجود
        pause
        exit /b 1
    )
)

:: التحقق من وجود مجلد node_modules
if not exist node_modules (
    echo 📦 تثبيت المكتبات المطلوبة...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ خطأ في تثبيت المكتبات
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت المكتبات بنجاح
    echo.
)

:: عرض معلومات المشروع
echo 📋 معلومات المشروع:
echo    - الاسم: لوحة تحكم بوت ديسكورد
echo    - الإصدار: 1.0.0
echo    - المنفذ: 3000
echo    - الرابط: http://localhost:3000
echo.

:: بدء التشغيل
echo 🚀 بدء تشغيل الخادم...
echo.
echo للإيقاف اضغط Ctrl+C
echo.

:: تشغيل التطبيق
npm start

:: في حالة الخروج
echo.
echo تم إيقاف الخادم
pause
