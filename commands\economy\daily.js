// ========================================
// 📅 أمر الراتب اليومي - CS Discord
// ========================================
// 📺 يوتيوب: https://www.youtube.com/@CS_Discord
// 💬 ديسكورد: https://discord.gg/yqTn2EwVsd
// ========================================

const { EmbedBuilder } = require('discord.js');
const Currency = require('../../models/Currency');

module.exports = {
  name: 'daily',
  aliases: ['راتب', 'يومي', 'مكافأة'],
  description: 'احصل على راتبك اليومي',
  usage: '',
  category: 'economy',
  cooldown: 5,
  
  async execute(message, args) {
    try {
      // الحصول على بيانات المستخدم
      const userCurrency = await Currency.getOrCreate(message.author.id, message.guild.id);
      
      // فحص إمكانية استلام الراتب
      if (!userCurrency.canClaimDaily()) {
        const nextClaim = getNextClaimTime(userCurrency.dailyReward.lastClaimed);
        
        const embed = new EmbedBuilder()
          .setColor('#ff6b6b')
          .setTitle('⏰ لا يمكنك استلام الراتب الآن!')
          .setDescription(`يمكنك استلام راتبك اليومي التالي خلال:\n**${nextClaim}**`)
          .addFields(
            {
              name: '📊 إحصائياتك',
              value: `🔥 التتالي الحالي: **${userCurrency.dailyReward.streak}** يوم\n📅 إجمالي الأيام: **${userCurrency.dailyReward.totalClaimed}** يوم`,
              inline: false
            }
          )
          .setFooter({
            text: 'CS Discord Economy System',
            iconURL: message.client.user.displayAvatarURL()
          })
          .setTimestamp();
        
        return await message.reply({ embeds: [embed] });
      }
      
      // استلام الراتب
      const result = userCurrency.claimDaily();
      await userCurrency.save();
      
      // تحديد لون الـ embed بناءً على التتالي
      let embedColor = '#00ff00';
      if (result.streak >= 30) embedColor = '#ffd700'; // ذهبي
      else if (result.streak >= 14) embedColor = '#9932cc'; // بنفسجي
      else if (result.streak >= 7) embedColor = '#ff69b4'; // وردي
      
      // إنشاء الـ embed
      const embed = new EmbedBuilder()
        .setColor(embedColor)
        .setTitle('🎉 تم استلام الراتب اليومي!')
        .setThumbnail(message.author.displayAvatarURL({ dynamic: true }))
        .addFields(
          {
            name: '💰 المبلغ المستلم',
            value: `**+${result.reward.toLocaleString('ar-EG')}** عملة`,
            inline: true
          },
          {
            name: '🔥 التتالي',
            value: `**${result.streak}** يوم`,
            inline: true
          },
          {
            name: '💵 رصيدك الجديد',
            value: `**${userCurrency.balance.toLocaleString('ar-EG')}** عملة`,
            inline: true
          }
        )
        .setFooter({
          text: 'CS Discord Economy System',
          iconURL: message.client.user.displayAvatarURL()
        })
        .setTimestamp();
      
      // إضافة رسائل تحفيزية بناءً على التتالي
      if (result.streak === 1) {
        embed.setDescription('🌟 مرحباً بك في نظام الرواتب! استمر في الحضور يومياً للحصول على مكافآت أكبر!');
      } else if (result.streak === 7) {
        embed.setDescription('🎊 رائع! أسبوع كامل من الحضور! مكافأتك تزداد كل يوم!');
      } else if (result.streak === 14) {
        embed.setDescription('🏆 مذهل! أسبوعان متتاليان! أنت مستخدم مميز!');
      } else if (result.streak === 30) {
        embed.setDescription('👑 أسطوري! شهر كامل من الحضور! أنت بطل حقيقي!');
      } else if (result.streak >= 50) {
        embed.setDescription('🌟 إنجاز خارق! أنت من النخبة المميزة في الخادم!');
      } else {
        embed.setDescription(`💪 استمر في التتالي! كل يوم إضافي يعني مكافأة أكبر!`);
      }
      
      // إضافة معلومات عن المكافآت القادمة
      const nextReward = calculateNextDayReward(result.streak);
      embed.addFields({
        name: '📈 مكافأة الغد',
        value: `**${nextReward.toLocaleString('ar-EG')}** عملة`,
        inline: true
      });
      
      // إضافة إنجازات خاصة
      const achievements = checkDailyAchievements(result.streak, userCurrency.dailyReward.totalClaimed);
      if (achievements.length > 0) {
        embed.addFields({
          name: '🏅 إنجازات جديدة!',
          value: achievements.join('\n'),
          inline: false
        });
      }
      
      await message.reply({ embeds: [embed] });
      
    } catch (error) {
      console.error('خطأ في أمر الراتب اليومي:', error);
      await message.reply('❌ حدث خطأ أثناء استلام الراتب اليومي!');
    }
  }
};

// دالة لحساب الوقت المتبقي للراتب التالي
function getNextClaimTime(lastClaimed) {
  const now = new Date();
  const nextClaim = new Date(lastClaimed);
  nextClaim.setDate(nextClaim.getDate() + 1);
  
  const diff = nextClaim - now;
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
  
  return `${hours} ساعة و ${minutes} دقيقة`;
}

// دالة لحساب مكافأة اليوم التالي
function calculateNextDayReward(currentStreak) {
  const baseReward = 100;
  const nextStreak = currentStreak + 1;
  const bonus = Math.min(nextStreak * 10, 500);
  return baseReward + bonus;
}

// دالة للتحقق من الإنجازات اليومية
function checkDailyAchievements(streak, totalClaimed) {
  const achievements = [];
  
  if (streak === 7) {
    achievements.push('🎯 **أسبوع ذهبي** - 7 أيام متتالية!');
  }
  
  if (streak === 14) {
    achievements.push('💎 **أسبوعان ماسيان** - 14 يوم متتالي!');
  }
  
  if (streak === 30) {
    achievements.push('👑 **الشهر الملكي** - 30 يوم متتالي!');
  }
  
  if (totalClaimed === 50) {
    achievements.push('🌟 **المحارب المخضرم** - 50 راتب إجمالي!');
  }
  
  if (totalClaimed === 100) {
    achievements.push('🏆 **الأسطورة** - 100 راتب إجمالي!');
  }
  
  return achievements;
}
