<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>500 - خطأ في الخادم</title>
    
    <!-- CSS Libraries -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #f04747 0%, #d32f2f 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #ffffff;
        }
        
        .error-container {
            text-align: center;
            max-width: 600px;
            padding: 40px;
        }
        
        .error-code {
            font-size: 8rem;
            font-weight: 700;
            color: rgba(255, 255, 255, 0.8);
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            margin-bottom: 20px;
            line-height: 1;
        }
        
        .error-title {
            font-size: 2.5rem;
            font-weight: 600;
            margin-bottom: 20px;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }
        
        .error-message {
            font-size: 1.2rem;
            margin-bottom: 40px;
            opacity: 0.9;
            line-height: 1.6;
        }
        
        .error-actions {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn-error {
            padding: 15px 30px;
            font-size: 1.1rem;
            font-weight: 600;
            border-radius: 50px;
            text-decoration: none;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }
        
        .btn-primary-error {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
        }
        
        .btn-primary-error:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
            color: white;
        }
        
        .btn-secondary-error {
            background: transparent;
            color: rgba(255, 255, 255, 0.8);
            border: 2px solid rgba(255, 255, 255, 0.3);
        }
        
        .btn-secondary-error:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            transform: translateY(-2px);
        }
        
        .error-icon {
            font-size: 4rem;
            margin-bottom: 30px;
            opacity: 0.7;
            animation: pulse 2s ease-in-out infinite;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 0.7; }
            50% { transform: scale(1.1); opacity: 1; }
        }
        
        .error-details {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            padding: 20px;
            margin: 30px 0;
            text-align: right;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .error-details h5 {
            color: #ffeb3b;
            margin-bottom: 15px;
            font-family: 'Cairo', sans-serif;
        }
        
        .floating-shapes {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            pointer-events: none;
        }
        
        .shape {
            position: absolute;
            opacity: 0.1;
            animation: floatShape 8s ease-in-out infinite;
        }
        
        .shape:nth-child(1) {
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }
        
        .shape:nth-child(2) {
            top: 20%;
            right: 10%;
            animation-delay: 2s;
        }
        
        .shape:nth-child(3) {
            bottom: 20%;
            left: 15%;
            animation-delay: 4s;
        }
        
        .shape:nth-child(4) {
            bottom: 10%;
            right: 20%;
            animation-delay: 6s;
        }
        
        @keyframes floatShape {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-30px) rotate(180deg); }
        }
        
        @media (max-width: 768px) {
            .error-code {
                font-size: 6rem;
            }
            
            .error-title {
                font-size: 2rem;
            }
            
            .error-message {
                font-size: 1rem;
            }
            
            .error-actions {
                flex-direction: column;
                align-items: center;
            }
            
            .btn-error {
                width: 100%;
                max-width: 300px;
                justify-content: center;
            }
            
            .error-details {
                font-size: 0.8rem;
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="floating-shapes">
        <i class="fas fa-exclamation-triangle shape" style="font-size: 3rem;"></i>
        <i class="fas fa-server shape" style="font-size: 2rem;"></i>
        <i class="fas fa-bug shape" style="font-size: 2.5rem;"></i>
        <i class="fas fa-tools shape" style="font-size: 2rem;"></i>
    </div>
    
    <div class="error-container">
        <div class="error-icon">
            <i class="fas fa-exclamation-triangle"></i>
        </div>
        
        <div class="error-code">500</div>
        
        <h1 class="error-title">خطأ في الخادم</h1>
        
        <p class="error-message">
            عذراً، حدث خطأ داخلي في الخادم. نحن نعمل على حل هذه المشكلة.
            يرجى المحاولة مرة أخرى لاحقاً أو التواصل مع الدعم الفني إذا استمرت المشكلة.
        </p>
        
        <% if (locals.error && locals.error.stack) { %>
        <div class="error-details">
            <h5><i class="fas fa-code"></i> تفاصيل الخطأ (للمطورين)</h5>
            <pre><%= error.stack %></pre>
        </div>
        <% } %>
        
        <div class="error-actions">
            <a href="javascript:history.back()" class="btn-error btn-primary-error">
                <i class="fas fa-arrow-right"></i>
                العودة للخلف
            </a>
            
            <a href="/" class="btn-error btn-secondary-error">
                <i class="fas fa-home"></i>
                الصفحة الرئيسية
            </a>
            
            <a href="javascript:location.reload()" class="btn-error btn-secondary-error">
                <i class="fas fa-redo"></i>
                إعادة المحاولة
            </a>
        </div>
        
        <div style="margin-top: 40px; opacity: 0.7;">
            <small>
                <i class="fas fa-clock"></i>
                وقت الخطأ: <%= new Date().toLocaleString('ar-SA') %>
            </small>
        </div>
    </div>
    
    <script>
        // إضافة تأثيرات تفاعلية
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير الحركة عند تحريك الماوس
            document.addEventListener('mousemove', function(e) {
                const shapes = document.querySelectorAll('.shape');
                const x = e.clientX / window.innerWidth;
                const y = e.clientY / window.innerHeight;
                
                shapes.forEach((shape, index) => {
                    const speed = (index + 1) * 0.5;
                    const xPos = (x - 0.5) * speed;
                    const yPos = (y - 0.5) * speed;
                    
                    shape.style.transform += ` translate(${xPos}px, ${yPos}px)`;
                });
            });
            
            // تأثير النقر على الأزرار
            document.querySelectorAll('.btn-error').forEach(button => {
                button.addEventListener('click', function(e) {
                    // إضافة تأثير النقر
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });
            
            // إرسال تقرير الخطأ تلقائياً (اختياري)
            if (window.location.hostname !== 'localhost') {
                setTimeout(() => {
                    // يمكن إضافة كود لإرسال تقرير الخطأ هنا
                    console.log('تم تسجيل الخطأ للمراجعة');
                }, 2000);
            }
        });
        
        // عداد تنازلي لإعادة المحاولة التلقائية
        let countdown = 30;
        const retryButton = document.querySelector('.btn-secondary-error[href="javascript:location.reload()"]');
        
        if (retryButton) {
            const originalText = retryButton.innerHTML;
            
            const countdownInterval = setInterval(() => {
                countdown--;
                retryButton.innerHTML = `<i class="fas fa-redo"></i> إعادة المحاولة (${countdown})`;
                
                if (countdown <= 0) {
                    clearInterval(countdownInterval);
                    retryButton.innerHTML = originalText;
                    // يمكن إضافة إعادة تحميل تلقائية هنا إذا رغبت
                    // location.reload();
                }
            }, 1000);
        }
    </script>
</body>
</html>
