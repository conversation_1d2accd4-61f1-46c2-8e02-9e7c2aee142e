// 🛡️ حماية حقوق CS Discord
// 📺 يوتيوب: https://www.youtube.com/@CS_Discord
// 💬 ديسكورد: https://discord.gg/yqTn2EwVsd

const { CSProtection } = require('../models');

// معلومات CS الأصلية (لا تعدل هذا!)
const CS_ORIGINAL_CREDITS = {
  youtube: 'https://www.youtube.com/@CS_Discord',
  discord: 'https://discord.gg/yqTn2EwVsd',
  author: 'CS Discord',
  hash: 'cs-discord-protection-2024'
};

// حماية ضد تعديل الحقوق في الكود
function validateCreditsIntegrity() {
  const currentCredits = {
    youtube: 'https://www.youtube.com/@CS_Discord',
    discord: 'https://discord.gg/yqTn2EwVsd',
    author: 'CS Discord'
  };

  return (
    currentCredits.youtube === CS_ORIGINAL_CREDITS.youtube &&
    currentCredits.discord === CS_ORIGINAL_CREDITS.discord &&
    currentCredits.author === CS_ORIGINAL_CREDITS.author
  );
}

// middleware للتحقق من سلامة الحقوق
const csProtectionMiddleware = async (req, res, next) => {
  try {
    // التحقق من سلامة الحقوق في الكود
    if (!validateCreditsIntegrity()) {
      console.error('🚨 تم اكتشاف تعديل في حقوق CS Discord في الكود!');
      return res.status(403).render('errors/cs-violation', {
        title: 'انتهاك حقوق المطور',
        message: 'تم اكتشاف تعديل في حقوق CS Discord',
        youtube: CS_ORIGINAL_CREDITS.youtube,
        discord: CS_ORIGINAL_CREDITS.discord
      });
    }

    // التحقق من قاعدة البيانات
    const isSystemValid = await CSProtection.validateSystem();
    if (!isSystemValid) {
      console.error('🚨 تم اكتشاف انتهاك في قاعدة البيانات!');
      return res.status(403).json({
        error: 'انتهاك حقوق المطور',
        message: 'تم اكتشاف تعديل في حقوق CS Discord',
        youtube: CS_ORIGINAL_CREDITS.youtube,
        discord: CS_ORIGINAL_CREDITS.discord,
        action: 'يرجى عدم تعديل أو حذف حقوق المطور'
      });
    }

    // إضافة معلومات الحماية للاستجابة
    res.locals.csProtection = {
      isActive: true,
      credits: CS_ORIGINAL_CREDITS,
      timestamp: Date.now()
    };

    next();
  } catch (error) {
    console.error('خطأ في نظام حماية CS:', error);
    
    // في حالة الخطأ، نعرض رسالة تحذيرية
    res.locals.csProtection = {
      isActive: false,
      credits: CS_ORIGINAL_CREDITS,
      error: true
    };
    
    next();
  }
};

// middleware للتحقق من محاولات تعديل الحقوق عبر الطلبات
const antiTamperingMiddleware = (req, res, next) => {
  // قائمة بالكلمات المشبوهة في الطلبات
  const suspiciousPatterns = [
    'remove-credits',
    'delete-credits',
    'modify-credits',
    'cs-bypass',
    'credit-removal',
    'hide-credits',
    'cs-discord-remove'
  ];

  // التحقق من URL
  const url = req.url.toLowerCase();
  const body = JSON.stringify(req.body || {}).toLowerCase();
  const query = JSON.stringify(req.query || {}).toLowerCase();

  const isSuspicious = suspiciousPatterns.some(pattern => 
    url.includes(pattern) || body.includes(pattern) || query.includes(pattern)
  );

  if (isSuspicious) {
    console.log('🚨 محاولة مشبوهة لتعديل الحقوق:', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      url: req.url,
      timestamp: new Date()
    });

    return res.status(403).json({
      error: 'محاولة غير مصرح بها',
      message: 'تم رصد محاولة تعديل حقوق CS Discord',
      youtube: CS_ORIGINAL_CREDITS.youtube,
      discord: CS_ORIGINAL_CREDITS.discord,
      warning: 'هذه المحاولة تم تسجيلها'
    });
  }

  next();
};

// middleware لحقن حقوق CS في جميع الاستجابات
const injectCreditsMiddleware = (req, res, next) => {
  const originalRender = res.render;
  const originalJson = res.json;

  // تعديل دالة render لإضافة الحقوق
  res.render = function(view, locals = {}, callback) {
    locals.csCredits = locals.csCredits || CS_ORIGINAL_CREDITS;
    locals.csProtected = true;
    return originalRender.call(this, view, locals, callback);
  };

  // تعديل دالة json لإضافة الحقوق
  res.json = function(obj) {
    if (typeof obj === 'object' && obj !== null) {
      obj._csCredits = CS_ORIGINAL_CREDITS;
      obj._csProtected = true;
    }
    return originalJson.call(this, obj);
  };

  next();
};

// دالة للتحقق الدوري من سلامة النظام
const startPeriodicCheck = () => {
  setInterval(async () => {
    try {
      const isValid = await CSProtection.validateSystem();
      if (!isValid) {
        console.error('🚨 فحص دوري: تم اكتشاف انتهاك في النظام!');
      }
    } catch (error) {
      console.error('خطأ في الفحص الدوري:', error);
    }
  }, 5 * 60 * 1000); // كل 5 دقائق
};

// دالة لتسجيل محاولة انتهاك
const logViolationAttempt = async (type, details, req) => {
  try {
    const protection = await CSProtection.findOne({}) || new CSProtection({});
    await protection.logViolation(type, details, 'high');
    
    console.log('📝 تم تسجيل محاولة انتهاك:', {
      type,
      details,
      ip: req?.ip,
      userAgent: req?.get('User-Agent'),
      timestamp: new Date()
    });
  } catch (error) {
    console.error('خطأ في تسجيل الانتهاك:', error);
  }
};

module.exports = {
  csProtectionMiddleware,
  antiTamperingMiddleware,
  injectCreditsMiddleware,
  startPeriodicCheck,
  logViolationAttempt,
  CS_ORIGINAL_CREDITS
};
