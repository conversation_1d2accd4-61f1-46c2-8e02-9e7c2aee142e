// ========================================
// 😀 أمر تحويل النص لإيموجي - CS Discord
// ========================================
// 📺 يوتيوب: https://www.youtube.com/@CS_Discord
// 💬 ديسكورد: https://discord.gg/yqTn2EwVsd
// ========================================

const { EmbedBuilder } = require('discord.js');

module.exports = {
  name: 'emoji',
  aliases: ['emojify', 'ايموجي', 'رموز'],
  description: 'تحويل النص إلى رموز إيموجي',
  usage: '<النص>',
  category: 'fun',
  cooldown: 3,
  
  async execute(message, args) {
    try {
      // التحقق من وجود النص
      if (!args.length) {
        return await message.reply('❌ يجب كتابة النص المراد تحويله!\nمثال: `emoji hello world`');
      }
      
      // دمج النص
      const text = args.join(' ').toLowerCase();
      
      // التحقق من طول النص
      if (text.length > 100) {
        return await message.reply('❌ النص طويل جداً! الحد الأقصى 100 حرف.');
      }
      
      // تحويل النص لإيموجي
      const emojiText = convertToEmoji(text);
      
      // تحويل النص لأرقام إيموجي
      const numberEmoji = convertNumbersToEmoji(text);
      
      // تحويل النص لرموز خاصة
      const specialEmoji = convertToSpecialEmoji(text);
      
      // إنشاء الـ embed
      const embed = new EmbedBuilder()
        .setColor('#ffff00')
        .setTitle('😀 تحويل النص لإيموجي')
        .addFields(
          {
            name: '📝 النص الأصلي',
            value: `\`\`\`${args.join(' ')}\`\`\``,
            inline: false
          },
          {
            name: '🔤 أحرف إيموجي',
            value: emojiText || 'لا يمكن تحويل هذا النص',
            inline: false
          }
        )
        .setFooter({
          text: 'CS Discord Fun Commands',
          iconURL: message.client.user.displayAvatarURL()
        })
        .setTimestamp();
      
      // إضافة الأرقام إذا وُجدت
      if (numberEmoji !== text) {
        embed.addFields({
          name: '🔢 أرقام إيموجي',
          value: numberEmoji,
          inline: false
        });
      }
      
      // إضافة الرموز الخاصة إذا وُجدت
      if (specialEmoji !== text) {
        embed.addFields({
          name: '⭐ رموز خاصة',
          value: specialEmoji,
          inline: false
        });
      }
      
      // إضافة نصائح
      embed.addFields({
        name: '💡 نصائح',
        value: '• يمكنك نسخ الإيموجي واستخدامه!\n• جرب كلمات مختلفة للحصول على رموز مختلفة!\n• الأرقام والأحرف الإنجليزية تعمل بشكل أفضل!',
        inline: false
      });
      
      await message.reply({ embeds: [embed] });
      
      // إضافة ردود فعل
      const reactions = ['😀', '🔤', '⭐'];
      for (const reaction of reactions) {
        try {
          await message.react(reaction);
          await new Promise(resolve => setTimeout(resolve, 300));
        } catch (error) {
          // تجاهل خطأ الردود
        }
      }
      
    } catch (error) {
      console.error('خطأ في أمر تحويل الإيموجي:', error);
      await message.reply('❌ حدث خطأ أثناء تحويل النص!');
    }
  }
};

// تحويل النص لإيموجي
function convertToEmoji(text) {
  const emojiMap = {
    'a': '🇦', 'b': '🇧', 'c': '🇨', 'd': '🇩', 'e': '🇪',
    'f': '🇫', 'g': '🇬', 'h': '🇭', 'i': '🇮', 'j': '🇯',
    'k': '🇰', 'l': '🇱', 'm': '🇲', 'n': '🇳', 'o': '🇴',
    'p': '🇵', 'q': '🇶', 'r': '🇷', 's': '🇸', 't': '🇹',
    'u': '🇺', 'v': '🇻', 'w': '🇼', 'x': '🇽', 'y': '🇾', 'z': '🇿',
    ' ': '   ',
    '!': '❗',
    '?': '❓',
    '.': '🔸',
    ',': '🔹',
    ':': '🔸',
    ';': '🔹',
    '-': '➖',
    '+': '➕',
    '=': '🟰',
    '<': '◀️',
    '>': '▶️',
    '&': '🔗',
    '@': '📧',
    '#': '#️⃣',
    '$': '💲',
    '%': '💯',
    '*': '⭐',
    '(': '🔸',
    ')': '🔸'
  };
  
  return text.split('').map(char => emojiMap[char] || char).join('');
}

// تحويل الأرقام لإيموجي
function convertNumbersToEmoji(text) {
  const numberMap = {
    '0': '0️⃣',
    '1': '1️⃣',
    '2': '2️⃣',
    '3': '3️⃣',
    '4': '4️⃣',
    '5': '5️⃣',
    '6': '6️⃣',
    '7': '7️⃣',
    '8': '8️⃣',
    '9': '9️⃣'
  };
  
  return text.split('').map(char => numberMap[char] || char).join('');
}

// تحويل لرموز خاصة
function convertToSpecialEmoji(text) {
  const specialMap = {
    'love': '💕',
    'heart': '❤️',
    'fire': '🔥',
    'water': '💧',
    'sun': '☀️',
    'moon': '🌙',
    'star': '⭐',
    'music': '🎵',
    'game': '🎮',
    'food': '🍕',
    'car': '🚗',
    'house': '🏠',
    'tree': '🌳',
    'flower': '🌸',
    'cat': '🐱',
    'dog': '🐶',
    'happy': '😊',
    'sad': '😢',
    'angry': '😠',
    'cool': '😎',
    'laugh': '😂',
    'cry': '😭',
    'sleep': '😴',
    'party': '🎉',
    'gift': '🎁',
    'money': '💰',
    'crown': '👑',
    'diamond': '💎',
    'rocket': '🚀',
    'bomb': '💣',
    'magic': '✨',
    'rainbow': '🌈',
    'thunder': '⚡',
    'snow': '❄️',
    'wind': '💨',
    'earth': '🌍',
    'space': '🌌',
    'alien': '👽',
    'robot': '🤖',
    'ghost': '👻',
    'skull': '💀',
    'poop': '💩',
    'clown': '🤡',
    'devil': '😈',
    'angel': '😇',
    'king': '👑',
    'queen': '👸',
    'prince': '🤴',
    'princess': '👸',
    'baby': '👶',
    'old': '👴',
    'man': '👨',
    'woman': '👩',
    'boy': '👦',
    'girl': '👧',
    'yes': '✅',
    'no': '❌',
    'ok': '👌',
    'good': '👍',
    'bad': '👎',
    'peace': '✌️',
    'victory': '✌️',
    'strong': '💪',
    'pray': '🙏',
    'clap': '👏',
    'wave': '👋',
    'point': '👉',
    'up': '👆',
    'down': '👇'
  };
  
  let result = text;
  
  // البحث عن الكلمات الخاصة واستبدالها
  Object.keys(specialMap).forEach(word => {
    const regex = new RegExp(`\\b${word}\\b`, 'gi');
    result = result.replace(regex, specialMap[word]);
  });
  
  return result;
}
