const express = require('express');
const mongoose = require('mongoose');
const session = require('express-session');
const MongoStore = require('connect-mongo');
const passport = require('passport');
const path = require('path');
const helmet = require('helmet');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const morgan = require('morgan');
const cors = require('cors');

// استيراد الإعدادات (بدون dotenv - جميع الإعدادات في config.js)
const config = require('./config');

// استيراد المسارات
const authRoutes = require('./routes/auth');
const dashboardRoutes = require('./routes/dashboard');
const apiRoutes = require('./routes/api');

// استيراد حماية CS
const {
  csProtectionMiddleware,
  antiTamperingMiddleware,
  injectCreditsMiddleware,
  startPeriodicCheck
} = require('./middleware/csProtection');

// إنشاء تطبيق Express
const app = express();

// تمرير client للتطبيق (سيتم تحديثه عند تشغيل البوت)
app.set('client', null);

// إعداد محرك العرض
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// إعداد الملفات الثابتة
app.use('/static', express.static(path.join(__dirname, 'public')));
app.use('/assets', express.static(path.join(__dirname, 'assets')));

// إعداد الأمان
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://cdnjs.cloudflare.com", "https://fonts.googleapis.com"],
      scriptSrc: ["'self'", "'unsafe-inline'", "https://cdnjs.cloudflare.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com", "https://cdnjs.cloudflare.com"],
      imgSrc: ["'self'", "data:", "https:", "http:"],
      connectSrc: ["'self'", "https://discord.com", "https://discordapp.com"]
    }
  }
}));

// إعداد CORS
app.use(cors({
  origin: config.server.baseUrl,
  credentials: true
}));

// إعداد ضغط الاستجابات
app.use(compression());

// إعداد تسجيل الطلبات
if (config.development.debug) {
  app.use(morgan('combined'));
}

// إعداد حد الطلبات
const limiter = rateLimit({
  windowMs: config.security.rateLimitWindowMs,
  max: config.security.rateLimitMax,
  message: {
    error: config.messages.errors.rateLimited
  },
  standardHeaders: true,
  legacyHeaders: false
});
app.use(limiter);

// إعداد معالجة البيانات
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// تطبيق حماية CS Discord
app.use(antiTamperingMiddleware);
app.use(injectCreditsMiddleware);

// الاتصال بقاعدة البيانات
mongoose.connect(config.database.mongoUri, config.database.options)
  .then(async () => {
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // التحقق من حماية حقوق CS
    const CSProtection = require('./models/CSProtection');
    const isValid = await CSProtection.validateSystem();

    if (!isValid) {
      console.error('❌ خطأ: تم اكتشاف انتهاك لحقوق CS Discord');
      console.error('📺 يوتيوب: https://www.youtube.com/@CS_Discord');
      console.error('💬 ديسكورد: https://discord.gg/yqTn2EwVsd');
      console.error('⚠️  يرجى عدم حذف أو تعديل حقوق المطور');
      process.exit(1);
    }

    console.log('🛡️ تم التحقق من حماية النظام بنجاح');
  })
  .catch((error) => {
    console.error('❌ خطأ في الاتصال بقاعدة البيانات:', error);
    process.exit(1);
  });

// إعداد الجلسات
app.use(session({
  secret: config.server.sessionSecret,
  resave: false,
  saveUninitialized: false,
  store: MongoStore.create({
    mongoUrl: config.database.mongoUri,
    touchAfter: 24 * 3600 // تحديث الجلسة كل 24 ساعة
  }),
  cookie: {
    secure: process.env.NODE_ENV === 'production',
    httpOnly: true,
    maxAge: 7 * 24 * 60 * 60 * 1000 // 7 أيام
  }
}));

// إعداد Passport
app.use(passport.initialize());
app.use(passport.session());
require('./auth/passport-config');

// إعداد المتغيرات العامة للقوالب
app.use((req, res, next) => {
  res.locals.user = req.user || null;
  res.locals.config = config;
  res.locals.currentPath = req.path;
  res.locals.query = req.query;

  // إضافة حقوق CS لجميع الصفحات
  res.locals.csCredits = {
    youtube: 'https://www.youtube.com/@CS_Discord',
    discord: 'https://discord.gg/yqTn2EwVsd',
    author: 'CS Discord',
    protected: true
  };

  next();
});

// حماية إضافية ضد تعديل الحقوق
app.use((req, res, next) => {
  // التحقق من headers مشبوهة
  const suspiciousHeaders = ['x-remove-credits', 'x-modify-credits', 'x-cs-bypass'];
  const hasSuspiciousHeader = suspiciousHeaders.some(header => req.headers[header]);

  if (hasSuspiciousHeader) {
    console.log('🚨 محاولة مشبوهة لتعديل الحقوق من IP:', req.ip);
    return res.status(403).json({
      error: 'محاولة غير مصرح بها',
      message: 'تم رصد محاولة تعديل حقوق CS Discord',
      youtube: 'https://www.youtube.com/@CS_Discord',
      discord: 'https://discord.gg/yqTn2EwVsd'
    });
  }

  next();
});

// الصفحة الرئيسية
app.get('/', (req, res) => {
  if (req.user) {
    return res.redirect('/dashboard');
  }
  res.render('index', {
    title: 'مرحباً بك في لوحة تحكم البوت',
    description: config.dashboard.description
  });
});

// المسارات مع حماية CS
app.use('/auth', authRoutes);
app.use('/dashboard', csProtectionMiddleware, dashboardRoutes);
app.use('/api', csProtectionMiddleware, apiRoutes);

// معالجة الأخطاء 404
app.use((req, res) => {
  res.status(404).render('errors/404', {
    title: 'الصفحة غير موجودة',
    message: config.messages.errors.notFound
  });
});

// معالجة الأخطاء العامة
app.use((error, req, res, next) => {
  console.error('خطأ في الخادم:', error);
  
  // إخفاء تفاصيل الخطأ في الإنتاج
  const isDevelopment = config.development.debug;
  
  res.status(error.status || 500).render('errors/500', {
    title: 'خطأ في الخادم',
    message: config.messages.errors.serverError,
    error: isDevelopment ? error : {}
  });
});

// بدء الخادم
const PORT = config.server.port;
const HOST = config.server.host;

app.listen(PORT, HOST, () => {
  console.log(`🚀 الخادم يعمل على ${config.server.baseUrl}`);
  console.log(`📊 البيئة: ${process.env.NODE_ENV || 'development'}`);
  console.log(`🔧 وضع التطوير: ${config.development.debug ? 'مفعل' : 'معطل'}`);
  console.log(`🛡️ حماية CS Discord: مفعلة`);
  console.log(`📺 يوتيوب: https://www.youtube.com/@CS_Discord`);
  console.log(`💬 ديسكورد: https://discord.gg/yqTn2EwVsd`);

  // بدء الفحص الدوري لحماية CS
  startPeriodicCheck();
});

// معالجة إغلاق التطبيق بشكل صحيح
process.on('SIGTERM', () => {
  console.log('🛑 إيقاف الخادم...');
  mongoose.connection.close(() => {
    console.log('✅ تم إغلاق اتصال قاعدة البيانات');
    process.exit(0);
  });
});

process.on('SIGINT', async () => {
  console.log('🛑 إيقاف الخادم...');
  try {
    await mongoose.connection.close();
    console.log('✅ تم إغلاق اتصال قاعدة البيانات');
    process.exit(0);
  } catch (error) {
    console.error('❌ خطأ في إغلاق قاعدة البيانات:', error);
    process.exit(1);
  }
});

module.exports = app;
