// ========================================
// 🚀 CS Discord Bot + Dashboard
// ========================================
// 📺 يوتيوب: https://www.youtube.com/@CS_Discord
// 💬 ديسكورد: https://discord.gg/yqTn2EwVsd
// ========================================

console.log('🚀 بدء تشغيل CS Discord Bot + Dashboard...');
console.log('📺 يوتيوب: https://www.youtube.com/@CS_Discord');
console.log('💬 ديسكورد: https://discord.gg/yqTn2EwVsd');
console.log('========================================');

const express = require('express');
const mongoose = require('mongoose');
const session = require('express-session');
const MongoStore = require('connect-mongo');
const passport = require('passport');
const path = require('path');
const helmet = require('helmet');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const morgan = require('morgan');
const cors = require('cors');
const fs = require('fs');

// استيراد Discord.js للبوت
const { Client, GatewayIntentBits, Collection, ActivityType } = require('discord.js');

// استيراد الإعدادات
const config = require('./config');

// استيراد المسارات
const authRoutes = require('./routes/auth');
const dashboardRoutes = require('./routes/dashboard');
const apiRoutes = require('./routes/api');

// استيراد حماية CS
const {
  csProtectionMiddleware,
  antiTamperingMiddleware,
  injectCreditsMiddleware,
  startPeriodicCheck
} = require('./middleware/csProtection');

// ========================================
// 🤖 إعداد البوت
// ========================================

console.log('🤖 إنشاء البوت...');
const client = new Client({
  intents: [
    GatewayIntentBits.Guilds,
    GatewayIntentBits.GuildMessages,
    GatewayIntentBits.MessageContent,
    GatewayIntentBits.GuildMembers,
    GatewayIntentBits.GuildVoiceStates,
    GatewayIntentBits.GuildMessageReactions
  ]
});

// مجموعة الأوامر
client.commands = new Collection();

// تحميل الأوامر
function loadCommands() {
  const commandsPath = path.join(__dirname, 'commands');
  if (!fs.existsSync(commandsPath)) {
    console.log('📁 مجلد الأوامر غير موجود، سيتم إنشاؤه...');
    fs.mkdirSync(commandsPath, { recursive: true });
    return;
  }

  const commandFolders = fs.readdirSync(commandsPath);
  let commandCount = 0;

  for (const folder of commandFolders) {
    const folderPath = path.join(commandsPath, folder);
    if (!fs.statSync(folderPath).isDirectory()) continue;

    const commandFiles = fs.readdirSync(folderPath).filter(file => file.endsWith('.js'));

    for (const file of commandFiles) {
      const filePath = path.join(folderPath, file);
      try {
        const command = require(filePath);
        if (command.name) {
          client.commands.set(command.name, command);
          commandCount++;
          console.log(`✅ تم تحميل الأمر: ${command.name}`);
        }
      } catch (error) {
        console.error(`❌ خطأ في تحميل الأمر ${file}:`, error.message);
      }
    }
  }

  console.log(`📝 تم تحميل ${commandCount} أمر`);
}

// ========================================
// 🌐 إعداد الخادم
// ========================================

console.log('🌐 إنشاء خادم الويب...');
const app = express();

// تمرير البوت للتطبيق
app.set('client', client);

// ========================================
// 🤖 أحداث البوت
// ========================================

// عند تشغيل البوت
client.once('ready', async () => {
  console.log(`🤖 البوت ${client.user.tag} جاهز!`);
  console.log(`📊 متصل بـ ${client.guilds.cache.size} خادم`);
  console.log(`👥 يخدم ${client.guilds.cache.reduce((acc, guild) => acc + guild.memberCount, 0)} مستخدم`);

  // تحديث النشاط
  client.user.setActivity('CS Discord Dashboard', { type: ActivityType.Watching });

  console.log('🔗 تم ربط البوت بلوحة التحكم');
});

// عند انضمام البوت لخادم جديد
client.on('guildCreate', guild => {
  console.log(`📈 انضم البوت لخادم جديد: ${guild.name} (${guild.memberCount} عضو)`);
});

// عند مغادرة البوت لخادم
client.on('guildDelete', guild => {
  console.log(`📉 غادر البوت الخادم: ${guild.name}`);
});

// معالجة الرسائل
client.on('messageCreate', async message => {
  if (message.author.bot) return;
  if (!message.guild) return;

  const prefix = config.bot.prefix;
  if (!message.content.startsWith(prefix)) return;

  const args = message.content.slice(prefix.length).trim().split(/ +/);
  const commandName = args.shift().toLowerCase();

  const command = client.commands.get(commandName) ||
                 client.commands.find(cmd => cmd.aliases && cmd.aliases.includes(commandName));

  if (!command) return;

  // التحقق من الصلاحيات
  if (command.ownerOnly && message.author.id !== config.bot.ownerId) {
    return message.reply('❌ هذا الأمر متاح للمالك فقط!');
  }

  if (command.permissions) {
    if (!message.member.permissions.has(command.permissions)) {
      return message.reply('❌ ليس لديك الصلاحيات المطلوبة!');
    }
  }

  // تنفيذ الأمر
  try {
    await command.execute(message, args, client);
  } catch (error) {
    console.error(`❌ خطأ في تنفيذ الأمر ${commandName}:`, error);
    message.reply('❌ حدث خطأ أثناء تنفيذ الأمر!');
  }
});

// معالجة الأخطاء
client.on('error', error => {
  console.error('❌ خطأ في البوت:', error);
});

client.on('warn', warning => {
  console.warn('⚠️ تحذير:', warning);
});

// ========================================
// 🌐 إعداد الخادم
// ========================================

// إعداد محرك العرض
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// إعداد الملفات الثابتة
app.use('/static', express.static(path.join(__dirname, 'public')));
app.use('/assets', express.static(path.join(__dirname, 'assets')));

// إعداد الأمان
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://cdnjs.cloudflare.com", "https://fonts.googleapis.com"],
      scriptSrc: ["'self'", "'unsafe-inline'", "https://cdnjs.cloudflare.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com", "https://cdnjs.cloudflare.com"],
      imgSrc: ["'self'", "data:", "https:", "http:"],
      connectSrc: ["'self'", "https://discord.com", "https://discordapp.com"]
    }
  }
}));

// إعداد CORS
app.use(cors({
  origin: config.server.baseUrl,
  credentials: true
}));

// إعداد ضغط الاستجابات
app.use(compression());

// إعداد تسجيل الطلبات
if (config.development.debug) {
  app.use(morgan('combined'));
}

// إعداد حد الطلبات
const limiter = rateLimit({
  windowMs: config.security.rateLimitWindowMs,
  max: config.security.rateLimitMax,
  message: {
    error: config.messages.errors.rateLimited
  },
  standardHeaders: true,
  legacyHeaders: false
});
app.use(limiter);

// إعداد معالجة البيانات
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// تطبيق حماية CS Discord
app.use(antiTamperingMiddleware);
app.use(injectCreditsMiddleware);

// الاتصال بقاعدة البيانات
mongoose.connect(config.database.mongoUri, config.database.options)
  .then(async () => {
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // التحقق من حماية حقوق CS
    const CSProtection = require('./models/CSProtection');
    const isValid = await CSProtection.validateSystem();

    if (!isValid) {
      console.error('❌ خطأ: تم اكتشاف انتهاك لحقوق CS Discord');
      console.error('📺 يوتيوب: https://www.youtube.com/@CS_Discord');
      console.error('💬 ديسكورد: https://discord.gg/yqTn2EwVsd');
      console.error('⚠️  يرجى عدم حذف أو تعديل حقوق المطور');
      process.exit(1);
    }

    console.log('🛡️ تم التحقق من حماية النظام بنجاح');
  })
  .catch((error) => {
    console.error('❌ خطأ في الاتصال بقاعدة البيانات:', error);
    process.exit(1);
  });

// إعداد الجلسات
app.use(session({
  secret: config.server.sessionSecret,
  resave: false,
  saveUninitialized: false,
  store: MongoStore.create({
    mongoUrl: config.database.mongoUri,
    touchAfter: 24 * 3600 // تحديث الجلسة كل 24 ساعة
  }),
  cookie: {
    secure: process.env.NODE_ENV === 'production',
    httpOnly: true,
    maxAge: 7 * 24 * 60 * 60 * 1000 // 7 أيام
  }
}));

// إعداد Passport
app.use(passport.initialize());
app.use(passport.session());
require('./auth/passport-config');

// إعداد المتغيرات العامة للقوالب
app.use((req, res, next) => {
  res.locals.user = req.user || null;
  res.locals.config = config;
  res.locals.currentPath = req.path;
  res.locals.query = req.query;

  // إضافة معلومات إضافية للمستخدم
  if (req.user) {
    res.locals.isOwner = req.user.id === '761218404833034251' || req.user.id === '1381952235851747348';
    res.locals.userAvatar = req.user.avatar ?
      `https://cdn.discordapp.com/avatars/${req.user.id}/${req.user.avatar}.png?size=128` :
      '/static/images/default-avatar.png';
  }

  // إضافة حقوق CS لجميع الصفحات
  res.locals.csCredits = {
    youtube: 'https://www.youtube.com/@CS_Discord',
    discord: 'https://discord.gg/yqTn2EwVsd',
    author: 'CS Discord',
    protected: true
  };

  next();
});

// حماية إضافية ضد تعديل الحقوق
app.use((req, res, next) => {
  // التحقق من headers مشبوهة
  const suspiciousHeaders = ['x-remove-credits', 'x-modify-credits', 'x-cs-bypass'];
  const hasSuspiciousHeader = suspiciousHeaders.some(header => req.headers[header]);

  if (hasSuspiciousHeader) {
    console.log('🚨 محاولة مشبوهة لتعديل الحقوق من IP:', req.ip);
    return res.status(403).json({
      error: 'محاولة غير مصرح بها',
      message: 'تم رصد محاولة تعديل حقوق CS Discord',
      youtube: 'https://www.youtube.com/@CS_Discord',
      discord: 'https://discord.gg/yqTn2EwVsd'
    });
  }

  next();
});

// الصفحة الرئيسية
app.get('/', (req, res) => {
  if (req.user) {
    return res.redirect('/dashboard');
  }
  res.render('index', {
    title: 'مرحباً بك في لوحة تحكم البوت',
    description: config.dashboard.description
  });
});

// المسارات مع حماية CS
app.use('/auth', authRoutes);
app.use('/dashboard', csProtectionMiddleware, dashboardRoutes);
app.use('/api', csProtectionMiddleware, apiRoutes);

// معالجة الأخطاء 404
app.use((req, res) => {
  res.status(404).render('errors/404', {
    title: 'الصفحة غير موجودة',
    message: config.messages.errors.notFound
  });
});

// معالجة الأخطاء العامة
app.use((error, req, res, next) => {
  console.error('خطأ في الخادم:', error);
  
  // إخفاء تفاصيل الخطأ في الإنتاج
  const isDevelopment = config.development.debug;
  
  res.status(error.status || 500).render('errors/500', {
    title: 'خطأ في الخادم',
    message: config.messages.errors.serverError,
    error: isDevelopment ? error : {}
  });
});

// ========================================
// 🚀 بدء التشغيل
// ========================================

// اتصال قاعدة البيانات
async function connectDatabase() {
  try {
    console.log('🔗 محاولة الاتصال بقاعدة البيانات...');
    await mongoose.connect(config.database.mongoUri, config.database.options);
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');
  } catch (error) {
    console.error('❌ خطأ في الاتصال بقاعدة البيانات:', error.message);
    console.log('⚠️ سيتم المتابعة بدون قاعدة البيانات...');
  }
}

// بدء البوت
async function startBot() {
  try {
    console.log('🤖 محاولة تسجيل دخول البوت...');
    await client.login(config.bot.token);
  } catch (error) {
    console.error('❌ خطأ في تسجيل دخول البوت:', error.message);
    console.log('⚠️ تأكد من صحة التوكن في config.js');
    console.log('⚠️ سيتم تشغيل الموقع بدون البوت...');
  }
}

// بدء الخادم
function startServer() {
  const PORT = config.server.port;
  const HOST = config.server.host;

  app.listen(PORT, HOST, () => {
    console.log('========================================');
    console.log(`🚀 الخادم يعمل على ${config.server.baseUrl}`);
    console.log(`📊 البيئة: ${process.env.NODE_ENV || 'development'}`);
    console.log(`🔧 وضع التطوير: ${config.development.debug ? 'مفعل' : 'معطل'}`);
    console.log(`🛡️ حماية CS Discord: مفعلة`);
    console.log(`📺 يوتيوب: https://www.youtube.com/@CS_Discord`);
    console.log(`💬 ديسكورد: https://discord.gg/yqTn2EwVsd`);
    console.log('========================================');

    // بدء الفحص الدوري لحماية CS
    startPeriodicCheck();
  });
}

// تشغيل كل شيء
async function startEverything() {
  try {
    // 1. اتصال قاعدة البيانات
    await connectDatabase();

    // 2. تحميل الأوامر
    loadCommands();

    // 3. بدء البوت
    await startBot();

    // 4. بدء الخادم
    startServer();

  } catch (error) {
    console.error('❌ خطأ في بدء التشغيل:', error);

    // حتى لو فشل شيء، شغل الخادم
    console.log('🌐 تشغيل الخادم فقط...');
    startServer();
  }
}

// معالجة إغلاق التطبيق بشكل صحيح
process.on('SIGTERM', () => {
  console.log('🛑 إيقاف البوت والخادم...');
  if (client) {
    client.destroy();
  }
  mongoose.connection.close(() => {
    console.log('✅ تم إغلاق اتصال قاعدة البيانات');
    process.exit(0);
  });
});

process.on('SIGINT', async () => {
  console.log('🛑 إيقاف البوت والخادم...');
  try {
    if (client) {
      client.destroy();
    }
    await mongoose.connection.close();
    console.log('✅ تم إغلاق اتصال قاعدة البيانات');
    process.exit(0);
  } catch (error) {
    console.error('❌ خطأ في إغلاق قاعدة البيانات:', error);
    process.exit(1);
  }
});

// معالج الأخطاء 404
app.use((req, res, next) => {
  res.status(404).render('errors/404', {
    title: '404 - الصفحة غير موجودة'
  });
});

// معالج الأخطاء العامة
app.use((error, req, res, next) => {
  console.error('خطأ في الخادم:', error);
  res.status(500).render('errors/500', {
    title: '500 - خطأ في الخادم',
    message: 'حدث خطأ غير متوقع في الخادم'
  });
});

// بدء التشغيل
startEverything();

module.exports = app;
