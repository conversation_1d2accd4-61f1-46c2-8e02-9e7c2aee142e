// ========================================
// ⭕ لعبة XO - CS Discord
// ========================================
// 📺 يوتيوب: https://www.youtube.com/@CS_Discord
// 💬 ديسكورد: https://discord.gg/yqTn2EwVsd
// ========================================

const { EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');

// تخزين الألعاب النشطة
const activeGames = new Map();

module.exports = {
  name: 'tictactoe',
  aliases: ['ttt', 'xo', 'اكس_او'],
  description: 'العب XO مع عضو آخر',
  usage: '<@المستخدم>',
  category: 'games',
  cooldown: 5,
  
  async execute(message, args) {
    try {
      // التحقق من وجود المستخدم المذكور
      const targetUser = message.mentions.users.first();
      
      if (!targetUser) {
        return await message.reply('❌ يجب ذكر المستخدم الذي تريد اللعب معه!\nمثال: `tictactoe @أحمد`');
      }
      
      // التحقق من عدم اللعب مع النفس
      if (targetUser.id === message.author.id) {
        return await message.reply('😅 لا يمكنك اللعب مع نفسك! ادع صديق للعب!');
      }
      
      // التحقق من عدم اللعب مع البوت
      if (targetUser.bot) {
        return await message.reply('🤖 البوتات لا تلعب XO! جرب مع إنسان!');
      }
      
      // التحقق من وجود لعبة نشطة
      const gameId = `${message.author.id}-${targetUser.id}`;
      const reverseGameId = `${targetUser.id}-${message.author.id}`;
      
      if (activeGames.has(gameId) || activeGames.has(reverseGameId)) {
        return await message.reply('❌ يوجد لعبة نشطة بالفعل بينكما! انتظروا حتى تنتهي.');
      }
      
      // إنشاء لعبة جديدة
      const game = {
        player1: message.author,
        player2: targetUser,
        board: Array(9).fill(null),
        currentPlayer: message.author,
        gameId: gameId,
        messageId: null,
        startTime: Date.now()
      };
      
      activeGames.set(gameId, game);
      
      // إنشاء embed الدعوة
      const inviteEmbed = new EmbedBuilder()
        .setColor('#00ff00')
        .setTitle('🎮 دعوة للعب XO!')
        .setDescription(`${targetUser}, **${message.author.username}** يدعوك للعب XO!\n\n🎯 اضغط على "قبول" للبدء أو "رفض" للإلغاء`)
        .addFields(
          {
            name: '👤 اللاعب الأول',
            value: `${message.author} (❌)`,
            inline: true
          },
          {
            name: '👤 اللاعب الثاني',
            value: `${targetUser} (⭕)`,
            inline: true
          }
        )
        .setFooter({
          text: 'لديك 60 ثانية للرد',
          iconURL: message.client.user.displayAvatarURL()
        })
        .setTimestamp();
      
      // أزرار القبول والرفض
      const inviteRow = new ActionRowBuilder()
        .addComponents(
          new ButtonBuilder()
            .setCustomId('ttt_accept')
            .setLabel('✅ قبول')
            .setStyle(ButtonStyle.Success),
          new ButtonBuilder()
            .setCustomId('ttt_decline')
            .setLabel('❌ رفض')
            .setStyle(ButtonStyle.Danger)
        );
      
      const inviteMessage = await message.reply({ 
        embeds: [inviteEmbed], 
        components: [inviteRow] 
      });
      
      game.messageId = inviteMessage.id;
      
      // انتظار الرد
      const filter = (interaction) => {
        return interaction.user.id === targetUser.id && 
               (interaction.customId === 'ttt_accept' || interaction.customId === 'ttt_decline');
      };
      
      try {
        const interaction = await inviteMessage.awaitMessageComponent({ 
          filter, 
          time: 60000 
        });
        
        if (interaction.customId === 'ttt_decline') {
          activeGames.delete(gameId);
          
          const declineEmbed = new EmbedBuilder()
            .setColor('#ff0000')
            .setTitle('❌ تم رفض الدعوة')
            .setDescription(`${targetUser.username} رفض دعوة اللعب`)
            .setTimestamp();
          
          await interaction.update({ embeds: [declineEmbed], components: [] });
          return;
        }
        
        // بدء اللعبة
        await startGame(interaction, game);
        
      } catch (error) {
        // انتهت مهلة الدعوة
        activeGames.delete(gameId);
        
        const timeoutEmbed = new EmbedBuilder()
          .setColor('#ffa500')
          .setTitle('⏰ انتهت المهلة')
          .setDescription('انتهت مهلة الدعوة للعب')
          .setTimestamp();
        
        await inviteMessage.edit({ embeds: [timeoutEmbed], components: [] });
      }
      
    } catch (error) {
      console.error('خطأ في لعبة XO:', error);
      await message.reply('❌ حدث خطأ أثناء إنشاء اللعبة!');
    }
  }
};

async function startGame(interaction, game) {
  const gameEmbed = createGameEmbed(game);
  const gameButtons = createGameButtons(game);
  
  await interaction.update({ 
    embeds: [gameEmbed], 
    components: gameButtons 
  });
  
  // إعداد collector للعبة
  const filter = (buttonInteraction) => {
    return (buttonInteraction.user.id === game.player1.id || 
            buttonInteraction.user.id === game.player2.id) &&
           buttonInteraction.customId.startsWith('ttt_');
  };
  
  const collector = interaction.message.createMessageComponentCollector({ 
    filter, 
    time: 300000 // 5 دقائق
  });
  
  collector.on('collect', async (buttonInteraction) => {
    // التحقق من دور اللاعب
    if (buttonInteraction.user.id !== game.currentPlayer.id) {
      await buttonInteraction.reply({ 
        content: '❌ ليس دورك!', 
        ephemeral: true 
      });
      return;
    }
    
    // الحصول على موقع الزر
    const position = parseInt(buttonInteraction.customId.split('_')[2]);
    
    // التحقق من أن المربع فارغ
    if (game.board[position] !== null) {
      await buttonInteraction.reply({ 
        content: '❌ هذا المربع محجوز!', 
        ephemeral: true 
      });
      return;
    }
    
    // تحديث اللوحة
    game.board[position] = game.currentPlayer.id === game.player1.id ? 'X' : 'O';
    
    // فحص الفوز
    const winner = checkWinner(game.board);
    
    if (winner) {
      // انتهت اللعبة - هناك فائز
      const winnerUser = winner === 'X' ? game.player1 : game.player2;
      const winEmbed = createWinEmbed(game, winnerUser);
      
      activeGames.delete(game.gameId);
      collector.stop();
      
      await buttonInteraction.update({ 
        embeds: [winEmbed], 
        components: [] 
      });
      
    } else if (game.board.every(cell => cell !== null)) {
      // انتهت اللعبة - تعادل
      const tieEmbed = createTieEmbed(game);
      
      activeGames.delete(game.gameId);
      collector.stop();
      
      await buttonInteraction.update({ 
        embeds: [tieEmbed], 
        components: [] 
      });
      
    } else {
      // استمرار اللعبة
      game.currentPlayer = game.currentPlayer.id === game.player1.id ? game.player2 : game.player1;
      
      const gameEmbed = createGameEmbed(game);
      const gameButtons = createGameButtons(game);
      
      await buttonInteraction.update({ 
        embeds: [gameEmbed], 
        components: gameButtons 
      });
    }
  });
  
  collector.on('end', (collected, reason) => {
    if (reason === 'time') {
      activeGames.delete(game.gameId);
      
      const timeoutEmbed = new EmbedBuilder()
        .setColor('#ffa500')
        .setTitle('⏰ انتهت مهلة اللعبة')
        .setDescription('انتهت مهلة اللعبة بسبب عدم النشاط')
        .setTimestamp();
      
      interaction.editReply({ embeds: [timeoutEmbed], components: [] });
    }
  });
}

function createGameEmbed(game) {
  const board = game.board.map((cell, index) => {
    if (cell === 'X') return '❌';
    if (cell === 'O') return '⭕';
    return `${index + 1}️⃣`;
  });
  
  const boardDisplay = 
    `${board[0]} ${board[1]} ${board[2]}\n` +
    `${board[3]} ${board[4]} ${board[5]}\n` +
    `${board[6]} ${board[7]} ${board[8]}`;
  
  return new EmbedBuilder()
    .setColor('#0099ff')
    .setTitle('🎮 لعبة XO')
    .setDescription(`**دور:** ${game.currentPlayer}\n\n${boardDisplay}`)
    .addFields(
      {
        name: '❌ اللاعب الأول',
        value: `${game.player1.username}`,
        inline: true
      },
      {
        name: '⭕ اللاعب الثاني',
        value: `${game.player2.username}`,
        inline: true
      }
    )
    .setFooter({
      text: 'CS Discord Games',
      iconURL: game.player1.client.user.displayAvatarURL()
    })
    .setTimestamp();
}

function createGameButtons(game) {
  const rows = [];
  
  for (let i = 0; i < 3; i++) {
    const row = new ActionRowBuilder();
    
    for (let j = 0; j < 3; j++) {
      const position = i * 3 + j;
      const cell = game.board[position];
      
      let emoji = `${position + 1}️⃣`;
      let disabled = false;
      
      if (cell === 'X') {
        emoji = '❌';
        disabled = true;
      } else if (cell === 'O') {
        emoji = '⭕';
        disabled = true;
      }
      
      row.addComponents(
        new ButtonBuilder()
          .setCustomId(`ttt_move_${position}`)
          .setEmoji(emoji)
          .setStyle(ButtonStyle.Secondary)
          .setDisabled(disabled)
      );
    }
    
    rows.push(row);
  }
  
  return rows;
}

function createWinEmbed(game, winner) {
  return new EmbedBuilder()
    .setColor('#00ff00')
    .setTitle('🎉 انتهت اللعبة!')
    .setDescription(`🏆 **الفائز:** ${winner}\n\nتهانينا على الفوز!`)
    .addFields(
      {
        name: '📊 نتيجة اللعبة',
        value: `${winner.username} فاز في ${Math.floor((Date.now() - game.startTime) / 1000)} ثانية`,
        inline: false
      }
    )
    .setFooter({
      text: 'CS Discord Games',
      iconURL: winner.client.user.displayAvatarURL()
    })
    .setTimestamp();
}

function createTieEmbed(game) {
  return new EmbedBuilder()
    .setColor('#ffa500')
    .setTitle('🤝 تعادل!')
    .setDescription('انتهت اللعبة بالتعادل!\n\nلعبة ممتازة من الطرفين!')
    .addFields(
      {
        name: '📊 نتيجة اللعبة',
        value: `تعادل بعد ${Math.floor((Date.now() - game.startTime) / 1000)} ثانية`,
        inline: false
      }
    )
    .setFooter({
      text: 'CS Discord Games',
      iconURL: game.player1.client.user.displayAvatarURL()
    })
    .setTimestamp();
}

function checkWinner(board) {
  const winPatterns = [
    [0, 1, 2], [3, 4, 5], [6, 7, 8], // صفوف
    [0, 3, 6], [1, 4, 7], [2, 5, 8], // أعمدة
    [0, 4, 8], [2, 4, 6] // أقطار
  ];
  
  for (const pattern of winPatterns) {
    const [a, b, c] = pattern;
    if (board[a] && board[a] === board[b] && board[a] === board[c]) {
      return board[a];
    }
  }
  
  return null;
}
