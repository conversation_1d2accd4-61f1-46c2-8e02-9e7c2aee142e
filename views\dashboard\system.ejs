<%- include('../partials/header') %>

<div class="main-content">
    <div class="content-header">
        <h1 class="page-title">
            <i class="fas fa-server"></i>
            إدارة النظام
        </h1>
        <p class="page-description">
            مراقبة وإدارة النظام والخادم والبوت
        </p>
    </div>

    <!-- حالة النظام -->
    <div class="system-status">
        <div class="status-card online">
            <div class="status-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="status-info">
                <h3>النظام متصل</h3>
                <p>جميع الخدمات تعمل بشكل طبيعي</p>
            </div>
        </div>
    </div>

    <!-- إحصائيات النظام -->
    <div class="stats-grid">
        <div class="stat-card cpu">
            <div class="stat-icon">
                <i class="fas fa-microchip"></i>
            </div>
            <div class="stat-content">
                <div class="stat-number" id="cpu-usage">45%</div>
                <div class="stat-label">استخدام المعالج</div>
                <div class="stat-bar">
                    <div class="stat-fill" style="width: 45%"></div>
                </div>
            </div>
        </div>
        
        <div class="stat-card memory">
            <div class="stat-icon">
                <i class="fas fa-memory"></i>
            </div>
            <div class="stat-content">
                <div class="stat-number" id="memory-usage">67%</div>
                <div class="stat-label">استخدام الذاكرة</div>
                <div class="stat-bar">
                    <div class="stat-fill" style="width: 67%"></div>
                </div>
            </div>
        </div>
        
        <div class="stat-card disk">
            <div class="stat-icon">
                <i class="fas fa-hdd"></i>
            </div>
            <div class="stat-content">
                <div class="stat-number" id="disk-usage">23%</div>
                <div class="stat-label">استخدام القرص</div>
                <div class="stat-bar">
                    <div class="stat-fill" style="width: 23%"></div>
                </div>
            </div>
        </div>
        
        <div class="stat-card network">
            <div class="stat-icon">
                <i class="fas fa-wifi"></i>
            </div>
            <div class="stat-content">
                <div class="stat-number" id="network-speed">125 Mbps</div>
                <div class="stat-label">سرعة الشبكة</div>
                <div class="stat-bar">
                    <div class="stat-fill" style="width: 85%"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- أدوات إدارة النظام -->
    <div class="system-tools">
        <div class="card">
            <div class="card-header">
                <div class="card-icon">
                    <i class="fas fa-tools"></i>
                </div>
                <div>
                    <h3 class="card-title">أدوات النظام</h3>
                </div>
            </div>
            <div class="card-body">
                <div class="tools-grid">
                    <button class="tool-btn restart" onclick="restartBot()">
                        <i class="fas fa-redo"></i>
                        <span>إعادة تشغيل البوت</span>
                    </button>
                    
                    <button class="tool-btn update" onclick="updateBot()">
                        <i class="fas fa-download"></i>
                        <span>تحديث البوت</span>
                    </button>
                    
                    <button class="tool-btn cache" onclick="clearCache()">
                        <i class="fas fa-broom"></i>
                        <span>مسح الذاكرة المؤقتة</span>
                    </button>
                    
                    <button class="tool-btn logs" onclick="viewLogs()">
                        <i class="fas fa-file-alt"></i>
                        <span>عرض السجلات</span>
                    </button>
                    
                    <button class="tool-btn database" onclick="optimizeDatabase()">
                        <i class="fas fa-database"></i>
                        <span>تحسين قاعدة البيانات</span>
                    </button>
                    
                    <button class="tool-btn maintenance" onclick="toggleMaintenance()">
                        <i class="fas fa-wrench"></i>
                        <span>وضع الصيانة</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- معلومات النظام -->
    <div class="system-info">
        <div class="card">
            <div class="card-header">
                <div class="card-icon">
                    <i class="fas fa-info-circle"></i>
                </div>
                <div>
                    <h3 class="card-title">معلومات النظام</h3>
                </div>
            </div>
            <div class="card-body">
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">نظام التشغيل:</div>
                        <div class="info-value">Windows 11</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">إصدار Node.js:</div>
                        <div class="info-value">v18.17.0</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">إصدار Discord.js:</div>
                        <div class="info-value">v14.13.0</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">وقت التشغيل:</div>
                        <div class="info-value" id="uptime">2 ساعة 15 دقيقة</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">آخر إعادة تشغيل:</div>
                        <div class="info-value">اليوم 14:30</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">إجمالي الذاكرة:</div>
                        <div class="info-value">16 GB</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <div class="card-icon">
                    <i class="fas fa-chart-area"></i>
                </div>
                <div>
                    <h3 class="card-title">مراقبة الأداء</h3>
                </div>
            </div>
            <div class="card-body">
                <div class="performance-chart">
                    <canvas id="performanceChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- سجل الأنشطة -->
    <div class="card">
        <div class="card-header">
            <div class="card-icon">
                <i class="fas fa-history"></i>
            </div>
            <div>
                <h3 class="card-title">سجل أنشطة النظام</h3>
            </div>
            <div class="card-actions">
                <button class="btn btn-sm btn-primary" onclick="refreshSystemLogs()">
                    <i class="fas fa-sync"></i>
                    تحديث
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="system-logs" id="system-logs">
                <div class="loading-spinner">
                    <i class="fas fa-spinner fa-spin"></i>
                    جاري تحميل سجل الأنشطة...
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.system-status {
    margin-bottom: 30px;
}

.status-card {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 25px;
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.status-card.online {
    background: linear-gradient(135deg, #00ff88, #00d4aa);
    color: white;
}

.status-card.offline {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
}

.status-icon {
    font-size: 2rem;
}

.status-info h3 {
    margin: 0 0 5px 0;
    font-size: 1.3rem;
}

.status-info p {
    margin: 0;
    opacity: 0.9;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 25px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 20px;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-card.cpu .stat-icon {
    background: linear-gradient(45deg, #667eea, #764ba2);
}

.stat-card.memory .stat-icon {
    background: linear-gradient(45deg, #feca57, #ff9ff3);
}

.stat-card.disk .stat-icon {
    background: linear-gradient(45deg, #00ff88, #00d4aa);
}

.stat-card.network .stat-icon {
    background: linear-gradient(45deg, #ff6b6b, #ee5a52);
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 1.8rem;
    font-weight: bold;
    color: white;
    margin-bottom: 5px;
}

.stat-label {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    margin-bottom: 10px;
}

.stat-bar {
    width: 100%;
    height: 6px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    overflow: hidden;
}

.stat-fill {
    height: 100%;
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 3px;
    transition: width 0.3s ease;
}

.system-tools {
    margin-bottom: 30px;
}

.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.tool-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.tool-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.tool-btn i {
    font-size: 1.5rem;
    margin-bottom: 5px;
}

.tool-btn.restart:hover {
    border-color: #feca57;
    color: #feca57;
}

.tool-btn.update:hover {
    border-color: #00ff88;
    color: #00ff88;
}

.tool-btn.cache:hover {
    border-color: #667eea;
    color: #667eea;
}

.tool-btn.logs:hover {
    border-color: #ff9ff3;
    color: #ff9ff3;
}

.tool-btn.database:hover {
    border-color: #764ba2;
    color: #764ba2;
}

.tool-btn.maintenance:hover {
    border-color: #ff6b6b;
    color: #ff6b6b;
}

.system-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.info-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 15px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
}

.info-label {
    color: rgba(255, 255, 255, 0.8);
    font-weight: bold;
}

.info-value {
    color: white;
    font-family: monospace;
}

.performance-chart {
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
}

.system-logs {
    min-height: 300px;
    max-height: 400px;
    overflow-y: auto;
}

.log-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 10px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.log-time {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.8rem;
    min-width: 80px;
}

.log-level {
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 0.7rem;
    font-weight: bold;
    min-width: 60px;
    text-align: center;
}

.log-level.info {
    background: #667eea;
    color: white;
}

.log-level.warning {
    background: #feca57;
    color: #333;
}

.log-level.error {
    background: #ff6b6b;
    color: white;
}

.log-level.success {
    background: #00ff88;
    color: #333;
}

.log-message {
    color: rgba(255, 255, 255, 0.9);
    flex: 1;
}

@media (max-width: 768px) {
    .tools-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .system-info {
        grid-template-columns: 1fr;
    }
    
    .info-item {
        flex-direction: column;
        gap: 5px;
        text-align: center;
    }
    
    .stat-card {
        flex-direction: column;
        text-align: center;
    }
}
</style>

<script>
// بيانات سجل النظام التجريبية
const systemLogs = [
    {
        time: '15:30:25',
        level: 'info',
        message: 'تم بدء تشغيل البوت بنجاح'
    },
    {
        time: '15:29:12',
        level: 'success',
        message: 'تم الاتصال بقاعدة البيانات'
    },
    {
        time: '15:28:45',
        level: 'info',
        message: 'تم تحميل 25 أمر'
    },
    {
        time: '15:27:33',
        level: 'warning',
        message: 'استخدام الذاكرة مرتفع (67%)'
    },
    {
        time: '15:26:18',
        level: 'info',
        message: 'تم الاتصال بـ Discord API'
    },
    {
        time: '15:25:02',
        level: 'error',
        message: 'فشل في الاتصال بخادم خارجي'
    }
];

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadSystemLogs();
    updateSystemStats();
    drawPerformanceChart();
    
    // تحديث الإحصائيات كل 5 ثوان
    setInterval(updateSystemStats, 5000);
});

// تحميل سجل النظام
function loadSystemLogs() {
    const container = document.getElementById('system-logs');
    
    container.innerHTML = systemLogs.map(log => `
        <div class="log-item">
            <div class="log-time">${log.time}</div>
            <div class="log-level ${log.level}">${getLevelText(log.level)}</div>
            <div class="log-message">${log.message}</div>
        </div>
    `).join('');
}

// تحديث إحصائيات النظام
function updateSystemStats() {
    // محاكاة تحديث الإحصائيات
    const cpuUsage = Math.floor(Math.random() * 30) + 30;
    const memoryUsage = Math.floor(Math.random() * 20) + 60;
    const diskUsage = Math.floor(Math.random() * 10) + 20;
    
    document.getElementById('cpu-usage').textContent = cpuUsage + '%';
    document.getElementById('memory-usage').textContent = memoryUsage + '%';
    document.getElementById('disk-usage').textContent = diskUsage + '%';
    
    // تحديث أشرطة التقدم
    document.querySelector('.stat-card.cpu .stat-fill').style.width = cpuUsage + '%';
    document.querySelector('.stat-card.memory .stat-fill').style.width = memoryUsage + '%';
    document.querySelector('.stat-card.disk .stat-fill').style.width = diskUsage + '%';
}

// رسم مخطط الأداء
function drawPerformanceChart() {
    const canvas = document.getElementById('performanceChart');
    const ctx = canvas.getContext('2d');
    
    // محاكاة رسم بياني بسيط
    ctx.fillStyle = '#667eea';
    ctx.fillRect(50, 150, 300, 30);
    ctx.fillStyle = 'white';
    ctx.font = '14px Arial';
    ctx.fillText('مخطط الأداء: استقرار النظام', 50, 140);
}

// إعادة تشغيل البوت
function restartBot() {
    if (confirm('هل أنت متأكد من إعادة تشغيل البوت؟\nسيتم قطع الاتصال مؤقتاً.')) {
        showNotification('🔄 جاري إعادة تشغيل البوت...', 'warning');
        
        setTimeout(() => {
            showNotification('✅ تم إعادة تشغيل البوت بنجاح!', 'success');
        }, 3000);
    }
}

// تحديث البوت
function updateBot() {
    showNotification('🔍 جاري البحث عن تحديثات...', 'info');
    
    setTimeout(() => {
        showNotification('✅ البوت محدث إلى أحدث إصدار!', 'success');
    }, 2000);
}

// مسح الذاكرة المؤقتة
function clearCache() {
    showNotification('🧹 جاري مسح الذاكرة المؤقتة...', 'info');
    
    setTimeout(() => {
        showNotification('✅ تم مسح الذاكرة المؤقتة بنجاح!', 'success');
    }, 1500);
}

// عرض السجلات
function viewLogs() {
    showNotification('📄 فتح سجلات النظام...', 'info');
    // يمكن فتح نافذة جديدة أو توجيه لصفحة السجلات
}

// تحسين قاعدة البيانات
function optimizeDatabase() {
    if (confirm('هل تريد تحسين قاعدة البيانات؟\nقد يستغرق هذا بعض الوقت.')) {
        showNotification('🔧 جاري تحسين قاعدة البيانات...', 'info');
        
        setTimeout(() => {
            showNotification('✅ تم تحسين قاعدة البيانات بنجاح!', 'success');
        }, 4000);
    }
}

// تبديل وضع الصيانة
function toggleMaintenance() {
    const isMaintenanceMode = confirm('هل تريد تفعيل وضع الصيانة؟\nسيتم إيقاف جميع الأوامر مؤقتاً.');
    
    if (isMaintenanceMode) {
        showNotification('🔧 تم تفعيل وضع الصيانة', 'warning');
    } else {
        showNotification('✅ تم إلغاء وضع الصيانة', 'success');
    }
}

// تحديث سجل النظام
function refreshSystemLogs() {
    // إضافة سجل جديد
    const newLog = {
        time: new Date().toLocaleTimeString('ar-EG'),
        level: 'info',
        message: 'تم تحديث سجل النظام'
    };
    
    systemLogs.unshift(newLog);
    loadSystemLogs();
    showNotification('🔄 تم تحديث سجل النظام!', 'info');
}

// دوال مساعدة
function getLevelText(level) {
    const levelTexts = {
        info: 'معلومات',
        warning: 'تحذير',
        error: 'خطأ',
        success: 'نجح'
    };
    return levelTexts[level] || level;
}

// إظهار الإشعارات
function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type} notification`;
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        animation: slideIn 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}
</script>

<%- include('../partials/footer') %>
