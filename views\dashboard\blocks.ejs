<%- include('../partials/header') %>

<div class="main-content">
    <div class="content-header">
        <h1 class="page-title">
            <i class="fas fa-cubes"></i>
            نظام البلوكات
        </h1>
        <p class="page-description">
            إنشاء وإدارة بلوكات تفاعلية مخصصة للخادم
        </p>
    </div>

    <!-- أدوات سريعة -->
    <div class="quick-actions">
        <button class="btn btn-success" onclick="openCreateBlockModal()">
            <i class="fas fa-plus"></i>
            إنشاء بلوك جديد
        </button>
        <button class="btn btn-info" onclick="openBlockTemplatesModal()">
            <i class="fas fa-layer-group"></i>
            قوالب البلوكات
        </button>
        <button class="btn btn-warning" onclick="importBlocks()">
            <i class="fas fa-upload"></i>
            استيراد بلوكات
        </button>
        <button class="btn btn-secondary" onclick="exportBlocks()">
            <i class="fas fa-download"></i>
            تصدير البلوكات
        </button>
    </div>

    <!-- إحصائيات البلوكات -->
    <div class="stats-grid">
        <div class="stat-card active">
            <div class="stat-number" id="active-blocks">0</div>
            <div class="stat-label">بلوكات نشطة</div>
        </div>
        <div class="stat-card interactive">
            <div class="stat-number" id="interactive-blocks">0</div>
            <div class="stat-label">بلوكات تفاعلية</div>
        </div>
        <div class="stat-card automated">
            <div class="stat-number" id="automated-blocks">0</div>
            <div class="stat-label">بلوكات تلقائية</div>
        </div>
        <div class="stat-card usage">
            <div class="stat-number" id="total-usage">0</div>
            <div class="stat-label">إجمالي الاستخدام</div>
        </div>
    </div>

    <!-- فئات البلوكات -->
    <div class="blocks-categories">
        <div class="category-tabs">
            <button class="tab-btn active" onclick="switchCategory('all')">جميع البلوكات</button>
            <button class="tab-btn" onclick="switchCategory('welcome')">ترحيب</button>
            <button class="tab-btn" onclick="switchCategory('moderation')">إشراف</button>
            <button class="tab-btn" onclick="switchCategory('fun')">ترفيه</button>
            <button class="tab-btn" onclick="switchCategory('utility')">أدوات</button>
            <button class="tab-btn" onclick="switchCategory('custom')">مخصص</button>
        </div>
    </div>

    <!-- قائمة البلوكات -->
    <div class="card">
        <div class="card-header">
            <div class="card-icon">
                <i class="fas fa-cubes"></i>
            </div>
            <div>
                <h3 class="card-title">البلوكات المتاحة</h3>
            </div>
            <div class="card-actions">
                <input type="text" class="form-control form-control-sm" placeholder="بحث..." id="blockSearch" onkeyup="searchBlocks()">
                <button class="btn btn-sm btn-primary" onclick="refreshBlocks()">
                    <i class="fas fa-sync"></i>
                    تحديث
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="blocks-container" id="blocks-container">
                <div class="loading-spinner">
                    <i class="fas fa-spinner fa-spin"></i>
                    جاري تحميل البلوكات...
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal إنشاء بلوك جديد -->
<div class="modal fade" id="createBlockModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">🧩 إنشاء بلوك جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createBlockForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">اسم البلوك</label>
                                <input type="text" class="form-control" id="blockName" placeholder="مثال: ترحيب تلقائي" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">فئة البلوك</label>
                                <select class="form-control" id="blockCategory" required>
                                    <option value="">اختر الفئة</option>
                                    <option value="welcome">ترحيب</option>
                                    <option value="moderation">إشراف</option>
                                    <option value="fun">ترفيه</option>
                                    <option value="utility">أدوات</option>
                                    <option value="custom">مخصص</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">وصف البلوك</label>
                        <textarea class="form-control" id="blockDescription" rows="3" placeholder="وصف مختصر لوظيفة البلوك..."></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">نوع التفعيل</label>
                                <select class="form-control" id="blockTrigger" onchange="updateTriggerOptions()">
                                    <option value="command">أمر</option>
                                    <option value="event">حدث</option>
                                    <option value="reaction">تفاعل</option>
                                    <option value="keyword">كلمة مفتاحية</option>
                                    <option value="schedule">مجدول</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">قيمة التفعيل</label>
                                <input type="text" class="form-control" id="blockTriggerValue" placeholder="مثال: !welcome">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">إعدادات البلوك</label>
                        <div class="block-settings">
                            <div class="setting-item">
                                <input type="checkbox" id="blockEnabled" checked>
                                <label for="blockEnabled">مفعل</label>
                            </div>
                            <div class="setting-item">
                                <input type="checkbox" id="blockLogging">
                                <label for="blockLogging">تسجيل الأنشطة</label>
                            </div>
                            <div class="setting-item">
                                <input type="checkbox" id="blockCooldown">
                                <label for="blockCooldown">فترة انتظار</label>
                            </div>
                            <div class="setting-item">
                                <input type="checkbox" id="blockPermissions">
                                <label for="blockPermissions">تحديد الصلاحيات</label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">محتوى البلوك</label>
                        <div class="block-content-editor">
                            <div class="editor-tabs">
                                <button type="button" class="tab-btn active" onclick="switchEditorTab('message')">رسالة</button>
                                <button type="button" class="tab-btn" onclick="switchEditorTab('embed')">Embed</button>
                                <button type="button" class="tab-btn" onclick="switchEditorTab('actions')">إجراءات</button>
                                <button type="button" class="tab-btn" onclick="switchEditorTab('conditions')">شروط</button>
                            </div>
                            
                            <div class="editor-content">
                                <div class="editor-panel active" id="message-panel">
                                    <textarea class="form-control" id="blockMessage" rows="4" placeholder="محتوى الرسالة..."></textarea>
                                    <div class="variables-help">
                                        <small>المتغيرات المتاحة: {user}, {server}, {channel}, {date}, {time}</small>
                                    </div>
                                </div>
                                
                                <div class="editor-panel" id="embed-panel">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <input type="text" class="form-control mb-2" id="embedTitle" placeholder="عنوان Embed">
                                            <textarea class="form-control mb-2" id="embedDescription" rows="3" placeholder="وصف Embed"></textarea>
                                            <input type="color" class="form-control" id="embedColor" value="#667eea">
                                        </div>
                                        <div class="col-md-6">
                                            <input type="url" class="form-control mb-2" id="embedImage" placeholder="رابط الصورة">
                                            <input type="url" class="form-control mb-2" id="embedThumbnail" placeholder="رابط الصورة المصغرة">
                                            <input type="text" class="form-control" id="embedFooter" placeholder="نص التذييل">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="editor-panel" id="actions-panel">
                                    <div class="actions-list">
                                        <div class="action-item">
                                            <select class="form-control">
                                                <option>إضافة رتبة</option>
                                                <option>إزالة رتبة</option>
                                                <option>إرسال رسالة خاصة</option>
                                                <option>كتم مؤقت</option>
                                                <option>طرد</option>
                                                <option>حظر</option>
                                            </select>
                                            <input type="text" class="form-control" placeholder="القيمة">
                                            <button type="button" class="btn btn-sm btn-danger">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <button type="button" class="btn btn-sm btn-success" onclick="addAction()">
                                        <i class="fas fa-plus"></i>
                                        إضافة إجراء
                                    </button>
                                </div>
                                
                                <div class="editor-panel" id="conditions-panel">
                                    <div class="conditions-list">
                                        <div class="condition-item">
                                            <select class="form-control">
                                                <option>المستخدم لديه رتبة</option>
                                                <option>المستخدم في قناة</option>
                                                <option>الوقت بين</option>
                                                <option>عدد الرسائل أكبر من</option>
                                            </select>
                                            <input type="text" class="form-control" placeholder="القيمة">
                                            <button type="button" class="btn btn-sm btn-danger">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <button type="button" class="btn btn-sm btn-success" onclick="addCondition()">
                                        <i class="fas fa-plus"></i>
                                        إضافة شرط
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">معاينة البلوك</label>
                        <div class="block-preview" id="blockPreview">
                            <div class="preview-message">
                                <div class="message-content">محتوى البلوك سيظهر هنا...</div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-info" onclick="testBlock()">
                    <i class="fas fa-vial"></i>
                    اختبار
                </button>
                <button type="button" class="btn btn-success" onclick="createBlock()">
                    <i class="fas fa-save"></i>
                    حفظ البلوك
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal قوالب البلوكات -->
<div class="modal fade" id="blockTemplatesModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">📋 قوالب البلوكات</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="templates-grid">
                    <div class="template-card" onclick="applyBlockTemplate('welcome')">
                        <div class="template-icon">👋</div>
                        <h6>رسالة ترحيب</h6>
                        <p>ترحيب تلقائي بالأعضاء الجدد</p>
                    </div>
                    <div class="template-card" onclick="applyBlockTemplate('goodbye')">
                        <div class="template-icon">👋</div>
                        <h6>رسالة وداع</h6>
                        <p>وداع الأعضاء المغادرين</p>
                    </div>
                    <div class="template-card" onclick="applyBlockTemplate('autorole')">
                        <div class="template-icon">🎭</div>
                        <h6>رتبة تلقائية</h6>
                        <p>إعطاء رتبة للأعضاء الجدد</p>
                    </div>
                    <div class="template-card" onclick="applyBlockTemplate('reaction')">
                        <div class="template-icon">⭐</div>
                        <h6>رتب التفاعل</h6>
                        <p>رتب عبر التفاعل مع رسالة</p>
                    </div>
                    <div class="template-card" onclick="applyBlockTemplate('modlog')">
                        <div class="template-icon">📋</div>
                        <h6>سجل الإشراف</h6>
                        <p>تسجيل أنشطة الإشراف</p>
                    </div>
                    <div class="template-card" onclick="applyBlockTemplate('automod')">
                        <div class="template-icon">🛡️</div>
                        <h6>إشراف تلقائي</h6>
                        <p>حماية تلقائية من السبام</p>
                    </div>
                    <div class="template-card" onclick="applyBlockTemplate('announcement')">
                        <div class="template-icon">📢</div>
                        <h6>إعلان مجدول</h6>
                        <p>إعلانات تلقائية مجدولة</p>
                    </div>
                    <div class="template-card" onclick="applyBlockTemplate('giveaway')">
                        <div class="template-icon">🎁</div>
                        <h6>مسابقة تلقائية</h6>
                        <p>إنشاء مسابقات تلقائية</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.quick-actions {
    display: flex;
    gap: 15px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.stat-card.active {
    background: linear-gradient(135deg, #00ff88, #00d4aa);
    color: white;
}

.stat-card.interactive {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.stat-card.automated {
    background: linear-gradient(135deg, #feca57, #ff9ff3);
    color: white;
}

.stat-card.usage {
    background: linear-gradient(135deg, #4ecdc4, #44a08d);
    color: white;
}

.blocks-categories {
    margin-bottom: 30px;
}

.category-tabs {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.tab-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: rgba(255, 255, 255, 0.8);
    padding: 10px 20px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.tab-btn:hover, .tab-btn.active {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border-color: #667eea;
}

.blocks-container {
    min-height: 300px;
}

.block-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 15px;
    border-left: 4px solid;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.block-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.05), transparent);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.block-item:hover::before {
    transform: translateX(100%);
}

.block-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.block-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.block-title {
    font-size: 1.2rem;
    font-weight: bold;
    color: white;
    margin: 0;
}

.block-category {
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: bold;
}

.block-description {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 15px;
}

.block-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
}

.block-actions {
    display: flex;
    gap: 10px;
}

.block-settings {
    background: rgba(255, 255, 255, 0.05);
    padding: 15px;
    border-radius: 10px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.setting-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.setting-item input[type="checkbox"] {
    width: 16px;
    height: 16px;
}

.setting-item label {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.9);
    cursor: pointer;
}

.block-content-editor {
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    overflow: hidden;
}

.editor-tabs {
    display: flex;
    background: rgba(255, 255, 255, 0.05);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.editor-tabs .tab-btn {
    border-radius: 0;
    border: none;
    border-right: 1px solid rgba(255, 255, 255, 0.2);
}

.editor-content {
    padding: 20px;
}

.editor-panel {
    display: none;
}

.editor-panel.active {
    display: block;
}

.variables-help {
    margin-top: 10px;
    color: rgba(255, 255, 255, 0.6);
}

.actions-list, .conditions-list {
    margin-bottom: 15px;
}

.action-item, .condition-item {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
    align-items: center;
}

.action-item select, .condition-item select {
    flex: 2;
}

.action-item input, .condition-item input {
    flex: 1;
}

.block-preview {
    background: rgba(255, 255, 255, 0.05);
    border: 2px dashed rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    padding: 20px;
    min-height: 100px;
}

.preview-message {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 15px;
}

.message-content {
    color: white;
    font-size: 0.9rem;
}

.templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.template-card {
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.template-card:hover {
    border-color: #667eea;
    transform: translateY(-5px);
    background: rgba(102, 126, 234, 0.1);
}

.template-icon {
    font-size: 2rem;
    margin-bottom: 10px;
}

.template-card h6 {
    color: white;
    margin-bottom: 10px;
}

.template-card p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    margin: 0;
}

@media (max-width: 768px) {
    .quick-actions {
        flex-direction: column;
    }
    
    .category-tabs {
        justify-content: center;
    }
    
    .block-header {
        flex-direction: column;
        gap: 10px;
    }
    
    .block-meta {
        flex-direction: column;
        gap: 10px;
        align-items: flex-start;
    }
    
    .action-item, .condition-item {
        flex-direction: column;
    }
    
    .templates-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}
</style>

<script src="/static/js/blocks.js"></script>

<%- include('../partials/footer') %>
