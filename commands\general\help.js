// ========================================
// 📚 أمر المساعدة - CS Discord
// ========================================
// 📺 يوتيوب: https://www.youtube.com/@CS_Discord
// 💬 ديسكورد: https://discord.gg/yqTn2EwVsd
// ========================================

const { EmbedBuilder } = require('discord.js');

module.exports = {
  name: 'help',
  aliases: ['مساعدة', 'h', 'commands'],
  description: 'عرض قائمة الأوامر المتاحة',
  usage: 'help [command]',
  category: 'general',
  cooldown: 5,
  
  async execute(message, args, client) {
    try {
      const config = require('../../config');
      
      if (args.length) {
        // عرض معلومات أمر محدد
        const commandName = args[0].toLowerCase();
        const command = client.commands.get(commandName) || 
                       client.commands.find(cmd => cmd.aliases && cmd.aliases.includes(commandName));
        
        if (!command) {
          return message.reply('❌ لم يتم العثور على هذا الأمر!');
        }
        
        const embed = new EmbedBuilder()
          .setColor('#667eea')
          .setTitle(`📝 معلومات الأمر: ${command.name}`)
          .setDescription(command.description || 'لا يوجد وصف')
          .addFields(
            {
              name: '📋 الاستخدام',
              value: `\`${config.bot.prefix}${command.usage || command.name}\``,
              inline: true
            },
            {
              name: '📂 الفئة',
              value: command.category || 'عام',
              inline: true
            },
            {
              name: '⏱️ الانتظار',
              value: `${command.cooldown || 0} ثانية`,
              inline: true
            }
          );
        
        if (command.aliases && command.aliases.length) {
          embed.addFields({
            name: '🔗 الأسماء البديلة',
            value: command.aliases.map(alias => `\`${alias}\``).join(', '),
            inline: false
          });
        }
        
        embed.setFooter({
          text: 'CS Discord Bot',
          iconURL: client.user.displayAvatarURL()
        });
        
        return message.reply({ embeds: [embed] });
      }
      
      // عرض جميع الأوامر
      const categories = {};
      
      client.commands.forEach(command => {
        const category = command.category || 'عام';
        if (!categories[category]) {
          categories[category] = [];
        }
        categories[category].push(command);
      });
      
      const embed = new EmbedBuilder()
        .setColor('#667eea')
        .setTitle('📚 قائمة الأوامر')
        .setDescription(`استخدم \`${config.bot.prefix}help [command]\` للحصول على معلومات مفصلة عن أمر معين`)
        .setThumbnail(client.user.displayAvatarURL());
      
      Object.keys(categories).forEach(category => {
        const categoryCommands = categories[category];
        const commandList = categoryCommands.map(cmd => `\`${cmd.name}\``).join(', ');
        
        embed.addFields({
          name: `📂 ${getCategoryEmoji(category)} ${category}`,
          value: commandList || 'لا توجد أوامر',
          inline: false
        });
      });
      
      embed.addFields(
        {
          name: '🔗 روابط مفيدة',
          value: `[📺 يوتيوب](https://www.youtube.com/@CS_Discord) • [💬 ديسكورد](https://discord.gg/yqTn2EwVsd) • [🌐 لوحة التحكم](${config.server.baseUrl})`,
          inline: false
        }
      );
      
      embed.setFooter({
        text: `إجمالي الأوامر: ${client.commands.size} • البادئة: ${config.bot.prefix}`,
        iconURL: client.user.displayAvatarURL()
      })
      .setTimestamp();
      
      await message.reply({ embeds: [embed] });
      
    } catch (error) {
      console.error('خطأ في أمر المساعدة:', error);
      await message.reply('❌ حدث خطأ أثناء عرض المساعدة!');
    }
  }
};

function getCategoryEmoji(category) {
  const emojis = {
    'general': '🌟',
    'عام': '🌟',
    'moderation': '🛡️',
    'إشراف': '🛡️',
    'fun': '🎉',
    'ترفيه': '🎉',
    'music': '🎵',
    'موسيقى': '🎵',
    'economy': '💰',
    'اقتصاد': '💰',
    'owner': '👑',
    'مالك': '👑',
    'utility': '🔧',
    'أدوات': '🔧'
  };
  
  return emojis[category.toLowerCase()] || '📋';
}
