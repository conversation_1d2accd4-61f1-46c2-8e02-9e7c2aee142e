// ========================================
// 📝 أمر تغيير الاسم - CS Discord
// ========================================
// 📺 يوتيوب: https://www.youtube.com/@CS_Discord
// 💬 ديسكورد: https://discord.gg/yqTn2EwVsd
// ========================================

const { EmbedBuilder, PermissionFlagsBits } = require('discord.js');

module.exports = {
  name: 'nick',
  aliases: ['nickname', 'اسم', 'تغيير_اسم'],
  description: 'تغيير اسم العضو في الخادم',
  usage: '<@المستخدم> [الاسم الجديد]',
  category: 'moderation',
  permissions: [PermissionFlagsBits.ManageNicknames],
  cooldown: 3,
  
  async execute(message, args) {
    try {
      // التحقق من الصلاحيات
      if (!message.member.permissions.has(PermissionFlagsBits.ManageNicknames)) {
        return await message.reply('❌ ليس لديك صلاحية تغيير أسماء الأعضاء!');
      }
      
      // التحقق من صلاحيات البوت
      if (!message.guild.members.me.permissions.has(PermissionFlagsBits.ManageNicknames)) {
        return await message.reply('❌ البوت لا يملك صلاحية تغيير الأسماء!');
      }
      
      // التحقق من وجود المعاملات
      if (!args.length) {
        return await message.reply('❌ استخدام خاطئ!\nالاستخدام: `nick <@المستخدم> [الاسم الجديد]`\nمثال: `nick @أحمد أحمد الجديد`\nلإزالة الاسم: `nick @أحمد`');
      }
      
      // تحديد المستخدم
      const targetMember = message.mentions.members.first();
      if (!targetMember) {
        return await message.reply('❌ يجب ذكر المستخدم!\nمثال: `nick @أحمد أحمد الجديد`');
      }
      
      // التحقق من عدم تغيير اسم النفس (إلا إذا كان المالك)
      if (targetMember.id === message.author.id && message.author.id !== message.guild.ownerId) {
        return await message.reply('❌ لا يمكنك تغيير اسمك بنفسك! اطلب من مشرف آخر.');
      }
      
      // التحقق من ترتيب الرتب
      if (targetMember.roles.highest.position >= message.member.roles.highest.position && 
          message.author.id !== message.guild.ownerId) {
        return await message.reply('❌ لا يمكنك تغيير اسم عضو له رتبة أعلى أو مساوية لرتبتك!');
      }
      
      // التحقق من ترتيب رتبة البوت
      if (targetMember.roles.highest.position >= message.guild.members.me.roles.highest.position) {
        return await message.reply('❌ لا يمكنني تغيير اسم عضو له رتبة أعلى من رتبة البوت!');
      }
      
      // تحديد الاسم الجديد
      const newNickname = args.slice(1).join(' ') || null;
      
      // التحقق من طول الاسم
      if (newNickname && newNickname.length > 32) {
        return await message.reply('❌ الاسم طويل جداً! الحد الأقصى 32 حرف.');
      }
      
      // التحقق من الأحرف المسموحة
      if (newNickname && /[^\w\s\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/.test(newNickname)) {
        return await message.reply('❌ الاسم يحتوي على أحرف غير مسموحة!');
      }
      
      // حفظ الاسم القديم
      const oldNickname = targetMember.nickname || targetMember.user.username;
      
      try {
        // تغيير الاسم
        await targetMember.setNickname(newNickname, `Nickname changed by ${message.author.tag}`);
        
        // إنشاء embed النجاح
        const successEmbed = new EmbedBuilder()
          .setColor('#00ff00')
          .setTitle('📝 تم تغيير الاسم بنجاح!')
          .addFields(
            {
              name: '👤 العضو',
              value: `${targetMember}`,
              inline: true
            },
            {
              name: '📝 الاسم القديم',
              value: `\`${oldNickname}\``,
              inline: true
            },
            {
              name: '✨ الاسم الجديد',
              value: newNickname ? `\`${newNickname}\`` : '`الاسم الأصلي`',
              inline: true
            },
            {
              name: '👮 بواسطة',
              value: `${message.author}`,
              inline: true
            },
            {
              name: '⏰ الوقت',
              value: `<t:${Math.floor(Date.now() / 1000)}:R>`,
              inline: true
            }
          )
          .setThumbnail(targetMember.user.displayAvatarURL({ dynamic: true }))
          .setFooter({
            text: 'CS Discord Moderation System',
            iconURL: message.client.user.displayAvatarURL()
          })
          .setTimestamp();
        
        // إضافة وصف حسب العملية
        if (newNickname) {
          successEmbed.setDescription(`✅ تم تغيير اسم **${targetMember.user.username}** إلى **${newNickname}**`);
        } else {
          successEmbed.setDescription(`✅ تم إزالة اسم **${targetMember.user.username}** وإرجاعه للاسم الأصلي`);
        }
        
        await message.reply({ embeds: [successEmbed] });
        
        // إرسال لوج إذا كان مفعل
        const logChannel = message.guild.channels.cache.find(ch => ch.name === 'mod-logs');
        if (logChannel) {
          const logEmbed = new EmbedBuilder()
            .setColor('#0099ff')
            .setTitle('📝 سجل الإدارة - تغيير اسم')
            .addFields(
              {
                name: 'المشرف',
                value: `${message.author.tag} (${message.author.id})`,
                inline: true
              },
              {
                name: 'العضو',
                value: `${targetMember.user.tag} (${targetMember.id})`,
                inline: true
              },
              {
                name: 'الاسم القديم',
                value: oldNickname,
                inline: true
              },
              {
                name: 'الاسم الجديد',
                value: newNickname || 'الاسم الأصلي',
                inline: true
              }
            )
            .setTimestamp();
          
          await logChannel.send({ embeds: [logEmbed] });
        }
        
        // إرسال رسالة خاصة للعضو (اختياري)
        try {
          const dmEmbed = new EmbedBuilder()
            .setColor('#0099ff')
            .setTitle(`📝 تم تغيير اسمك في ${message.guild.name}`)
            .addFields(
              {
                name: 'الاسم القديم',
                value: oldNickname,
                inline: true
              },
              {
                name: 'الاسم الجديد',
                value: newNickname || 'الاسم الأصلي',
                inline: true
              },
              {
                name: 'بواسطة',
                value: message.author.tag,
                inline: true
              }
            )
            .setTimestamp();
          
          await targetMember.send({ embeds: [dmEmbed] });
        } catch (error) {
          // تجاهل خطأ الرسائل الخاصة إذا كانت مغلقة
        }
        
      } catch (error) {
        console.error('خطأ في تغيير الاسم:', error);
        
        let errorMessage = '❌ حدث خطأ أثناء تغيير الاسم!';
        
        if (error.code === 50013) {
          errorMessage = '❌ ليس لدي صلاحية كافية لتغيير اسم هذا العضو!';
        } else if (error.code === 50001) {
          errorMessage = '❌ ليس لدي وصول لهذا العضو!';
        } else if (error.code === 50035) {
          errorMessage = '❌ الاسم غير صالح!';
        }
        
        await message.reply(errorMessage);
      }
      
    } catch (error) {
      console.error('خطأ في أمر تغيير الاسم:', error);
      await message.reply('❌ حدث خطأ أثناء تنفيذ الأمر!');
    }
  }
};
