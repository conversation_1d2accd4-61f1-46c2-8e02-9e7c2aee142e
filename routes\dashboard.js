const express = require('express');
const router = express.Router();
const { requireAuth, requireGuildAccess } = require('../middleware/auth');
const { GuildSettings, ModerationLogs, Tickets, XPLevels } = require('../models');

// التحقق من تسجيل الدخول لجميع مسارات لوحة التحكم
router.use(requireAuth);

// الصفحة الرئيسية للوحة التحكم
router.get('/', async (req, res) => {
  try {
    // الحصول على الخوادم التي يمكن للمستخدم الوصول إليها
    const userGuilds = req.user.permissions.guilds || [];
    
    // إحصائيات سريعة
    const stats = {
      totalGuilds: userGuilds.length,
      totalTickets: 0,
      totalWarnings: 0,
      totalUsers: 0
    };

    // حساب الإحصائيات للخوادم المتاحة
    for (const guild of userGuilds) {
      try {
        const [tickets, warnings] = await Promise.all([
          Tickets.countDocuments({ 
            guildId: guild.guildId, 
            status: { $in: ['open', 'pending'] } 
          }),
          ModerationLogs.countDocuments({ 
            guildId: guild.guildId, 
            action: 'warn',
            status: 'active'
          })
        ]);
        
        stats.totalTickets += tickets;
        stats.totalWarnings += warnings;
      } catch (error) {
        console.error(`خطأ في حساب إحصائيات الخادم ${guild.guildId}:`, error);
      }
    }

    res.render('dashboard/index', {
      title: 'لوحة التحكم الرئيسية',
      userGuilds,
      stats,
      recentActivity: [] // سيتم إضافة النشاطات الأخيرة لاحقاً
    });
  } catch (error) {
    console.error('خطأ في تحميل لوحة التحكم:', error);
    res.status(500).render('errors/500', {
      title: 'خطأ في الخادم',
      message: 'حدث خطأ أثناء تحميل لوحة التحكم'
    });
  }
});

// اختيار الخادم
router.get('/select-guild', (req, res) => {
  const userGuilds = req.user.permissions.guilds || [];
  
  res.render('dashboard/select-guild', {
    title: 'اختيار الخادم',
    userGuilds
  });
});

// لوحة تحكم خادم محدد
router.get('/guild/:guildId', requireGuildAccess, async (req, res) => {
  try {
    const { guildId } = req.params;
    
    // الحصول على إعدادات الخادم
    let guildSettings = await GuildSettings.findOne({ guildId });
    if (!guildSettings) {
      guildSettings = new GuildSettings({ guildId });
      await guildSettings.save();
    }

    // إحصائيات الخادم
    const [
      totalMembers,
      openTickets,
      activeWarnings,
      totalXP
    ] = await Promise.all([
      // سيتم ربطها مع بوت Discord لاحقاً
      Promise.resolve(0),
      Tickets.countDocuments({ 
        guildId, 
        status: { $in: ['open', 'pending'] } 
      }),
      ModerationLogs.countDocuments({ 
        guildId, 
        action: 'warn',
        status: 'active'
      }),
      XPLevels.countDocuments({ guildId })
    ]);

    const guildStats = {
      totalMembers,
      openTickets,
      activeWarnings,
      totalXP,
      botStatus: 'online' // سيتم ربطها مع البوت لاحقاً
    };

    // النشاطات الأخيرة
    const recentLogs = await ModerationLogs.find({ guildId })
      .sort({ createdAt: -1 })
      .limit(10);

    res.render('dashboard/guild', {
      title: `لوحة تحكم الخادم`,
      guildId,
      guildSettings,
      guildStats,
      recentLogs
    });
  } catch (error) {
    console.error('خطأ في تحميل لوحة تحكم الخادم:', error);
    res.status(500).render('errors/500', {
      title: 'خطأ في الخادم',
      message: 'حدث خطأ أثناء تحميل بيانات الخادم'
    });
  }
});

// الإعدادات العامة
router.get('/guild/:guildId/settings', requireGuildAccess, async (req, res) => {
  try {
    const { guildId } = req.params;
    
    let guildSettings = await GuildSettings.findOne({ guildId });
    if (!guildSettings) {
      guildSettings = new GuildSettings({ guildId });
      await guildSettings.save();
    }

    res.render('dashboard/settings/general', {
      title: 'الإعدادات العامة',
      guildId,
      settings: guildSettings
    });
  } catch (error) {
    console.error('خطأ في تحميل الإعدادات:', error);
    res.status(500).render('errors/500', {
      title: 'خطأ في الخادم',
      message: 'حدث خطأ أثناء تحميل الإعدادات'
    });
  }
});

// إعدادات الإدارة والمودريشن
router.get('/guild/:guildId/moderation', requireGuildAccess, async (req, res) => {
  try {
    const { guildId } = req.params;
    
    const guildSettings = await GuildSettings.findOne({ guildId });
    const moderationLogs = await ModerationLogs.find({ guildId })
      .sort({ createdAt: -1 })
      .limit(50);

    res.render('dashboard/moderation', {
      title: 'إدارة المودريشن',
      guildId,
      settings: guildSettings?.moderation || {},
      logs: moderationLogs
    });
  } catch (error) {
    console.error('خطأ في تحميل إعدادات المودريشن:', error);
    res.status(500).render('errors/500', {
      title: 'خطأ في الخادم',
      message: 'حدث خطأ أثناء تحميل إعدادات المودريشن'
    });
  }
});

// إعدادات التذاكر
router.get('/guild/:guildId/tickets', requireGuildAccess, async (req, res) => {
  try {
    const { guildId } = req.params;
    
    const guildSettings = await GuildSettings.findOne({ guildId });
    const tickets = await Tickets.find({ guildId })
      .sort({ createdAt: -1 })
      .limit(50);

    res.render('dashboard/tickets', {
      title: 'نظام التذاكر',
      guildId,
      settings: guildSettings?.tickets || {},
      tickets
    });
  } catch (error) {
    console.error('خطأ في تحميل إعدادات التذاكر:', error);
    res.status(500).render('errors/500', {
      title: 'خطأ في الخادم',
      message: 'حدث خطأ أثناء تحميل إعدادات التذاكر'
    });
  }
});

// إعدادات الترحيب والمغادرة
router.get('/guild/:guildId/welcome', requireGuildAccess, async (req, res) => {
  try {
    const { guildId } = req.params;
    
    const guildSettings = await GuildSettings.findOne({ guildId });

    res.render('dashboard/welcome', {
      title: 'إعدادات الترحيب والمغادرة',
      guildId,
      welcomeSettings: guildSettings?.welcome || {},
      leaveSettings: guildSettings?.leave || {}
    });
  } catch (error) {
    console.error('خطأ في تحميل إعدادات الترحيب:', error);
    res.status(500).render('errors/500', {
      title: 'خطأ في الخادم',
      message: 'حدث خطأ أثناء تحميل إعدادات الترحيب'
    });
  }
});

// نظام المستويات والـ XP
router.get('/guild/:guildId/leveling', requireGuildAccess, async (req, res) => {
  try {
    const { guildId } = req.params;
    
    const guildSettings = await GuildSettings.findOne({ guildId });
    const topUsers = await XPLevels.getLeaderboard(guildId, 'total', 10);

    res.render('dashboard/leveling', {
      title: 'نظام المستويات والـ XP',
      guildId,
      settings: guildSettings?.leveling || {},
      topUsers
    });
  } catch (error) {
    console.error('خطأ في تحميل نظام المستويات:', error);
    res.status(500).render('errors/500', {
      title: 'خطأ في الخادم',
      message: 'حدث خطأ أثناء تحميل نظام المستويات'
    });
  }
});

// سجلات النظام
router.get('/guild/:guildId/logs', requireGuildAccess, async (req, res) => {
  try {
    const { guildId } = req.params;
    const { type = 'all', page = 1 } = req.query;
    const limit = 20;
    const skip = (page - 1) * limit;

    let query = { guildId };
    if (type !== 'all') {
      query.action = type;
    }

    const [logs, totalLogs] = await Promise.all([
      ModerationLogs.find(query)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit),
      ModerationLogs.countDocuments(query)
    ]);

    const totalPages = Math.ceil(totalLogs / limit);

    res.render('dashboard/logs', {
      title: 'سجلات النظام',
      guildId,
      logs,
      currentPage: parseInt(page),
      totalPages,
      currentType: type
    });
  } catch (error) {
    console.error('خطأ في تحميل السجلات:', error);
    res.status(500).render('errors/500', {
      title: 'خطأ في الخادم',
      message: 'حدث خطأ أثناء تحميل السجلات'
    });
  }
});

// صفحة إعدادات الخادم الجديدة
router.get('/server-settings', async (req, res) => {
  try {
    res.render('dashboard/server-settings', {
      title: 'إعدادات الخادم'
    });
  } catch (error) {
    console.error('خطأ في صفحة إعدادات الخادم:', error);
    res.status(500).render('errors/500', {
      title: 'خطأ في الخادم',
      message: 'حدث خطأ أثناء تحميل إعدادات الخادم'
    });
  }
});

// صفحة الإعدادات العامة الجديدة
router.get('/general', async (req, res) => {
  try {
    res.render('dashboard/general', {
      title: 'الإعدادات العامة'
    });
  } catch (error) {
    console.error('خطأ في صفحة الإعدادات العامة:', error);
    res.status(500).render('errors/500', {
      title: 'خطأ في الخادم',
      message: 'حدث خطأ أثناء تحميل الإعدادات العامة'
    });
  }
});

// صفحة الردود التلقائية الجديدة
router.get('/autoresponses', async (req, res) => {
  try {
    res.render('dashboard/autoresponses', {
      title: 'الردود التلقائية'
    });
  } catch (error) {
    console.error('خطأ في صفحة الردود التلقائية:', error);
    res.status(500).render('errors/500', {
      title: 'خطأ في الخادم',
      message: 'حدث خطأ أثناء تحميل الردود التلقائية'
    });
  }
});

// صفحة نظام العملات
router.get('/economy', async (req, res) => {
  try {
    res.render('dashboard/economy', {
      title: 'نظام العملات'
    });
  } catch (error) {
    console.error('خطأ في صفحة نظام العملات:', error);
    res.status(500).render('errors/500', {
      title: 'خطأ في الخادم',
      message: 'حدث خطأ أثناء تحميل نظام العملات'
    });
  }
});

// صفحة منشئ الأوامر المخصصة
router.get('/command-builder', async (req, res) => {
  try {
    res.render('dashboard/command-builder', {
      title: 'منشئ الأوامر المخصصة'
    });
  } catch (error) {
    console.error('خطأ في صفحة منشئ الأوامر:', error);
    res.status(500).render('errors/500', {
      title: 'خطأ في الخادم',
      message: 'حدث خطأ أثناء تحميل منشئ الأوامر'
    });
  }
});

// صفحة نظام الحماية التلقائية
router.get('/automod', async (req, res) => {
  try {
    res.render('dashboard/automod', {
      title: 'نظام الحماية التلقائية'
    });
  } catch (error) {
    console.error('خطأ في صفحة الحماية التلقائية:', error);
    res.status(500).render('errors/500', {
      title: 'خطأ في الخادم',
      message: 'حدث خطأ أثناء تحميل نظام الحماية التلقائية'
    });
  }
});

// صفحة إدارة الرتب
router.get('/roles', async (req, res) => {
  try {
    res.render('dashboard/roles', {
      title: 'إدارة الرتب'
    });
  } catch (error) {
    console.error('خطأ في صفحة إدارة الرتب:', error);
    res.status(500).render('errors/500', {
      title: 'خطأ في الخادم',
      message: 'حدث خطأ أثناء تحميل إدارة الرتب'
    });
  }
});

// صفحة الاستطلاعات
router.get('/polls', async (req, res) => {
  try {
    res.render('dashboard/polls', {
      title: 'نظام الاستطلاعات'
    });
  } catch (error) {
    console.error('خطأ في صفحة الاستطلاعات:', error);
    res.status(500).render('errors/500', {
      title: 'خطأ في الخادم',
      message: 'حدث خطأ أثناء تحميل الاستطلاعات'
    });
  }
});

// صفحة المسابقات
router.get('/giveaways', async (req, res) => {
  try {
    res.render('dashboard/giveaways', {
      title: 'إدارة المسابقات'
    });
  } catch (error) {
    console.error('خطأ في صفحة المسابقات:', error);
    res.status(500).render('errors/500', {
      title: 'خطأ في الخادم',
      message: 'حدث خطأ أثناء تحميل المسابقات'
    });
  }
});



// صفحة السجلات
router.get('/logs', async (req, res) => {
  try {
    res.render('dashboard/logs', {
      title: 'سجلات الخادم'
    });
  } catch (error) {
    console.error('خطأ في صفحة السجلات:', error);
    res.status(500).render('errors/500', {
      title: 'خطأ في الخادم',
      message: 'حدث خطأ أثناء تحميل السجلات'
    });
  }
});

// صفحة البلوكات
router.get('/blocks', async (req, res) => {
  try {
    res.render('dashboard/blocks', {
      title: 'نظام البلوكات'
    });
  } catch (error) {
    console.error('خطأ في صفحة البلوكات:', error);
    res.status(500).render('errors/500', {
      title: 'خطأ في الخادم',
      message: 'حدث خطأ أثناء تحميل البلوكات'
    });
  }
});

// صفحة الأدوات المتقدمة
router.get('/tools', async (req, res) => {
  try {
    res.render('dashboard/tools', {
      title: 'الأدوات المتقدمة'
    });
  } catch (error) {
    console.error('خطأ في صفحة الأدوات:', error);
    res.status(500).render('errors/500', {
      title: 'خطأ في الخادم',
      message: 'حدث خطأ أثناء تحميل الأدوات'
    });
  }
});

// صفحة إدارة Premium (للمالك فقط)
router.get('/premium', async (req, res) => {
  try {
    // التحقق من أن المستخدم هو المالك
    if (req.user.id !== '761218404833034251') {
      return res.status(403).render('errors/404', {
        title: '403 - غير مسموح'
      });
    }

    res.render('dashboard/premium', {
      title: 'إدارة Premium'
    });
  } catch (error) {
    console.error('خطأ في صفحة Premium:', error);
    res.status(500).render('errors/500', {
      title: 'خطأ في الخادم',
      message: 'حدث خطأ أثناء تحميل إدارة Premium'
    });
  }
});

// صفحة التحليلات المتقدمة (للمالك فقط)
router.get('/analytics', async (req, res) => {
  try {
    if (req.user.id !== '761218404833034251') {
      return res.status(403).render('errors/404', {
        title: '403 - غير مسموح'
      });
    }

    res.render('dashboard/analytics', {
      title: 'التحليلات المتقدمة'
    });
  } catch (error) {
    console.error('خطأ في صفحة التحليلات:', error);
    res.status(500).render('errors/500', {
      title: 'خطأ في الخادم',
      message: 'حدث خطأ أثناء تحميل التحليلات'
    });
  }
});

// صفحة النسخ الاحتياطي (للمالك فقط)
router.get('/backup', async (req, res) => {
  try {
    if (req.user.id !== '761218404833034251') {
      return res.status(403).render('errors/404', {
        title: '403 - غير مسموح'
      });
    }

    res.render('dashboard/backup', {
      title: 'النسخ الاحتياطي'
    });
  } catch (error) {
    console.error('خطأ في صفحة النسخ الاحتياطي:', error);
    res.status(500).render('errors/500', {
      title: 'خطأ في الخادم',
      message: 'حدث خطأ أثناء تحميل النسخ الاحتياطي'
    });
  }
});

// صفحة إدارة النظام (للمالك فقط)
router.get('/system', async (req, res) => {
  try {
    if (req.user.id !== '761218404833034251') {
      return res.status(403).render('errors/404', {
        title: '403 - غير مسموح'
      });
    }

    res.render('dashboard/system', {
      title: 'إدارة النظام'
    });
  } catch (error) {
    console.error('خطأ في صفحة إدارة النظام:', error);
    res.status(500).render('errors/500', {
      title: 'خطأ في الخادم',
      message: 'حدث خطأ أثناء تحميل إدارة النظام'
    });
  }
});

// صفحة إدارة Premium (للمالك فقط)
router.get('/premium', async (req, res) => {
  try {
    const config = require('../config');

    // التحقق من أن المستخدم هو المالك
    if (req.user.id !== config.bot.ownerId) {
      return res.status(403).render('errors/403', {
        title: 'غير مسموح',
        message: 'هذه الصفحة متاحة للمالك فقط'
      });
    }

    res.render('dashboard/premium', {
      title: 'إدارة Premium'
    });
  } catch (error) {
    console.error('خطأ في صفحة Premium:', error);
    res.status(500).render('errors/500', {
      title: 'خطأ في الخادم',
      message: 'حدث خطأ أثناء تحميل صفحة Premium'
    });
  }
});

// صفحة إعدادات البوت (للمالك فقط)
router.get('/bot-settings', async (req, res) => {
  try {
    const config = require('../config');

    if (req.user.id !== config.bot.ownerId) {
      return res.status(403).render('errors/403', {
        title: 'غير مسموح',
        message: 'هذه الصفحة متاحة للمالك فقط'
      });
    }

    res.render('dashboard/bot-settings', {
      title: 'إعدادات البوت المتقدمة'
    });
  } catch (error) {
    console.error('خطأ في صفحة إعدادات البوت:', error);
    res.status(500).render('errors/500', {
      title: 'خطأ في الخادم',
      message: 'حدث خطأ أثناء تحميل إعدادات البوت'
    });
  }
});

// صفحة التحليلات المتقدمة (للمالك فقط)
router.get('/analytics', async (req, res) => {
  try {
    const config = require('../config');

    if (req.user.id !== config.bot.ownerId) {
      return res.status(403).render('errors/403', {
        title: 'غير مسموح',
        message: 'هذه الصفحة متاحة للمالك فقط'
      });
    }

    res.render('dashboard/analytics', {
      title: 'التحليلات المتقدمة'
    });
  } catch (error) {
    console.error('خطأ في صفحة التحليلات:', error);
    res.status(500).render('errors/500', {
      title: 'خطأ في الخادم',
      message: 'حدث خطأ أثناء تحميل التحليلات'
    });
  }
});

module.exports = router;
