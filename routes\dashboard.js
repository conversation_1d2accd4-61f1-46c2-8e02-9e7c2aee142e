const express = require('express');
const router = express.Router();
const { requireAuth, requireGuildAccess } = require('../middleware/auth');
const { GuildSettings, ModerationLogs, Tickets, XPLevels } = require('../models');

// التحقق من تسجيل الدخول لجميع مسارات لوحة التحكم
router.use(requireAuth);

// الصفحة الرئيسية للوحة التحكم
router.get('/', async (req, res) => {
  try {
    // الحصول على الخوادم التي يمكن للمستخدم الوصول إليها
    const userGuilds = req.user.permissions.guilds || [];
    
    // إحصائيات سريعة
    const stats = {
      totalGuilds: userGuilds.length,
      totalTickets: 0,
      totalWarnings: 0,
      totalUsers: 0
    };

    // حساب الإحصائيات للخوادم المتاحة
    for (const guild of userGuilds) {
      try {
        const [tickets, warnings] = await Promise.all([
          Tickets.countDocuments({ 
            guildId: guild.guildId, 
            status: { $in: ['open', 'pending'] } 
          }),
          ModerationLogs.countDocuments({ 
            guildId: guild.guildId, 
            action: 'warn',
            status: 'active'
          })
        ]);
        
        stats.totalTickets += tickets;
        stats.totalWarnings += warnings;
      } catch (error) {
        console.error(`خطأ في حساب إحصائيات الخادم ${guild.guildId}:`, error);
      }
    }

    res.render('dashboard/index', {
      title: 'لوحة التحكم الرئيسية',
      userGuilds,
      stats,
      recentActivity: [] // سيتم إضافة النشاطات الأخيرة لاحقاً
    });
  } catch (error) {
    console.error('خطأ في تحميل لوحة التحكم:', error);
    res.status(500).render('errors/500', {
      title: 'خطأ في الخادم',
      message: 'حدث خطأ أثناء تحميل لوحة التحكم'
    });
  }
});

// اختيار الخادم
router.get('/select-guild', (req, res) => {
  const userGuilds = req.user.permissions.guilds || [];
  
  res.render('dashboard/select-guild', {
    title: 'اختيار الخادم',
    userGuilds
  });
});

// لوحة تحكم خادم محدد
router.get('/guild/:guildId', requireGuildAccess, async (req, res) => {
  try {
    const { guildId } = req.params;
    
    // الحصول على إعدادات الخادم
    let guildSettings = await GuildSettings.findOne({ guildId });
    if (!guildSettings) {
      guildSettings = new GuildSettings({ guildId });
      await guildSettings.save();
    }

    // إحصائيات الخادم
    const [
      totalMembers,
      openTickets,
      activeWarnings,
      totalXP
    ] = await Promise.all([
      // سيتم ربطها مع بوت Discord لاحقاً
      Promise.resolve(0),
      Tickets.countDocuments({ 
        guildId, 
        status: { $in: ['open', 'pending'] } 
      }),
      ModerationLogs.countDocuments({ 
        guildId, 
        action: 'warn',
        status: 'active'
      }),
      XPLevels.countDocuments({ guildId })
    ]);

    const guildStats = {
      totalMembers,
      openTickets,
      activeWarnings,
      totalXP,
      botStatus: 'online' // سيتم ربطها مع البوت لاحقاً
    };

    // النشاطات الأخيرة
    const recentLogs = await ModerationLogs.find({ guildId })
      .sort({ createdAt: -1 })
      .limit(10);

    res.render('dashboard/guild', {
      title: `لوحة تحكم الخادم`,
      guildId,
      guildSettings,
      guildStats,
      recentLogs
    });
  } catch (error) {
    console.error('خطأ في تحميل لوحة تحكم الخادم:', error);
    res.status(500).render('errors/500', {
      title: 'خطأ في الخادم',
      message: 'حدث خطأ أثناء تحميل بيانات الخادم'
    });
  }
});

// الإعدادات العامة
router.get('/guild/:guildId/settings', requireGuildAccess, async (req, res) => {
  try {
    const { guildId } = req.params;
    
    let guildSettings = await GuildSettings.findOne({ guildId });
    if (!guildSettings) {
      guildSettings = new GuildSettings({ guildId });
      await guildSettings.save();
    }

    res.render('dashboard/settings/general', {
      title: 'الإعدادات العامة',
      guildId,
      settings: guildSettings
    });
  } catch (error) {
    console.error('خطأ في تحميل الإعدادات:', error);
    res.status(500).render('errors/500', {
      title: 'خطأ في الخادم',
      message: 'حدث خطأ أثناء تحميل الإعدادات'
    });
  }
});

// إعدادات الإدارة والمودريشن
router.get('/guild/:guildId/moderation', requireGuildAccess, async (req, res) => {
  try {
    const { guildId } = req.params;
    
    const guildSettings = await GuildSettings.findOne({ guildId });
    const moderationLogs = await ModerationLogs.find({ guildId })
      .sort({ createdAt: -1 })
      .limit(50);

    res.render('dashboard/moderation', {
      title: 'إدارة المودريشن',
      guildId,
      settings: guildSettings?.moderation || {},
      logs: moderationLogs
    });
  } catch (error) {
    console.error('خطأ في تحميل إعدادات المودريشن:', error);
    res.status(500).render('errors/500', {
      title: 'خطأ في الخادم',
      message: 'حدث خطأ أثناء تحميل إعدادات المودريشن'
    });
  }
});

// إعدادات التذاكر
router.get('/guild/:guildId/tickets', requireGuildAccess, async (req, res) => {
  try {
    const { guildId } = req.params;
    
    const guildSettings = await GuildSettings.findOne({ guildId });
    const tickets = await Tickets.find({ guildId })
      .sort({ createdAt: -1 })
      .limit(50);

    res.render('dashboard/tickets', {
      title: 'نظام التذاكر',
      guildId,
      settings: guildSettings?.tickets || {},
      tickets
    });
  } catch (error) {
    console.error('خطأ في تحميل إعدادات التذاكر:', error);
    res.status(500).render('errors/500', {
      title: 'خطأ في الخادم',
      message: 'حدث خطأ أثناء تحميل إعدادات التذاكر'
    });
  }
});

// إعدادات الترحيب والمغادرة
router.get('/guild/:guildId/welcome', requireGuildAccess, async (req, res) => {
  try {
    const { guildId } = req.params;
    
    const guildSettings = await GuildSettings.findOne({ guildId });

    res.render('dashboard/welcome', {
      title: 'إعدادات الترحيب والمغادرة',
      guildId,
      welcomeSettings: guildSettings?.welcome || {},
      leaveSettings: guildSettings?.leave || {}
    });
  } catch (error) {
    console.error('خطأ في تحميل إعدادات الترحيب:', error);
    res.status(500).render('errors/500', {
      title: 'خطأ في الخادم',
      message: 'حدث خطأ أثناء تحميل إعدادات الترحيب'
    });
  }
});

// نظام المستويات والـ XP
router.get('/guild/:guildId/leveling', requireGuildAccess, async (req, res) => {
  try {
    const { guildId } = req.params;
    
    const guildSettings = await GuildSettings.findOne({ guildId });
    const topUsers = await XPLevels.getLeaderboard(guildId, 'total', 10);

    res.render('dashboard/leveling', {
      title: 'نظام المستويات والـ XP',
      guildId,
      settings: guildSettings?.leveling || {},
      topUsers
    });
  } catch (error) {
    console.error('خطأ في تحميل نظام المستويات:', error);
    res.status(500).render('errors/500', {
      title: 'خطأ في الخادم',
      message: 'حدث خطأ أثناء تحميل نظام المستويات'
    });
  }
});

// سجلات النظام
router.get('/guild/:guildId/logs', requireGuildAccess, async (req, res) => {
  try {
    const { guildId } = req.params;
    const { type = 'all', page = 1 } = req.query;
    const limit = 20;
    const skip = (page - 1) * limit;

    let query = { guildId };
    if (type !== 'all') {
      query.action = type;
    }

    const [logs, totalLogs] = await Promise.all([
      ModerationLogs.find(query)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit),
      ModerationLogs.countDocuments(query)
    ]);

    const totalPages = Math.ceil(totalLogs / limit);

    res.render('dashboard/logs', {
      title: 'سجلات النظام',
      guildId,
      logs,
      currentPage: parseInt(page),
      totalPages,
      currentType: type
    });
  } catch (error) {
    console.error('خطأ في تحميل السجلات:', error);
    res.status(500).render('errors/500', {
      title: 'خطأ في الخادم',
      message: 'حدث خطأ أثناء تحميل السجلات'
    });
  }
});

module.exports = router;
