<%- include('../layout', { body: `
<div class="dashboard-overview">
    <!-- Welcome Section -->
    <div class="welcome-section mb-4">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="welcome-title">
                    <i class="fas fa-home"></i>
                    مرحباً بك، ${user.username}!
                </h1>
                <p class="welcome-subtitle">
                    إليك نظرة سريعة على حالة خوادمك وإحصائياتها
                </p>
            </div>
            <div class="col-md-4 text-end">
                <div class="quick-actions">
                    <a href="/dashboard/select-guild" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        إدارة خادم
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="stats-grid mb-5">
        <div class="row g-4">
            <div class="col-lg-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-icon servers">
                        <i class="fas fa-server"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number" id="server-count">جاري التحميل...</h3>
                        <p class="stat-label">الخوادم المتاحة</p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-icon tickets">
                        <i class="fas fa-ticket-alt"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number" id="user-count">جاري التحميل...</h3>
                        <p class="stat-label">إجمالي المستخدمين</p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-icon warnings">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number" id="channel-count">جاري التحميل...</h3>
                        <p class="stat-label">إجمالي القنوات</p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-icon users">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number" id="uptime">جاري التحميل...</h3>
                        <p class="stat-label">وقت التشغيل</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="row g-4">
        <!-- Guilds List -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-server"></i>
                        خوادمك
                    </h5>
                </div>
                <div class="card-body">
                    ${userGuilds.length > 0 ? `
                        <div class="guilds-grid">
                            ${userGuilds.map(guild => `
                                <div class="guild-card">
                                    <div class="guild-info">
                                        <div class="guild-avatar">
                                            <i class="fas fa-server"></i>
                                        </div>
                                        <div class="guild-details">
                                            <h6 class="guild-name">${guild.guildName || guild.guildId}</h6>
                                            <p class="guild-id">ID: ${guild.guildId}</p>
                                            <div class="guild-permissions">
                                                ${guild.permissions.owner ? '<span class="badge bg-warning">مالك</span>' : ''}
                                                ${guild.permissions.admin ? '<span class="badge bg-danger">مدير</span>' : ''}
                                                ${guild.permissions.moderator ? '<span class="badge bg-info">مشرف</span>' : ''}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="guild-actions">
                                        <a href="/dashboard/guild/${guild.guildId}" class="btn btn-sm btn-primary">
                                            <i class="fas fa-cog"></i>
                                            إدارة
                                        </a>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    ` : `
                        <div class="empty-state">
                            <div class="empty-icon">
                                <i class="fas fa-server"></i>
                            </div>
                            <h5>لا توجد خوادم متاحة</h5>
                            <p>لم يتم منحك صلاحيات لإدارة أي خادم بعد.</p>
                            <a href="https://discord.com/api/oauth2/authorize?client_id=${config.bot.clientId}&permissions=8&scope=bot%20applications.commands" 
                               class="btn btn-primary" target="_blank">
                                <i class="fas fa-plus"></i>
                                إضافة البوت لخادمك
                            </a>
                        </div>
                    `}
                </div>
            </div>
        </div>

        <!-- Quick Actions & Info -->
        <div class="col-lg-4">
            <!-- Bot Status -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-robot"></i>
                        حالة البوت
                    </h5>
                </div>
                <div class="card-body">
                    <div class="bot-status">
                        <div class="status-indicator online">
                            <span class="status-dot"></span>
                            <span class="status-text">متصل</span>
                        </div>
                        <div class="bot-info mt-3">
                            <div class="info-item">
                                <span class="info-label">وقت التشغيل:</span>
                                <span class="info-value">24/7</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">الإصدار:</span>
                                <span class="info-value">v1.0.0</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">آخر تحديث:</span>
                                <span class="info-value">منذ ساعة</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bolt"></i>
                        إجراءات سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="quick-actions-list">
                        <a href="/dashboard/select-guild" class="quick-action-item">
                            <div class="action-icon">
                                <i class="fas fa-server"></i>
                            </div>
                            <div class="action-content">
                                <h6>إدارة خادم</h6>
                                <p>اختر خادم لإدارته</p>
                            </div>
                            <i class="fas fa-chevron-left"></i>
                        </a>
                        
                        <a href="https://discord.com/api/oauth2/authorize?client_id=${config.bot.clientId}&permissions=8&scope=bot%20applications.commands" 
                           class="quick-action-item" target="_blank">
                            <div class="action-icon">
                                <i class="fas fa-plus"></i>
                            </div>
                            <div class="action-content">
                                <h6>إضافة البوت</h6>
                                <p>أضف البوت لخادم جديد</p>
                            </div>
                            <i class="fas fa-external-link-alt"></i>
                        </a>
                        
                        <a href="#" class="quick-action-item" onclick="showSupportModal()">
                            <div class="action-icon">
                                <i class="fas fa-life-ring"></i>
                            </div>
                            <div class="action-content">
                                <h6>الدعم الفني</h6>
                                <p>احصل على مساعدة</p>
                            </div>
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.welcome-section {
    background: linear-gradient(135deg, rgba(114, 137, 218, 0.1) 0%, rgba(153, 170, 181, 0.1) 100%);
    border-radius: 15px;
    padding: 30px;
    border: 1px solid rgba(114, 137, 218, 0.2);
}

.welcome-title {
    font-size: 2rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 10px;
}

.welcome-subtitle {
    color: #b9bbbe;
    font-size: 1.1rem;
    margin: 0;
}

.stats-grid .stat-card {
    background: #40444b;
    border-radius: 15px;
    padding: 25px;
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all 0.3s ease;
    border: 1px solid #2c2f33;
}

.stats-grid .stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-icon.servers { background: linear-gradient(135deg, #7289da, #99aab5); }
.stat-icon.tickets { background: linear-gradient(135deg, #43b581, #3ca374); }
.stat-icon.warnings { background: linear-gradient(135deg, #faa61a, #f57c00); }
.stat-icon.users { background: linear-gradient(135deg, #f04747, #d32f2f); }

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #ffffff;
    margin: 0;
}

.stat-label {
    color: #b9bbbe;
    margin: 0;
    font-size: 0.9rem;
}

.guilds-grid {
    display: grid;
    gap: 20px;
}

.guild-card {
    background: #36393f;
    border-radius: 12px;
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: all 0.3s ease;
    border: 1px solid #2c2f33;
}

.guild-card:hover {
    background: #3a3d44;
    transform: translateX(-5px);
}

.guild-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.guild-avatar {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #7289da, #99aab5);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.guild-name {
    color: #ffffff;
    font-weight: 600;
    margin: 0 0 5px 0;
}

.guild-id {
    color: #72767d;
    font-size: 0.85rem;
    margin: 0 0 8px 0;
}

.guild-permissions .badge {
    margin-left: 5px;
    font-size: 0.75rem;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
}

.empty-icon {
    width: 80px;
    height: 80px;
    background: #36393f;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 2rem;
    color: #72767d;
}

.bot-status .status-indicator {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #43b581;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(67, 181, 129, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(67, 181, 129, 0); }
    100% { box-shadow: 0 0 0 0 rgba(67, 181, 129, 0); }
}

.status-indicator.online .status-text { color: #43b581; }

.bot-info .info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.info-label {
    color: #b9bbbe;
    font-size: 0.9rem;
}

.info-value {
    color: #ffffff;
    font-weight: 600;
    font-size: 0.9rem;
}

.quick-actions-list .quick-action-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: #36393f;
    border-radius: 10px;
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
    margin-bottom: 10px;
    border: 1px solid #2c2f33;
}

.quick-action-item:hover {
    background: #3a3d44;
    transform: translateX(-3px);
    color: inherit;
}

.quick-action-item .action-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #7289da, #99aab5);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.quick-action-item .action-content h6 {
    color: #ffffff;
    margin: 0 0 3px 0;
    font-weight: 600;
}

.quick-action-item .action-content p {
    color: #b9bbbe;
    margin: 0;
    font-size: 0.85rem;
}

.quick-action-item > i {
    color: #72767d;
    margin-right: auto;
}
</style>

<script>
function showSupportModal() {
    alert('قريباً: نظام الدعم الفني');
}
</script>

<script src="/static/js/dashboard.js"></script>
` }) %>
