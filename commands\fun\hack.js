// ========================================
// 💻 أمر الهاك المزيف - CS Discord
// ========================================
// 📺 يوتيوب: https://www.youtube.com/@CS_Discord
// 💬 ديسكورد: https://discord.gg/yqTn2EwVsd
// ========================================

const { EmbedBuilder } = require('discord.js');

module.exports = {
  name: 'hack',
  aliases: ['هاك', 'اختراق', 'hack_user'],
  description: 'هاك مزيف لعضو (للمرح فقط!) 😂',
  usage: '<@المستخدم>',
  category: 'fun',
  cooldown: 10,
  
  async execute(message, args) {
    try {
      // التحقق من وجود المستخدم المذكور
      const targetUser = message.mentions.users.first();
      
      if (!targetUser) {
        return await message.reply('❌ يجب ذكر المستخدم الذي تريد "هاكه"!\nمثال: `hack @أحمد`');
      }
      
      // التحقق من عدم هاك النفس
      if (targetUser.id === message.author.id) {
        return await message.reply('😅 لا يمكنك هاك نفسك! هذا غير منطقي!');
      }
      
      // التحقق من عدم هاك البوت
      if (targetUser.bot) {
        return await message.reply('🤖 البوتات محمية بجدران نار قوية! لا يمكن اختراقها!');
      }
      
      // رسالة البداية
      const startEmbed = new EmbedBuilder()
        .setColor('#ff0000')
        .setTitle('💻 بدء عملية الاختراق...')
        .setDescription(`🎯 الهدف: **${targetUser.username}**\n⚡ جاري تهيئة الأدوات...`)
        .setFooter({
          text: 'تحذير: هذا مجرد مزح!',
          iconURL: message.client.user.displayAvatarURL()
        })
        .setTimestamp();
      
      const hackMessage = await message.reply({ embeds: [startEmbed] });
      
      // مراحل الهاك المزيف
      const hackStages = [
        '🔍 البحث عن نقاط الضعف...',
        '🔓 كسر كلمة المرور...',
        '📡 الوصول إلى الشبكة...',
        '💾 تحميل البيانات...',
        '🔐 فك التشفير...',
        '📱 الوصول إلى الهاتف...',
        '💳 العثور على معلومات البنك...',
        '🎮 اختراق حسابات الألعاب...',
        '📧 الوصول إلى الإيميل...',
        '✅ اكتمال الاختراق!'
      ];
      
      // تنفيذ مراحل الهاك
      for (let i = 0; i < hackStages.length; i++) {
        await new Promise(resolve => setTimeout(resolve, 2000)); // انتظار ثانيتين
        
        const progressBar = createProgressBar(i + 1, hackStages.length);
        const percentage = Math.round(((i + 1) / hackStages.length) * 100);
        
        const updateEmbed = new EmbedBuilder()
          .setColor(i === hackStages.length - 1 ? '#00ff00' : '#ffa500')
          .setTitle('💻 عملية الاختراق جارية...')
          .setDescription(`🎯 الهدف: **${targetUser.username}**\n\n${hackStages[i]}\n\n${progressBar} **${percentage}%**`)
          .setFooter({
            text: 'تحذير: هذا مجرد مزح!',
            iconURL: message.client.user.displayAvatarURL()
          })
          .setTimestamp();
        
        await hackMessage.edit({ embeds: [updateEmbed] });
      }
      
      // النتيجة النهائية المضحكة
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const fakeData = generateFakeData(targetUser);
      
      const resultEmbed = new EmbedBuilder()
        .setColor('#00ff00')
        .setTitle('🎉 تم الاختراق بنجاح!')
        .setDescription(`💻 تم اختراق **${targetUser.username}** بنجاح!\n\n**البيانات المسروقة:**`)
        .addFields(
          {
            name: '🔑 كلمة المرور',
            value: `\`${fakeData.password}\``,
            inline: true
          },
          {
            name: '💳 رقم البطاقة',
            value: `\`${fakeData.creditCard}\``,
            inline: true
          },
          {
            name: '📧 الإيميل',
            value: `\`${fakeData.email}\``,
            inline: true
          },
          {
            name: '📱 رقم الهاتف',
            value: `\`${fakeData.phone}\``,
            inline: true
          },
          {
            name: '🏠 العنوان',
            value: `\`${fakeData.address}\``,
            inline: true
          },
          {
            name: '💰 رصيد البنك',
            value: `\`${fakeData.bankBalance}\``,
            inline: true
          },
          {
            name: '🎮 ألعابه المفضلة',
            value: fakeData.games.join(', '),
            inline: false
          },
          {
            name: '🍕 طعامه المفضل',
            value: fakeData.favoriteFood,
            inline: true
          },
          {
            name: '🎵 أغنيته المفضلة',
            value: fakeData.favoriteSong,
            inline: true
          }
        )
        .setFooter({
          text: '😂 هذا مجرد مزح! لا تصدق أي شيء من هذا!',
          iconURL: message.client.user.displayAvatarURL()
        })
        .setTimestamp();
      
      await hackMessage.edit({ embeds: [resultEmbed] });
      
      // إضافة ردود فعل مضحكة
      const reactions = ['😂', '💻', '🔓', '🎯', '🤣'];
      for (const reaction of reactions) {
        try {
          await hackMessage.react(reaction);
          await new Promise(resolve => setTimeout(resolve, 500));
        } catch (error) {
          // تجاهل خطأ الردود
        }
      }
      
    } catch (error) {
      console.error('خطأ في أمر الهاك المزيف:', error);
      await message.reply('❌ حدث خطأ أثناء "الاختراق"! ربما الهدف محمي جداً! 😅');
    }
  }
};

// إنشاء شريط التقدم
function createProgressBar(current, total) {
  const percentage = current / total;
  const filledBars = Math.round(percentage * 20);
  const emptyBars = 20 - filledBars;
  
  return '█'.repeat(filledBars) + '░'.repeat(emptyBars);
}

// إنشاء بيانات مزيفة مضحكة
function generateFakeData(user) {
  const passwords = [
    '123456789',
    'password123',
    'ilovemom',
    'qwerty123',
    'admin123',
    'letmein',
    'welcome123',
    'monkey123'
  ];
  
  const foods = [
    '🍕 البيتزا',
    '🍔 البرجر',
    '🍝 المكرونة',
    '🍗 الدجاج المقلي',
    '🌮 التاكو',
    '🍜 الرامن',
    '🥙 الشاورما',
    '🍰 الكيك'
  ];
  
  const songs = [
    'Baby Shark',
    'Never Gonna Give You Up',
    'Gangnam Style',
    'Despacito',
    'Shape of You',
    'Blinding Lights',
    'Watermelon Sugar',
    'Levitating'
  ];
  
  const games = [
    ['Minecraft', 'Fortnite', 'Among Us'],
    ['PUBG', 'Call of Duty', 'Apex Legends'],
    ['League of Legends', 'Dota 2', 'Valorant'],
    ['FIFA', 'PES', 'Rocket League'],
    ['GTA V', 'Red Dead Redemption', 'Cyberpunk']
  ];
  
  const addresses = [
    'شارع الهرم، الجيزة',
    'شارع التحرير، القاهرة',
    'كورنيش النيل، الأقصر',
    'شارع الجمهورية، الإسكندرية',
    'شارع الملك فيصل، الرياض',
    'شارع الحمرا، بيروت',
    'شارع الرشيد، بغداد'
  ];
  
  return {
    password: passwords[Math.floor(Math.random() * passwords.length)],
    creditCard: `**** **** **** ${Math.floor(Math.random() * 9000) + 1000}`,
    email: `${user.username.toLowerCase()}${Math.floor(Math.random() * 999)}@gmail.com`,
    phone: `+966${Math.floor(Math.random() * *********) + *********}`,
    address: addresses[Math.floor(Math.random() * addresses.length)],
    bankBalance: `${(Math.random() * 1000000).toFixed(2)} ريال`,
    games: games[Math.floor(Math.random() * games.length)],
    favoriteFood: foods[Math.floor(Math.random() * foods.length)],
    favoriteSong: songs[Math.floor(Math.random() * songs.length)]
  };
}
