<%- include('../partials/header') %>

<div class="main-content">
    <div class="content-header">
        <h1 class="page-title">
            <i class="fas fa-gift"></i>
            إدارة المسابقات
        </h1>
        <p class="page-description">
            إنشاء وإدارة مسابقات تفاعلية للأعضاء
        </p>
    </div>

    <!-- أدوات سريعة -->
    <div class="quick-actions">
        <button class="btn btn-success" onclick="openCreateGiveawayModal()">
            <i class="fas fa-plus"></i>
            إنشاء مسابقة جديدة
        </button>
        <button class="btn btn-info" onclick="openTemplatesModal()">
            <i class="fas fa-layer-group"></i>
            قوالب المسابقات
        </button>
        <button class="btn btn-warning" onclick="showAnalytics()">
            <i class="fas fa-chart-pie"></i>
            تحليلات المسابقات
        </button>
        <button class="btn btn-secondary" onclick="exportGiveaways()">
            <i class="fas fa-download"></i>
            تصدير البيانات
        </button>
    </div>

    <!-- إحصائيات المسابقات -->
    <div class="stats-grid">
        <div class="stat-card active">
            <div class="stat-number" id="active-giveaways">2</div>
            <div class="stat-label">مسابقات نشطة</div>
        </div>
        <div class="stat-card completed">
            <div class="stat-number" id="completed-giveaways">15</div>
            <div class="stat-label">مسابقات مكتملة</div>
        </div>
        <div class="stat-card participants">
            <div class="stat-number" id="total-participants">1,247</div>
            <div class="stat-label">إجمالي المشاركين</div>
        </div>
        <div class="stat-card prizes">
            <div class="stat-number" id="total-prizes">45</div>
            <div class="stat-label">جوائز موزعة</div>
        </div>
    </div>

    <!-- قائمة المسابقات -->
    <div class="card">
        <div class="card-header">
            <div class="card-icon">
                <i class="fas fa-list"></i>
            </div>
            <div>
                <h3 class="card-title">المسابقات</h3>
            </div>
            <div class="card-actions">
                <select class="form-control form-control-sm" id="giveawayFilter" onchange="filterGiveaways()">
                    <option value="all">جميع المسابقات</option>
                    <option value="active">النشطة</option>
                    <option value="completed">المكتملة</option>
                    <option value="draft">المسودات</option>
                </select>
                <button class="btn btn-sm btn-primary" onclick="refreshGiveaways()">
                    <i class="fas fa-sync"></i>
                    تحديث
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="giveaways-container" id="giveaways-container">
                <div class="loading-spinner">
                    <i class="fas fa-spinner fa-spin"></i>
                    جاري تحميل المسابقات...
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal إنشاء مسابقة جديدة -->
<div class="modal fade" id="createGiveawayModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">🎁 إنشاء مسابقة جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createGiveawayForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">عنوان المسابقة</label>
                                <input type="text" class="form-control" id="giveawayTitle" placeholder="مثال: مسابقة نيترو ديسكورد" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الجائزة</label>
                                <input type="text" class="form-control" id="giveawayPrize" placeholder="مثال: Discord Nitro" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">وصف المسابقة</label>
                        <textarea class="form-control" id="giveawayDescription" rows="3" placeholder="وصف مختصر للمسابقة والجائزة..."></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">القناة</label>
                                <select class="form-control" id="giveawayChannel" required>
                                    <option value="">اختر القناة</option>
                                    <option value="general">العام</option>
                                    <option value="giveaways">المسابقات</option>
                                    <option value="announcements">الإعلانات</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">مدة المسابقة</label>
                                <select class="form-control" id="giveawayDuration">
                                    <option value="1">ساعة واحدة</option>
                                    <option value="6">6 ساعات</option>
                                    <option value="24" selected>24 ساعة</option>
                                    <option value="72">3 أيام</option>
                                    <option value="168">أسبوع</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">عدد الفائزين</label>
                                <input type="number" class="form-control" id="giveawayWinners" value="1" min="1" max="10">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الرتبة المطلوبة (اختياري)</label>
                                <select class="form-control" id="giveawayRole">
                                    <option value="">بدون رتبة محددة</option>
                                    <option value="member">عضو</option>
                                    <option value="vip">VIP</option>
                                    <option value="premium">Premium</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">شروط المشاركة</label>
                        <div class="requirements-list">
                            <div class="requirement-item">
                                <input type="checkbox" id="req_react" checked>
                                <label for="req_react">التفاعل مع الرسالة (🎉)</label>
                            </div>
                            <div class="requirement-item">
                                <input type="checkbox" id="req_follow">
                                <label for="req_follow">متابعة الخادم</label>
                            </div>
                            <div class="requirement-item">
                                <input type="checkbox" id="req_invite">
                                <label for="req_invite">دعوة صديق واحد</label>
                            </div>
                            <div class="requirement-item">
                                <input type="checkbox" id="req_message">
                                <label for="req_message">إرسال رسالة في الخادم</label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">إعدادات إضافية</label>
                        <div class="additional-settings">
                            <div class="setting-item">
                                <input type="checkbox" id="giveaway_ping">
                                <label for="giveaway_ping">تنبيه الجميع عند البدء</label>
                            </div>
                            <div class="setting-item">
                                <input type="checkbox" id="giveaway_dm">
                                <label for="giveaway_dm">إرسال رسالة خاصة للفائز</label>
                            </div>
                            <div class="setting-item">
                                <input type="checkbox" id="giveaway_reroll">
                                <label for="giveaway_reroll">السماح بإعادة السحب</label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">معاينة المسابقة</label>
                        <div class="giveaway-preview" id="giveawayPreview">
                            <div class="preview-header">
                                <h4>🎁 مسابقة جديدة!</h4>
                            </div>
                            <div class="preview-content">
                                <div class="preview-title">عنوان المسابقة</div>
                                <div class="preview-prize">🏆 الجائزة: جائزة رائعة</div>
                                <div class="preview-info">
                                    <div>👥 الفائزين: 1</div>
                                    <div>⏰ ينتهي في: 24 ساعة</div>
                                </div>
                                <div class="preview-reaction">
                                    تفاعل مع 🎉 للمشاركة!
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-info" onclick="saveGiveawayDraft()">
                    <i class="fas fa-save"></i>
                    حفظ كمسودة
                </button>
                <button type="button" class="btn btn-success" onclick="createGiveaway()">
                    <i class="fas fa-rocket"></i>
                    إطلاق المسابقة
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.quick-actions {
    display: flex;
    gap: 15px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.stat-card.active {
    background: linear-gradient(135deg, #00ff88, #00d4aa);
    color: white;
}

.stat-card.completed {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.stat-card.participants {
    background: linear-gradient(135deg, #feca57, #ff9ff3);
    color: white;
}

.stat-card.prizes {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 10px;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.giveaways-container {
    min-height: 300px;
}

.loading-spinner {
    text-align: center;
    padding: 50px;
    color: rgba(255, 255, 255, 0.6);
}

.giveaway-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 15px;
    border-left: 4px solid;
    transition: all 0.3s ease;
}

.giveaway-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.giveaway-item.active {
    border-left-color: #00ff88;
}

.giveaway-item.completed {
    border-left-color: #667eea;
}

.giveaway-item.draft {
    border-left-color: #feca57;
}

.giveaway-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.giveaway-title {
    font-size: 1.2rem;
    font-weight: bold;
    color: white;
    margin: 0;
}

.giveaway-status {
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: bold;
}

.giveaway-status.active {
    background: #00ff88;
    color: #333;
}

.giveaway-status.completed {
    background: #667eea;
    color: white;
}

.giveaway-status.draft {
    background: #feca57;
    color: #333;
}

.giveaway-prize {
    color: #feca57;
    font-weight: bold;
    margin-bottom: 10px;
}

.giveaway-description {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 15px;
}

.giveaway-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 10px;
    margin-bottom: 15px;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
}

.giveaway-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.requirements-list {
    background: rgba(255, 255, 255, 0.05);
    padding: 15px;
    border-radius: 10px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
}

.requirement-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.requirement-item input[type="checkbox"] {
    width: 16px;
    height: 16px;
}

.requirement-item label {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.9);
    cursor: pointer;
}

.additional-settings {
    background: rgba(255, 255, 255, 0.05);
    padding: 15px;
    border-radius: 10px;
}

.setting-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 10px;
}

.setting-item input[type="checkbox"] {
    width: 16px;
    height: 16px;
}

.setting-item label {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.9);
    cursor: pointer;
}

.giveaway-preview {
    background: rgba(255, 255, 255, 0.05);
    border: 2px dashed rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    padding: 20px;
    text-align: center;
}

.preview-header h4 {
    color: #feca57;
    margin-bottom: 15px;
}

.preview-title {
    font-size: 1.2rem;
    font-weight: bold;
    color: white;
    margin-bottom: 10px;
}

.preview-prize {
    color: #feca57;
    font-weight: bold;
    margin-bottom: 15px;
}

.preview-info {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 15px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
}

.preview-reaction {
    background: rgba(255, 255, 255, 0.1);
    padding: 10px;
    border-radius: 10px;
    color: #00ff88;
    font-weight: bold;
}

@media (max-width: 768px) {
    .quick-actions {
        flex-direction: column;
    }
    
    .giveaway-header {
        flex-direction: column;
        gap: 10px;
    }
    
    .giveaway-info {
        grid-template-columns: 1fr;
    }
    
    .giveaway-actions {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .preview-info {
        flex-direction: column;
        gap: 10px;
    }
}
</style>

<script src="/static/js/giveaways.js"></script>

<%- include('../partials/footer') %>
