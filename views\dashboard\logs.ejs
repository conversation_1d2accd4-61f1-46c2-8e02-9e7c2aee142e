<%- include('../partials/header') %>

<div class="main-content">
    <div class="content-header">
        <h1 class="page-title">
            <i class="fas fa-clipboard-list"></i>
            سجلات الخادم
        </h1>
        <p class="page-description">
            مراقبة وتتبع جميع أنشطة الخادم والأعضاء
        </p>
    </div>

    <!-- فلاتر السجلات -->
    <div class="logs-filters">
        <div class="filter-group">
            <label>نوع السجل:</label>
            <select class="form-control" id="logType" onchange="filterLogs()">
                <option value="all">جميع السجلات</option>
                <option value="moderation">الإشراف</option>
                <option value="member">الأعضاء</option>
                <option value="message">الرسائل</option>
                <option value="voice">الصوت</option>
                <option value="role">الرتب</option>
                <option value="channel">القنوات</option>
            </select>
        </div>
        
        <div class="filter-group">
            <label>التاريخ:</label>
            <select class="form-control" id="dateFilter" onchange="filterLogs()">
                <option value="today">اليوم</option>
                <option value="week">هذا الأسبوع</option>
                <option value="month">هذا الشهر</option>
                <option value="all">جميع الأوقات</option>
            </select>
        </div>
        
        <div class="filter-group">
            <label>البحث:</label>
            <input type="text" class="form-control" id="searchLogs" placeholder="بحث في السجلات..." onkeyup="searchLogs()">
        </div>
        
        <div class="filter-group">
            <button class="btn btn-primary" onclick="refreshLogs()">
                <i class="fas fa-sync"></i>
                تحديث
            </button>
            <button class="btn btn-success" onclick="exportLogs()">
                <i class="fas fa-download"></i>
                تصدير
            </button>
        </div>
    </div>

    <!-- إحصائيات السجلات -->
    <div class="stats-grid">
        <div class="stat-card moderation">
            <div class="stat-number" id="moderation-count">23</div>
            <div class="stat-label">إجراءات إشرافية</div>
        </div>
        <div class="stat-card members">
            <div class="stat-number" id="members-count">45</div>
            <div class="stat-label">أنشطة الأعضاء</div>
        </div>
        <div class="stat-card messages">
            <div class="stat-number" id="messages-count">1,247</div>
            <div class="stat-label">رسائل محذوفة</div>
        </div>
        <div class="stat-card voice">
            <div class="stat-number" id="voice-count">89</div>
            <div class="stat-label">أنشطة صوتية</div>
        </div>
    </div>

    <!-- قائمة السجلات -->
    <div class="card">
        <div class="card-header">
            <div class="card-icon">
                <i class="fas fa-list"></i>
            </div>
            <div>
                <h3 class="card-title">السجلات الحديثة</h3>
            </div>
        </div>
        <div class="card-body">
            <div class="logs-container" id="logs-container">
                <div class="loading-spinner">
                    <i class="fas fa-spinner fa-spin"></i>
                    جاري تحميل السجلات...
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.logs-filters {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
    background: rgba(255, 255, 255, 0.05);
    padding: 20px;
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.filter-group label {
    color: rgba(255, 255, 255, 0.9);
    font-weight: bold;
    font-size: 0.9rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.stat-card.moderation {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
}

.stat-card.members {
    background: linear-gradient(135deg, #4ecdc4, #44a08d);
    color: white;
}

.stat-card.messages {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.stat-card.voice {
    background: linear-gradient(135deg, #feca57, #ff9ff3);
    color: white;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 10px;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.logs-container {
    min-height: 400px;
}

.loading-spinner {
    text-align: center;
    padding: 50px;
    color: rgba(255, 255, 255, 0.6);
}

.log-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    margin-bottom: 10px;
    border-left: 4px solid;
    transition: all 0.3s ease;
}

.log-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(5px);
}

.log-item.moderation {
    border-left-color: #ff6b6b;
}

.log-item.member {
    border-left-color: #4ecdc4;
}

.log-item.message {
    border-left-color: #667eea;
}

.log-item.voice {
    border-left-color: #feca57;
}

.log-item.role {
    border-left-color: #ff9ff3;
}

.log-item.channel {
    border-left-color: #96ceb4;
}

.log-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
}

.log-icon.moderation {
    background: #ff6b6b;
}

.log-icon.member {
    background: #4ecdc4;
}

.log-icon.message {
    background: #667eea;
}

.log-icon.voice {
    background: #feca57;
}

.log-icon.role {
    background: #ff9ff3;
}

.log-icon.channel {
    background: #96ceb4;
}

.log-content {
    flex: 1;
}

.log-title {
    color: white;
    font-weight: bold;
    margin-bottom: 5px;
}

.log-description {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.log-meta {
    display: flex;
    gap: 15px;
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.6);
}

.log-user {
    display: flex;
    align-items: center;
    gap: 8px;
}

.log-user img {
    width: 20px;
    height: 20px;
    border-radius: 50%;
}

.log-time {
    margin-left: auto;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.8rem;
}

@media (max-width: 768px) {
    .logs-filters {
        grid-template-columns: 1fr;
    }
    
    .log-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .log-meta {
        flex-direction: column;
        gap: 5px;
    }
}
</style>

<script>
// بيانات السجلات التجريبية
let logs = [
    {
        id: 1,
        type: 'moderation',
        icon: 'fas fa-gavel',
        title: 'تم حظر عضو',
        description: 'تم حظر العضو @user123 لمخالفة القوانين',
        user: { name: 'المشرف', avatar: '/static/images/bot-logo.svg' },
        time: new Date(Date.now() - 30 * 60 * 1000)
    },
    {
        id: 2,
        type: 'member',
        icon: 'fas fa-user-plus',
        title: 'عضو جديد',
        description: 'انضم @newuser إلى الخادم',
        user: { name: 'النظام', avatar: '/static/images/bot-logo.svg' },
        time: new Date(Date.now() - 2 * 60 * 60 * 1000)
    },
    {
        id: 3,
        type: 'message',
        icon: 'fas fa-trash',
        title: 'حذف رسالة',
        description: 'تم حذف رسالة تحتوي على محتوى غير مناسب',
        user: { name: 'البوت', avatar: '/static/images/bot-logo.svg' },
        time: new Date(Date.now() - 4 * 60 * 60 * 1000)
    },
    {
        id: 4,
        type: 'voice',
        icon: 'fas fa-microphone',
        title: 'انضمام صوتي',
        description: 'انضم @user456 إلى قناة العام الصوتية',
        user: { name: 'النظام', avatar: '/static/images/bot-logo.svg' },
        time: new Date(Date.now() - 6 * 60 * 60 * 1000)
    },
    {
        id: 5,
        type: 'role',
        icon: 'fas fa-user-tag',
        title: 'تعديل رتبة',
        description: 'تم إعطاء رتبة "مشرف" للعضو @helper',
        user: { name: 'المالك', avatar: '/static/images/bot-logo.svg' },
        time: new Date(Date.now() - 12 * 60 * 60 * 1000)
    }
];

// تحميل السجلات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadLogs();
});

// تحميل السجلات
function loadLogs() {
    displayLogs(logs);
}

// عرض السجلات
function displayLogs(logsToShow) {
    const container = document.getElementById('logs-container');
    
    if (logsToShow.length === 0) {
        container.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-clipboard-list"></i>
                <h4>لا توجد سجلات</h4>
                <p>لم يتم العثور على سجلات تطابق المعايير المحددة</p>
            </div>
        `;
        return;
    }
    
    container.innerHTML = logsToShow.map(log => `
        <div class="log-item ${log.type}">
            <div class="log-icon ${log.type}">
                <i class="${log.icon}"></i>
            </div>
            <div class="log-content">
                <div class="log-title">${log.title}</div>
                <div class="log-description">${log.description}</div>
                <div class="log-meta">
                    <div class="log-user">
                        <img src="${log.user.avatar}" alt="${log.user.name}">
                        <span>${log.user.name}</span>
                    </div>
                </div>
            </div>
            <div class="log-time">
                ${formatTime(log.time)}
            </div>
        </div>
    `).join('');
}

// فلترة السجلات
function filterLogs() {
    const type = document.getElementById('logType').value;
    const dateFilter = document.getElementById('dateFilter').value;
    
    let filteredLogs = logs;
    
    // فلترة حسب النوع
    if (type !== 'all') {
        filteredLogs = filteredLogs.filter(log => log.type === type);
    }
    
    // فلترة حسب التاريخ
    const now = new Date();
    if (dateFilter === 'today') {
        filteredLogs = filteredLogs.filter(log => {
            const logDate = new Date(log.time);
            return logDate.toDateString() === now.toDateString();
        });
    } else if (dateFilter === 'week') {
        const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        filteredLogs = filteredLogs.filter(log => log.time >= weekAgo);
    } else if (dateFilter === 'month') {
        const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        filteredLogs = filteredLogs.filter(log => log.time >= monthAgo);
    }
    
    displayLogs(filteredLogs);
}

// البحث في السجلات
function searchLogs() {
    const searchTerm = document.getElementById('searchLogs').value.toLowerCase();
    
    if (!searchTerm) {
        filterLogs();
        return;
    }
    
    const filteredLogs = logs.filter(log => 
        log.title.toLowerCase().includes(searchTerm) ||
        log.description.toLowerCase().includes(searchTerm) ||
        log.user.name.toLowerCase().includes(searchTerm)
    );
    
    displayLogs(filteredLogs);
}

// تحديث السجلات
function refreshLogs() {
    document.getElementById('logs-container').innerHTML = `
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            جاري تحديث السجلات...
        </div>
    `;
    
    setTimeout(() => {
        loadLogs();
        showNotification('تم تحديث السجلات!', 'success');
    }, 1000);
}

// تصدير السجلات
function exportLogs() {
    const data = logs.map(log => ({
        النوع: log.type,
        العنوان: log.title,
        الوصف: log.description,
        المستخدم: log.user.name,
        الوقت: log.time.toLocaleString('ar-EG')
    }));
    
    const csv = convertToCSV(data);
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `server-logs-${new Date().toISOString().split('T')[0]}.csv`;
    link.click();
    
    showNotification('تم تصدير السجلات بنجاح!', 'success');
}

// تحويل إلى CSV
function convertToCSV(data) {
    const headers = Object.keys(data[0]).join(',');
    const rows = data.map(row => Object.values(row).join(',')).join('\n');
    return headers + '\n' + rows;
}

// تنسيق الوقت
function formatTime(date) {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (minutes < 60) {
        return `منذ ${minutes} دقيقة`;
    } else if (hours < 24) {
        return `منذ ${hours} ساعة`;
    } else {
        return `منذ ${days} يوم`;
    }
}

// إظهار الإشعارات
function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} notification`;
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        animation: slideIn 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}
</script>

<%- include('../partials/footer') %>
