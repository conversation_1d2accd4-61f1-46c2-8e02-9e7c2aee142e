<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> - <%= config.dashboard.title %></title>
    
    <!-- Meta Tags -->
    <meta name="description" content="<%= config.dashboard.description %>">
    <meta name="author" content="Discord Bot Dashboard">
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/static/favicon.ico">
    
    <!-- CSS Libraries -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="/static/css/main.css" rel="stylesheet">
    <link href="/static/css/dashboard.css" rel="stylesheet">
    <link href="/static/css/responsive.css" rel="stylesheet">
    
    <!-- Theme CSS -->
    <link href="/static/css/themes/dark.css" rel="stylesheet" id="theme-css">
    
    <style>
        :root {
            --primary-color: #7289da;
            --secondary-color: #99aab5;
            --success-color: #43b581;
            --warning-color: #faa61a;
            --danger-color: #f04747;
            --info-color: #00b0f4;
            --dark-color: #2c2f33;
            --light-color: #ffffff;
            --sidebar-width: 280px;
            --header-height: 70px;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #36393f;
            color: #ffffff;
            overflow-x: hidden;
        }
        
        .main-wrapper {
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar {
            width: var(--sidebar-width);
            background: linear-gradient(135deg, #2c2f33 0%, #23272a 100%);
            position: fixed;
            height: 100vh;
            right: 0;
            top: 0;
            z-index: 1000;
            overflow-y: auto;
            transition: transform 0.3s ease;
            box-shadow: -2px 0 10px rgba(0,0,0,0.3);
        }
        
        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #40444b;
            text-align: center;
        }
        
        .sidebar-header .logo {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            margin-bottom: 10px;
        }
        
        .sidebar-header h4 {
            color: var(--primary-color);
            font-weight: 600;
            margin: 0;
        }
        
        .sidebar-menu {
            padding: 20px 0;
        }
        
        .sidebar-menu .menu-item {
            display: block;
            padding: 15px 25px;
            color: #b9bbbe;
            text-decoration: none;
            transition: all 0.3s ease;
            border-right: 3px solid transparent;
        }
        
        .sidebar-menu .menu-item:hover,
        .sidebar-menu .menu-item.active {
            background-color: #40444b;
            color: #ffffff;
            border-right-color: var(--primary-color);
        }

        .menu-section {
            margin: 20px 0;
            padding: 15px 0;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .menu-section-title {
            color: #ffd700;
            font-weight: bold;
            font-size: 0.9rem;
            margin-bottom: 10px;
            padding: 0 20px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .menu-item.owner-only {
            background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 193, 7, 0.05));
            border-right: 3px solid #ffd700;
            position: relative;
        }

        .menu-item.owner-only::before {
            content: '👑';
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 0.8rem;
        }

        .menu-item.owner-only:hover {
            background: linear-gradient(135deg, rgba(255, 215, 0, 0.2), rgba(255, 193, 7, 0.1));
            border-right-color: #ffed4e;
        }
        
        .sidebar-menu .menu-item i {
            width: 20px;
            margin-left: 15px;
        }
        
        .main-content {
            flex: 1;
            margin-right: var(--sidebar-width);
            min-height: 100vh;
        }
        
        .header {
            background: #40444b;
            height: var(--header-height);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            position: sticky;
            top: 0;
            z-index: 999;
        }
        
        .header-title h2 {
            color: #ffffff;
            margin: 0;
            font-weight: 600;
        }
        
        .header-actions {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .user-menu {
            position: relative;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            border: 2px solid var(--primary-color);
        }
        
        .content-area {
            padding: 30px;
            min-height: calc(100vh - var(--header-height));
        }
        
        .card {
            background: #40444b;
            border: none;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            margin-bottom: 20px;
        }
        
        .card-header {
            background: #36393f;
            border-bottom: 1px solid #2c2f33;
            border-radius: 10px 10px 0 0 !important;
            padding: 20px;
        }
        
        .card-body {
            padding: 25px;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: 600;
        }
        
        .btn-primary:hover {
            background-color: #677bc4;
            border-color: #677bc4;
        }
        
        .form-control {
            background-color: #36393f;
            border: 1px solid #40444b;
            color: #ffffff;
            border-radius: 8px;
            padding: 12px 15px;
        }
        
        .form-control:focus {
            background-color: #36393f;
            border-color: var(--primary-color);
            color: #ffffff;
            box-shadow: 0 0 0 0.2rem rgba(114, 137, 218, 0.25);
        }
        
        .form-label {
            color: #b9bbbe;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        /* Mobile Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(100%);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-right: 0;
            }
            
            .content-area {
                padding: 20px 15px;
            }
            
            .header {
                padding: 0 15px;
            }
        }
        
        .mobile-menu-toggle {
            display: none;
            background: none;
            border: none;
            color: #ffffff;
            font-size: 20px;
            cursor: pointer;
        }
        
        @media (max-width: 768px) {
            .mobile-menu-toggle {
                display: block;
            }
        }
        
        .alert {
            border: none;
            border-radius: 8px;
            padding: 15px 20px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background-color: rgba(67, 181, 129, 0.2);
            color: var(--success-color);
            border-right: 4px solid var(--success-color);
        }
        
        .alert-danger {
            background-color: rgba(240, 71, 71, 0.2);
            color: var(--danger-color);
            border-right: 4px solid var(--danger-color);
        }
        
        .alert-warning {
            background-color: rgba(250, 166, 26, 0.2);
            color: var(--warning-color);
            border-right: 4px solid var(--warning-color);
        }
        
        .alert-info {
            background-color: rgba(0, 176, 244, 0.2);
            color: var(--info-color);
            border-right: 4px solid var(--info-color);
        }

        /* حقوق CS Discord */
        .cs-credits {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(135deg, #23272a 0%, #2c2f33 100%);
            padding: 20px;
            border-top: 1px solid #40444b;
            text-align: center;
        }

        .credits-title {
            color: #b9bbbe;
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .credits-links {
            display: flex;
            flex-direction: column;
            gap: 8px;
            margin-bottom: 10px;
        }

        .credit-link {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            padding: 8px 12px;
            border-radius: 6px;
            text-decoration: none;
            font-size: 0.85rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .credit-link.youtube {
            background: rgba(255, 0, 0, 0.1);
            color: #ff0000;
            border: 1px solid rgba(255, 0, 0, 0.2);
        }

        .credit-link.youtube:hover {
            background: rgba(255, 0, 0, 0.2);
            color: #ff0000;
            transform: translateY(-1px);
        }

        .credit-link.discord {
            background: rgba(114, 137, 218, 0.1);
            color: #7289da;
            border: 1px solid rgba(114, 137, 218, 0.2);
        }

        .credit-link.discord:hover {
            background: rgba(114, 137, 218, 0.2);
            color: #7289da;
            transform: translateY(-1px);
        }

        .credits-version {
            color: #72767d;
            font-size: 0.75rem;
            opacity: 0.8;
        }

        /* تعديل padding للـ sidebar لإفساح مجال للحقوق */
        .sidebar-menu {
            padding: 20px 0 180px 0; /* إضافة padding سفلي */
        }

        /* حماية إضافية للحقوق */
        .cs-credits::before {
            content: "CS Discord - لا تحذف هذا النص";
            position: absolute;
            left: -9999px;
            top: -9999px;
            opacity: 0;
        }

        /* تحسينات للشاشات الصغيرة */
        @media (max-width: 480px) {
            .sidebar {
                width: 100%;
                transform: translateX(100%);
            }

            .main-content {
                margin-right: 0;
            }

            .content-area {
                padding: 15px 10px;
            }

            .header {
                padding: 0 10px;
            }

            .header-title h2 {
                font-size: 1.2rem;
            }

            .cs-credits {
                padding: 15px;
            }

            .credit-link {
                padding: 6px 10px;
                font-size: 0.8rem;
            }
        }

        /* تحسينات للشاشات المتوسطة */
        @media (min-width: 481px) and (max-width: 768px) {
            .sidebar {
                width: 250px;
            }

            .main-content {
                margin-right: 250px;
            }

            .content-area {
                padding: 20px 15px;
            }
        }

        /* تحسينات للشاشات الكبيرة */
        @media (min-width: 1200px) {
            .sidebar {
                width: 300px;
            }

            .main-content {
                margin-right: 300px;
            }

            .content-area {
                padding: 40px;
            }
        }

        /* حماية ضد إخفاء الحقوق */
        .cs-credits {
            pointer-events: none !important;
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
        }

        .credit-link {
            pointer-events: auto !important;
        }
    </style>
</head>
<body>
    <!-- زر القائمة للهاتف -->
    <button class="mobile-menu-btn" onclick="toggleSidebar()" style="display: none;">
      <i class="fas fa-bars"></i>
    </button>

    <div class="main-wrapper">
        <!-- Sidebar -->
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <img src="/static/images/bot-logo.svg" alt="Bot Logo" class="logo">
                <h4><%= config.dashboard.title %></h4>
                <% if (user) { %>
                    <small class="text-muted">مرحباً، <%= user.username %></small>
                <% } %>
            </div>
            
            <div class="sidebar-menu">
                <a href="/dashboard" class="menu-item <%= currentPath === '/dashboard' ? 'active' : '' %>">
                    <i class="fas fa-home"></i>
                    الرئيسية
                </a>
                
                <a href="/dashboard/server-settings" class="menu-item">
                    <i class="fas fa-server"></i>
                    إعدادات الخادم
                </a>

                <a href="/dashboard/general" class="menu-item">
                    <i class="fas fa-cog"></i>
                    الإعدادات العامة
                </a>

                <a href="/dashboard/moderation" class="menu-item">
                    <i class="fas fa-shield-alt"></i>
                    نظام الإدارة
                </a>

                <a href="/dashboard/autoresponses" class="menu-item">
                    <i class="fas fa-robot"></i>
                    الردود التلقائية
                </a>

                <a href="/dashboard/economy" class="menu-item">
                    <i class="fas fa-coins"></i>
                    نظام العملات
                </a>

                <a href="/dashboard/command-builder" class="menu-item">
                    <i class="fas fa-code"></i>
                    منشئ الأوامر
                </a>

                <a href="/dashboard/automod" class="menu-item">
                    <i class="fas fa-shield-alt"></i>
                    الحماية التلقائية
                </a>

                <a href="/dashboard/roles" class="menu-item">
                    <i class="fas fa-users-cog"></i>
                    إدارة الرتب
                </a>

                <!-- قسم المالك فقط -->
                <% if (user && user.id === '761218404833034251') { %>
                <div class="menu-section">
                    <div class="menu-section-title">
                        <i class="fas fa-crown"></i>
                        إدارة المالك
                    </div>

                    <a href="/dashboard/premium" class="menu-item owner-only">
                        <i class="fas fa-crown"></i>
                        إدارة Premium
                    </a>

                    <a href="/dashboard/bot-settings" class="menu-item owner-only">
                        <i class="fas fa-robot"></i>
                        إعدادات البوت
                    </a>

                    <a href="/dashboard/analytics" class="menu-item owner-only">
                        <i class="fas fa-chart-line"></i>
                        التحليلات المتقدمة
                    </a>
                </div>
                <% } %>

                <% if (user && user.permissions.guilds.length > 0) { %>
                    <a href="/dashboard/select-guild" class="menu-item">
                        <i class="fas fa-plus"></i>
                        إضافة خادم
                    </a>
                <% } %>
                
                <!-- قائمة الخوادم -->
                <% if (locals.guildId) { %>
                    <div class="menu-section">
                        <div class="menu-section-title">إدارة الخادم</div>
                        
                        <a href="/dashboard/guild/<%= guildId %>" class="menu-item">
                            <i class="fas fa-tachometer-alt"></i>
                            لوحة التحكم
                        </a>
                        
                        <a href="/dashboard/guild/<%= guildId %>/settings" class="menu-item">
                            <i class="fas fa-cog"></i>
                            الإعدادات العامة
                        </a>
                        
                        <a href="/dashboard/guild/<%= guildId %>/moderation" class="menu-item">
                            <i class="fas fa-shield-alt"></i>
                            الإدارة والمودريشن
                        </a>
                        
                        <a href="/dashboard/guild/<%= guildId %>/tickets" class="menu-item">
                            <i class="fas fa-ticket-alt"></i>
                            نظام التذاكر
                        </a>
                        
                        <a href="/dashboard/guild/<%= guildId %>/welcome" class="menu-item">
                            <i class="fas fa-hand-wave"></i>
                            الترحيب والمغادرة
                        </a>
                        
                        <a href="/dashboard/guild/<%= guildId %>/leveling" class="menu-item">
                            <i class="fas fa-chart-line"></i>
                            نظام المستويات
                        </a>
                        
                        <a href="/dashboard/guild/<%= guildId %>/logs" class="menu-item">
                            <i class="fas fa-file-alt"></i>
                            سجل اللوج
                        </a>
                    </div>
                <% } %>
                
                <div class="menu-section">
                    <a href="/auth/logout" class="menu-item">
                        <i class="fas fa-sign-out-alt"></i>
                        تسجيل الخروج
                    </a>
                </div>

                <!-- حقوق CS Discord -->
                <div class="cs-credits" data-cs-protection="true" data-cs-author="CS Discord">
                    <div class="credits-title">
                        <i class="fas fa-code"></i>
                        تطوير
                    </div>
                    <div class="credits-links">
                        <a href="https://www.youtube.com/@CS_Discord" target="_blank"
                           class="credit-link youtube"
                           data-cs-credit="youtube"
                           data-cs-verify="CS_Discord">
                            <i class="fab fa-youtube"></i>
                            CS Discord
                        </a>
                        <a href="https://discord.gg/yqTn2EwVsd" target="_blank"
                           class="credit-link discord"
                           data-cs-credit="discord"
                           data-cs-verify="yqTn2EwVsd">
                            <i class="fab fa-discord"></i>
                            خادم الدعم
                        </a>
                    </div>
                    <div class="credits-version" data-cs-version="1.0.0">
                        الإصدار 1.0.0 - CS Discord
                    </div>
                    <!-- حماية مخفية -->
                    <div style="display: none;" data-cs-hash="cs-discord-protection">
                        CS Discord - https://www.youtube.com/@CS_Discord - https://discord.gg/yqTn2EwVsd
                    </div>
                </div>
            </div>
        </nav>
        
        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="header">
                <div class="header-title">
                    <button class="mobile-menu-toggle" onclick="toggleSidebar()">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h2><%= title %></h2>
                </div>
                
                <div class="header-actions">
                    <% if (user) { %>
                        <div class="user-menu">
                            <img src="https://cdn.discordapp.com/avatars/<%= user.discordId %>/<%= user.avatar %>.png" 
                                 alt="<%= user.username %>" 
                                 class="user-avatar"
                                 onerror="this.src='/static/images/default-avatar.png'">
                        </div>
                    <% } %>
                </div>
            </header>
            
            <!-- Content Area -->
            <div class="content-area">
                <%- body %>
            </div>
        </main>
    </div>
    
    <!-- JavaScript Libraries -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/axios/1.4.0/axios.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="/static/js/main.js"></script>
    
    <script>
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('show');
        }
        
        // إغلاق الـ sidebar عند النقر خارجه في الهاتف
        document.addEventListener('click', function(event) {
            const sidebar = document.getElementById('sidebar');
            const toggleBtn = document.querySelector('.mobile-menu-toggle');
            
            if (window.innerWidth <= 768 && 
                !sidebar.contains(event.target) && 
                !toggleBtn.contains(event.target)) {
                sidebar.classList.remove('show');
            }
        });
        
        // تحديث حالة القائمة النشطة
        document.addEventListener('DOMContentLoaded', function() {
            const currentPath = window.location.pathname;
            const menuItems = document.querySelectorAll('.menu-item');
            
            menuItems.forEach(item => {
                if (item.getAttribute('href') === currentPath) {
                    item.classList.add('active');
                } else {
                    item.classList.remove('active');
                }
            });
        });
    </script>

    <!-- JavaScript للقائمة المتجاوبة -->
    <script>
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('active');
        }

        // إغلاق القائمة عند النقر خارجها
        document.addEventListener('click', function(event) {
            const sidebar = document.getElementById('sidebar');
            const menuBtn = document.querySelector('.mobile-menu-btn');

            if (!sidebar.contains(event.target) && !menuBtn.contains(event.target)) {
                sidebar.classList.remove('active');
            }
        });

        // تحديث صورة البوت إذا لم تكن موجودة
        document.addEventListener('DOMContentLoaded', function() {
            const botLogo = document.querySelector('.logo');
            if (botLogo) {
                botLogo.onerror = function() {
                    this.src = '/static/images/bot-logo.svg';
                    this.onerror = function() {
                        this.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8Y2lyY2xlIGN4PSI0MCIgY3k9IjQwIiByPSIzNSIgZmlsbD0iIzY2N2VlYSIvPgogIDx0ZXh0IHg9IjQwIiB5PSI0NSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0IiBmaWxsPSJ3aGl0ZSI+Qk9UPC90ZXh0Pgo8L3N2Zz4=';
                    };
                };
            }
        });
    </script>
</body>
</html>
