# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
node_modules/
jspm_packages/

# TypeScript v1 declaration files
typings/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows image file caches
Thumbs.db
ehthumbs.db

# Folder config file
Desktop.ini

# Recycle Bin used on file shares
$RECYCLE.BIN/

# Windows Installer files
*.cab
*.msi
*.msm
*.msp

# Windows shortcuts
*.lnk

# Session files
sessions/

# Upload directories
uploads/
public/uploads/

# Backup files
*.backup
*.bak
*.tmp

# Database files
*.db
*.sqlite
*.sqlite3

# Configuration files with sensitive data
config/production.js
config/local.js

# SSL certificates
*.pem
*.key
*.crt
*.csr

# PM2 files
ecosystem.config.js
.pm2/

# Docker files
.dockerignore
Dockerfile
docker-compose.yml

# Build artifacts
build/
dist/
out/

# Test files
test-results/
coverage/

# Backup and temporary files
*.orig
*.rej
*.bak
*.tmp
*.temp

# IDE and editor files
.vscode/
.idea/
*.sublime-project
*.sublime-workspace

# Package lock files (choose one)
# package-lock.json
# yarn.lock

# Local development files
.local/
.dev/

# Error logs
error.log
access.log

# Process files
*.pid

# Memory dumps
*.heapsnapshot

# ESLint cache
.eslintcache

# Prettier cache
.prettiercache

# Stylelint cache
.stylelintcache
