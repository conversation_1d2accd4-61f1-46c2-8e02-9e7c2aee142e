# 🗄️ خيارات قاعدة البيانات السريعة

## 🔧 كيفية تغيير قاعدة البيانات

افتح ملف: `core/security/credentials.js`

ابحث عن السطر:
```javascript
mongoUri: 'النص_المشفر_هنا',
```

واستبدل `النص_المشفر_هنا` بواحد من الخيارات التالية:

---

## 📋 الخيارات المتاحة

### الخيار 1: قاعدة بيانات محلية (الأسهل) ✅
```javascript
mongoUri: 'bW9uZ29kYjovL2xvY2FsaG9zdDoyNzAxNy9kaXNjb3JkLWJvdC1kYXNoYm9hcmQ=',
```
**يفك التشفير إلى:** `mongodb://localhost:27017/discord-bot-dashboard`

---

### الخيار 2: MongoDB Atlas مجاني (موصى به)
```javascript
mongoUri: '********************************************************************************',
```
**يفك التشفير إلى:** `mongodb+srv://username:<EMAIL>/database`

**للحصول على MongoDB Atlas مجاناً:**
1. اذهب إلى: https://www.mongodb.com/atlas
2. أنشئ حساب مجاني
3. أنشئ cluster مجاني
4. احصل على connection string
5. شفّره بـ base64 وضعه في الملف

---

### الخيار 3: قاعدة بيانات محلية مع مصادقة
```javascript
mongoUri: '************************************************************************',
```
**يفك التشفير إلى:** `****************************************************`

---

### الخيار 4: قاعدة بيانات خارجية
```javascript
mongoUri: '************************************************************************************',
```
**يفك التشفير إلى:** `********************************:port/database?authSource=admin`

---

## 🔐 كيفية تشفير رابط جديد

### طريقة سريعة (أونلاين):
1. اذهب إلى: https://www.base64encode.org/
2. ضع رابط قاعدة البيانات
3. اضغط "Encode"
4. انسخ النتيجة وضعها في الملف

### مثال:
- **الرابط الأصلي:** `mongodb://localhost:27017/mybot`
- **بعد التشفير:** `bW9uZ29kYjovL2xvY2FsaG9zdDoyNzAxNy9teWJvdA==`

---

## ⚡ حل سريع للمشكلة الحالية

**المشكلة:** `Authentication failed`

**الحل السريع:**
1. افتح `core/security/credentials.js`
2. ابحث عن `mongoUri:`
3. استبدل القيمة بـ:
```javascript
mongoUri: 'bW9uZ29kYjovL2xvY2FsaG9zdDoyNzAxNy9kaXNjb3JkLWJvdC1kYXNoYm9hcmQ=',
```
4. احفظ الملف
5. أعد تشغيل الخادم

---

## 📞 الدعم

📺 **يوتيوب**: https://www.youtube.com/@CS_Discord  
💬 **ديسكورد**: https://discord.gg/yqTn2EwVsd

---
**تطوير: CS Discord** 🗄️
