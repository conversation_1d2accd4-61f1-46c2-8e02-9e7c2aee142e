// ========================================
// 🎰 أمر المقامرة - CS Discord
// ========================================
// 📺 يوتيوب: https://www.youtube.com/@CS_Discord
// 💬 ديسكورد: https://discord.gg/yqTn2EwVsd
// ========================================

const { EmbedBuilder } = require('discord.js');
const Currency = require('../../models/Currency');

module.exports = {
  name: 'gamble',
  aliases: ['bet', 'مقامرة', 'رهان'],
  description: 'قامر بأموالك (احذر من الإدمان!)',
  usage: '<المبلغ>',
  category: 'economy',
  cooldown: 10,
  
  async execute(message, args) {
    try {
      // التحقق من وجود المبلغ
      if (!args[0]) {
        return await message.reply('❌ يجب تحديد مبلغ المقامرة!\nمثال: `gamble 100`');
      }
      
      // تحويل المبلغ إلى رقم
      let amount = parseInt(args[0]);
      
      // التحقق من صحة المبلغ
      if (isNaN(amount) || amount <= 0) {
        return await message.reply('❌ يجب أن يكون المبلغ رقم صحيح أكبر من 0!');
      }
      
      // الحصول على بيانات المستخدم
      const userCurrency = await Currency.getOrCreate(message.author.id, message.guild.id);
      
      // التحقق من الحد الأدنى والأقصى
      const minBet = 10;
      const maxBet = Math.min(userCurrency.balance, 10000);
      
      if (amount < minBet) {
        return await message.reply(`❌ الحد الأدنى للمقامرة هو **${minBet}** عملة!`);
      }
      
      if (amount > maxBet) {
        return await message.reply(`❌ الحد الأقصى للمقامرة هو **${maxBet.toLocaleString('ar-EG')}** عملة!`);
      }
      
      // التحقق من كفاية الرصيد
      if (userCurrency.balance < amount) {
        return await message.reply(`❌ رصيدك غير كافي! رصيدك الحالي: **${userCurrency.balance.toLocaleString('ar-EG')}** عملة`);
      }
      
      // تحديد نوع المقامرة العشوائي
      const gameTypes = ['slots', 'dice', 'coinflip', 'roulette'];
      const gameType = gameTypes[Math.floor(Math.random() * gameTypes.length)];
      
      let result;
      
      switch (gameType) {
        case 'slots':
          result = playSlots(amount);
          break;
        case 'dice':
          result = playDice(amount);
          break;
        case 'coinflip':
          result = playCoinFlip(amount);
          break;
        case 'roulette':
          result = playRoulette(amount);
          break;
      }
      
      // تحديث الرصيد والإحصائيات
      if (result.won) {
        userCurrency.balance += result.winAmount;
        userCurrency.gambling.totalWon += 1;
        userCurrency.gambling.winStreak += 1;
        userCurrency.gambling.loseStreak = 0;
      } else {
        userCurrency.balance -= amount;
        userCurrency.gambling.totalLost += 1;
        userCurrency.gambling.loseStreak += 1;
        userCurrency.gambling.winStreak = 0;
      }
      
      userCurrency.gambling.totalBet += 1;
      
      // إضافة المعاملة
      userCurrency.transactions.push({
        type: 'gambling',
        amount: result.won ? result.winAmount : -amount,
        description: `${result.gameName} - ${result.won ? 'فوز' : 'خسارة'}`,
        timestamp: new Date()
      });
      
      await userCurrency.save();
      
      // إنشاء الـ embed
      const embed = new EmbedBuilder()
        .setColor(result.won ? '#00ff00' : '#ff0000')
        .setTitle(`🎰 ${result.gameName}`)
        .setDescription(result.description)
        .setThumbnail(message.author.displayAvatarURL({ dynamic: true }))
        .addFields(
          {
            name: '💰 المبلغ المراهن',
            value: `**${amount.toLocaleString('ar-EG')}** عملة`,
            inline: true
          },
          {
            name: result.won ? '🎉 الربح' : '💸 الخسارة',
            value: result.won ? `**+${result.winAmount.toLocaleString('ar-EG')}** عملة` : `**-${amount.toLocaleString('ar-EG')}** عملة`,
            inline: true
          },
          {
            name: '💵 رصيدك الجديد',
            value: `**${userCurrency.balance.toLocaleString('ar-EG')}** عملة`,
            inline: true
          }
        )
        .setFooter({
          text: 'CS Discord Economy System',
          iconURL: message.client.user.displayAvatarURL()
        })
        .setTimestamp();
      
      // إضافة إحصائيات المقامرة
      const winRate = userCurrency.gambling.totalBet > 0 ? 
        Math.round(((userCurrency.gambling.totalWon) / userCurrency.gambling.totalBet) * 100) : 0;
      
      embed.addFields({
        name: '📊 إحصائيات المقامرة',
        value: `🎯 نسبة الفوز: **${winRate}%**\n🔥 تتالي الفوز: **${userCurrency.gambling.winStreak}**\n💔 تتالي الخسارة: **${userCurrency.gambling.loseStreak}**`,
        inline: false
      });
      
      // رسائل تحذيرية
      if (userCurrency.gambling.loseStreak >= 5) {
        embed.addFields({
          name: '⚠️ تحذير',
          value: 'لديك سلسلة خسائر طويلة! فكر في أخذ استراحة من المقامرة.',
          inline: false
        });
      }
      
      if (userCurrency.balance < amount * 2) {
        embed.addFields({
          name: '💡 نصيحة',
          value: 'رصيدك منخفض! جرب العمل أو الراتب اليومي لزيادة رصيدك.',
          inline: false
        });
      }
      
      await message.reply({ embeds: [embed] });
      
    } catch (error) {
      console.error('خطأ في أمر المقامرة:', error);
      await message.reply('❌ حدث خطأ أثناء المقامرة!');
    }
  }
};

// لعبة ماكينة القمار
function playSlots(amount) {
  const symbols = ['🍎', '🍊', '🍋', '🍇', '🍓', '💎', '⭐', '🔔'];
  const reels = [
    symbols[Math.floor(Math.random() * symbols.length)],
    symbols[Math.floor(Math.random() * symbols.length)],
    symbols[Math.floor(Math.random() * symbols.length)]
  ];
  
  let multiplier = 0;
  
  if (reels[0] === reels[1] && reels[1] === reels[2]) {
    // ثلاثة متطابقة
    if (reels[0] === '💎') multiplier = 10;
    else if (reels[0] === '⭐') multiplier = 8;
    else if (reels[0] === '🔔') multiplier = 6;
    else multiplier = 4;
  } else if (reels[0] === reels[1] || reels[1] === reels[2] || reels[0] === reels[2]) {
    // اثنان متطابقان
    multiplier = 1.5;
  }
  
  const won = multiplier > 0;
  const winAmount = won ? Math.floor(amount * multiplier) : 0;
  
  return {
    won,
    winAmount,
    gameName: 'ماكينة القمار',
    description: `${reels.join(' | ')}\n${won ? `🎉 فزت! المضاعف: x${multiplier}` : '💸 خسرت! حظ أوفر المرة القادمة!'}`
  };
}

// لعبة النرد
function playDice(amount) {
  const playerRoll = Math.floor(Math.random() * 6) + 1;
  const houseRoll = Math.floor(Math.random() * 6) + 1;
  
  let won = false;
  let multiplier = 0;
  
  if (playerRoll > houseRoll) {
    won = true;
    multiplier = playerRoll === 6 ? 3 : 2;
  } else if (playerRoll === houseRoll) {
    won = true;
    multiplier = 1;
  }
  
  const winAmount = won ? Math.floor(amount * multiplier) : 0;
  
  return {
    won,
    winAmount,
    gameName: 'لعبة النرد',
    description: `🎲 نردك: **${playerRoll}** | 🎲 نرد البيت: **${houseRoll}**\n${won ? `🎉 فزت! المضاعف: x${multiplier}` : '💸 خسرت! حظ أوفر المرة القادمة!'}`
  };
}

// لعبة قلب العملة
function playCoinFlip(amount) {
  const playerChoice = Math.random() < 0.5 ? 'heads' : 'tails';
  const result = Math.random() < 0.5 ? 'heads' : 'tails';
  
  const won = playerChoice === result;
  const winAmount = won ? amount * 2 : 0;
  
  const choiceEmoji = { heads: '👑', tails: '⚡' };
  
  return {
    won,
    winAmount,
    gameName: 'قلب العملة',
    description: `اختيارك: ${choiceEmoji[playerChoice]} | النتيجة: ${choiceEmoji[result]}\n${won ? '🎉 فزت! ضاعفت أموالك!' : '💸 خسرت! حظ أوفر المرة القادمة!'}`
  };
}

// لعبة الروليت
function playRoulette(amount) {
  const number = Math.floor(Math.random() * 37); // 0-36
  const isRed = [1,3,5,7,9,12,14,16,18,19,21,23,25,27,30,32,34,36].includes(number);
  const isBlack = number !== 0 && !isRed;
  
  let won = false;
  let multiplier = 0;
  let result = '';
  
  if (number === 0) {
    won = true;
    multiplier = 35;
    result = '🟢 صفر! جائزة كبرى!';
  } else if (Math.random() < 0.5) {
    // رهان على اللون
    const betColor = Math.random() < 0.5 ? 'red' : 'black';
    if ((betColor === 'red' && isRed) || (betColor === 'black' && isBlack)) {
      won = true;
      multiplier = 2;
      result = `${isRed ? '🔴' : '⚫'} ${number} - فزت برهان اللون!`;
    } else {
      result = `${isRed ? '🔴' : '⚫'} ${number} - خسرت رهان اللون!`;
    }
  } else {
    // رهان على الرقم
    const betNumber = Math.floor(Math.random() * 37);
    if (betNumber === number) {
      won = true;
      multiplier = 35;
      result = `🎯 ${number} - فزت برهان الرقم!`;
    } else {
      result = `${number} - خسرت رهان الرقم!`;
    }
  }
  
  const winAmount = won ? Math.floor(amount * multiplier) : 0;
  
  return {
    won,
    winAmount,
    gameName: 'الروليت',
    description: result + `\n${won ? `🎉 المضاعف: x${multiplier}` : '💸 حظ أوفر المرة القادمة!'}`
  };
}
