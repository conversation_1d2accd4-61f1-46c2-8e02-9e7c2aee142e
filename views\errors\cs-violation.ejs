<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>انتهاك حقوق المطور - CS Discord</title>
    
    <!-- CSS Libraries -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #ff4757 0%, #c44569 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #ffffff;
            overflow: hidden;
        }
        
        .violation-container {
            text-align: center;
            max-width: 700px;
            padding: 40px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 20px;
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.2);
            animation: shake 0.5s ease-in-out;
        }
        
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-10px); }
            75% { transform: translateX(10px); }
        }
        
        .violation-icon {
            font-size: 5rem;
            color: #ff4757;
            margin-bottom: 30px;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
        
        .violation-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        
        .violation-message {
            font-size: 1.3rem;
            margin-bottom: 30px;
            line-height: 1.6;
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            border-right: 4px solid #ff4757;
        }
        
        .cs-info {
            background: rgba(0, 0, 0, 0.4);
            padding: 30px;
            border-radius: 15px;
            margin: 30px 0;
            border: 2px solid #7289da;
        }
        
        .cs-title {
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 20px;
            color: #7289da;
        }
        
        .cs-links {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .cs-link {
            padding: 15px 25px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
            border: 2px solid;
        }
        
        .cs-link.youtube {
            background: rgba(255, 0, 0, 0.2);
            color: #ff0000;
            border-color: #ff0000;
        }
        
        .cs-link.youtube:hover {
            background: rgba(255, 0, 0, 0.4);
            color: #ff0000;
            transform: translateY(-2px);
        }
        
        .cs-link.discord {
            background: rgba(114, 137, 218, 0.2);
            color: #7289da;
            border-color: #7289da;
        }
        
        .cs-link.discord:hover {
            background: rgba(114, 137, 218, 0.4);
            color: #7289da;
            transform: translateY(-2px);
        }
        
        .warning-text {
            background: rgba(255, 193, 7, 0.2);
            color: #ffc107;
            padding: 15px;
            border-radius: 10px;
            border: 1px solid rgba(255, 193, 7, 0.3);
            font-weight: 600;
            margin-top: 20px;
        }
        
        .countdown {
            font-size: 1.5rem;
            font-weight: 700;
            color: #ff4757;
            margin-top: 20px;
        }
        
        .floating-icons {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
        }
        
        .floating-icon {
            position: absolute;
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
            color: #ff4757;
        }
        
        .floating-icon:nth-child(1) {
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }
        
        .floating-icon:nth-child(2) {
            top: 20%;
            right: 10%;
            animation-delay: 1s;
        }
        
        .floating-icon:nth-child(3) {
            bottom: 20%;
            left: 15%;
            animation-delay: 2s;
        }
        
        .floating-icon:nth-child(4) {
            bottom: 10%;
            right: 20%;
            animation-delay: 3s;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        
        @media (max-width: 768px) {
            .violation-container {
                padding: 30px 20px;
                margin: 20px;
            }
            
            .violation-title {
                font-size: 2rem;
            }
            
            .violation-message {
                font-size: 1.1rem;
            }
            
            .cs-links {
                flex-direction: column;
                align-items: center;
            }
            
            .cs-link {
                width: 100%;
                max-width: 300px;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="floating-icons">
        <i class="fas fa-shield-alt floating-icon" style="font-size: 3rem;"></i>
        <i class="fas fa-exclamation-triangle floating-icon" style="font-size: 2rem;"></i>
        <i class="fas fa-ban floating-icon" style="font-size: 2.5rem;"></i>
        <i class="fas fa-lock floating-icon" style="font-size: 2rem;"></i>
    </div>
    
    <div class="violation-container">
        <div class="violation-icon">
            <i class="fas fa-shield-alt"></i>
        </div>
        
        <h1 class="violation-title">🚨 انتهاك حقوق المطور</h1>
        
        <div class="violation-message">
            <i class="fas fa-exclamation-triangle"></i>
            تم اكتشاف محاولة لتعديل أو حذف حقوق المطور الأصلي.
            هذا التطبيق مجاني ومفتوح المصدر، ولكن يجب الحفاظ على حقوق المطور.
        </div>
        
        <div class="cs-info">
            <h2 class="cs-title">
                <i class="fas fa-code"></i>
                المطور الأصلي: CS Discord
            </h2>
            
            <div class="cs-links">
                <a href="<%= youtube %>" target="_blank" class="cs-link youtube">
                    <i class="fab fa-youtube"></i>
                    قناة اليوتيوب
                </a>
                
                <a href="<%= discord %>" target="_blank" class="cs-link discord">
                    <i class="fab fa-discord"></i>
                    خادم الدعم
                </a>
            </div>
            
            <div class="warning-text">
                <i class="fas fa-info-circle"></i>
                يرجى عدم تعديل أو حذف حقوق المطور. هذا يساعد في دعم تطوير المزيد من المشاريع المجانية.
            </div>
        </div>
        
        <div class="countdown" id="countdown">
            سيتم إعادة التوجيه خلال <span id="timer">10</span> ثانية...
        </div>
    </div>
    
    <script>
        // عداد تنازلي لإعادة التوجيه
        let timeLeft = 10;
        const timerElement = document.getElementById('timer');
        
        const countdown = setInterval(() => {
            timeLeft--;
            timerElement.textContent = timeLeft;
            
            if (timeLeft <= 0) {
                clearInterval(countdown);
                window.location.href = '/';
            }
        }, 1000);
        
        // منع النقر بالزر الأيمن
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
            return false;
        });
        
        // منع اختصارات لوحة المفاتيح
        document.addEventListener('keydown', function(e) {
            if (e.keyCode === 123 || 
                (e.ctrlKey && e.shiftKey && (e.keyCode === 73 || e.keyCode === 74)) ||
                (e.ctrlKey && e.keyCode === 85)) {
                e.preventDefault();
                return false;
            }
        });
        
        // رسالة في الكونسول
        console.log('%c🛡️ حماية CS Discord', 'color: red; font-size: 30px; font-weight: bold;');
        console.log('%c📺 يوتيوب: <%= youtube %>', 'color: #ff0000; font-size: 16px;');
        console.log('%c💬 ديسكورد: <%= discord %>', 'color: #7289da; font-size: 16px;');
        console.log('%c⚠️ يرجى عدم تعديل حقوق المطور', 'color: orange; font-size: 14px;');
    </script>
</body>
</html>
