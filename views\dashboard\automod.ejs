<%- include('../partials/header') %>

<div class="main-content">
    <div class="content-header">
        <h1 class="page-title">
            <i class="fas fa-shield-alt"></i>
            نظام الحماية التلقائية
        </h1>
        <p class="page-description">
            إعداد قواعد الحماية التلقائية والخطوط الحمراء
        </p>
    </div>

    <!-- بطاقات الميزات -->
    <div class="features-grid">
        <!-- حماية الروابط -->
        <div class="card feature-card">
            <div class="card-header">
                <div class="card-icon">
                    <i class="fas fa-link"></i>
                </div>
                <div>
                    <h3 class="card-title">حماية الروابط</h3>
                    <div class="toggle-switch">
                        <input type="checkbox" id="linkProtection">
                        <label for="linkProtection"></label>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <p class="card-description">منع إرسال الروابط غير المرغوب فيها</p>
                
                <div class="setting-group">
                    <label>الروابط المسموحة:</label>
                    <div class="allowed-links">
                        <div class="link-item">
                            <input type="text" placeholder="discord.gg" class="form-control">
                            <button class="btn btn-sm btn-danger" onclick="removeLink(this)">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <button class="btn btn-sm btn-success" onclick="addAllowedLink()">
                        <i class="fas fa-plus"></i>
                        إضافة رابط
                    </button>
                </div>
                
                <div class="setting-group">
                    <label>الإجراء عند المخالفة:</label>
                    <select class="form-control" id="linkAction">
                        <option value="delete">حذف الرسالة</option>
                        <option value="warn">تحذير</option>
                        <option value="mute">كتم مؤقت</option>
                        <option value="kick">طرد</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- حماية السبام -->
        <div class="card feature-card">
            <div class="card-header">
                <div class="card-icon">
                    <i class="fas fa-ban"></i>
                </div>
                <div>
                    <h3 class="card-title">حماية السبام</h3>
                    <div class="toggle-switch">
                        <input type="checkbox" id="spamProtection">
                        <label for="spamProtection"></label>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <p class="card-description">منع الرسائل المتكررة والسبام</p>
                
                <div class="setting-group">
                    <label>عدد الرسائل المسموح:</label>
                    <input type="number" class="form-control" id="maxMessages" value="5" min="1" max="20">
                </div>
                
                <div class="setting-group">
                    <label>خلال (ثانية):</label>
                    <input type="number" class="form-control" id="timeWindow" value="10" min="5" max="60">
                </div>
                
                <div class="setting-group">
                    <label>الإجراء:</label>
                    <select class="form-control" id="spamAction">
                        <option value="delete">حذف الرسائل</option>
                        <option value="mute">كتم 10 دقائق</option>
                        <option value="mute30">كتم 30 دقيقة</option>
                        <option value="kick">طرد</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- حماية الكلمات المحظورة -->
        <div class="card feature-card">
            <div class="card-header">
                <div class="card-icon">
                    <i class="fas fa-comment-slash"></i>
                </div>
                <div>
                    <h3 class="card-title">الكلمات المحظورة</h3>
                    <div class="toggle-switch">
                        <input type="checkbox" id="badWordsProtection">
                        <label for="badWordsProtection"></label>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <p class="card-description">منع الكلمات والعبارات غير اللائقة</p>
                
                <div class="setting-group">
                    <label>قائمة الكلمات المحظورة:</label>
                    <div class="banned-words">
                        <div class="word-tags">
                            <!-- سيتم إضافة الكلمات هنا -->
                        </div>
                        <div class="add-word-input">
                            <input type="text" id="newBannedWord" placeholder="أضف كلمة محظورة" class="form-control">
                            <button class="btn btn-success" onclick="addBannedWord()">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="setting-group">
                    <label>الإجراء:</label>
                    <select class="form-control" id="badWordsAction">
                        <option value="delete">حذف الرسالة</option>
                        <option value="warn">تحذير + حذف</option>
                        <option value="mute">كتم + حذف</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- حماية الرتب -->
        <div class="card feature-card">
            <div class="card-header">
                <div class="card-icon">
                    <i class="fas fa-user-shield"></i>
                </div>
                <div>
                    <h3 class="card-title">حماية الرتب</h3>
                    <div class="toggle-switch">
                        <input type="checkbox" id="roleProtection">
                        <label for="roleProtection"></label>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <p class="card-description">منع التلاعب بالرتب والصلاحيات</p>
                
                <div class="setting-group">
                    <label>الرتب المحمية:</label>
                    <div class="protected-roles" id="protectedRoles">
                        <!-- سيتم تحميل الرتب هنا -->
                    </div>
                </div>
                
                <div class="setting-group">
                    <label>منع إنشاء رتب جديدة:</label>
                    <div class="toggle-switch">
                        <input type="checkbox" id="preventRoleCreation">
                        <label for="preventRoleCreation"></label>
                    </div>
                </div>
                
                <div class="setting-group">
                    <label>الإجراء عند المخالفة:</label>
                    <select class="form-control" id="roleProtectionAction">
                        <option value="revert">إلغاء التغيير</option>
                        <option value="ban">حظر فوري</option>
                        <option value="kick">طرد</option>
                        <option value="demote">إزالة الصلاحيات</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- حماية القنوات -->
        <div class="card feature-card">
            <div class="card-header">
                <div class="card-icon">
                    <i class="fas fa-hashtag"></i>
                </div>
                <div>
                    <h3 class="card-title">حماية القنوات</h3>
                    <div class="toggle-switch">
                        <input type="checkbox" id="channelProtection">
                        <label for="channelProtection"></label>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <p class="card-description">منع حذف أو تعديل القنوات المهمة</p>
                
                <div class="setting-group">
                    <label>القنوات المحمية:</label>
                    <div class="protected-channels" id="protectedChannels">
                        <!-- سيتم تحميل القنوات هنا -->
                    </div>
                </div>
                
                <div class="setting-group">
                    <label>منع إنشاء قنوات جديدة:</label>
                    <div class="toggle-switch">
                        <input type="checkbox" id="preventChannelCreation">
                        <label for="preventChannelCreation"></label>
                    </div>
                </div>
            </div>
        </div>

        <!-- الخطوط الحمراء -->
        <div class="card feature-card danger">
            <div class="card-header">
                <div class="card-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div>
                    <h3 class="card-title">الخطوط الحمراء</h3>
                    <div class="toggle-switch">
                        <input type="checkbox" id="redLinesProtection">
                        <label for="redLinesProtection"></label>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <p class="card-description">قواعد صارمة لا يُسمح بتجاوزها أبداً</p>
                
                <div class="red-lines-list">
                    <div class="red-line-item">
                        <div class="red-line-content">
                            <h5>🚫 منع السب والشتائم</h5>
                            <p>حظر فوري لأي شخص يستخدم كلمات نابية</p>
                        </div>
                        <div class="toggle-switch">
                            <input type="checkbox" id="redLine1" checked>
                            <label for="redLine1"></label>
                        </div>
                    </div>
                    
                    <div class="red-line-item">
                        <div class="red-line-content">
                            <h5>🚫 منع المحتوى الإباحي</h5>
                            <p>حظر فوري لأي محتوى غير لائق</p>
                        </div>
                        <div class="toggle-switch">
                            <input type="checkbox" id="redLine2" checked>
                            <label for="redLine2"></label>
                        </div>
                    </div>
                    
                    <div class="red-line-item">
                        <div class="red-line-content">
                            <h5>🚫 منع التهديد والوعيد</h5>
                            <p>حظر فوري لأي تهديد للأعضاء</p>
                        </div>
                        <div class="toggle-switch">
                            <input type="checkbox" id="redLine3" checked>
                            <label for="redLine3"></label>
                        </div>
                    </div>
                    
                    <div class="red-line-item">
                        <div class="red-line-content">
                            <h5>🚫 منع الإعلانات</h5>
                            <p>حظر فوري للإعلان لخوادم أخرى</p>
                        </div>
                        <div class="toggle-switch">
                            <input type="checkbox" id="redLine4" checked>
                            <label for="redLine4"></label>
                        </div>
                    </div>
                </div>
                
                <div class="setting-group">
                    <label>إضافة خط أحمر جديد:</label>
                    <div class="add-red-line">
                        <input type="text" id="newRedLine" placeholder="وصف الخط الأحمر" class="form-control">
                        <button class="btn btn-danger" onclick="addRedLine()">
                            <i class="fas fa-plus"></i>
                            إضافة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- أزرار الحفظ -->
    <div class="save-section">
        <button class="btn btn-success btn-lg" onclick="saveAutoModSettings()">
            <i class="fas fa-save"></i>
            حفظ جميع الإعدادات
        </button>
        <button class="btn btn-secondary btn-lg" onclick="resetToDefaults()">
            <i class="fas fa-undo"></i>
            إعادة تعيين
        </button>
        <button class="btn btn-info btn-lg" onclick="testAutoMod()">
            <i class="fas fa-vial"></i>
            اختبار النظام
        </button>
    </div>
</div>

<style>
.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.feature-card {
    border: 2px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.feature-card:hover {
    border-color: rgba(102, 126, 234, 0.5);
    transform: translateY(-5px);
}

.feature-card.danger {
    border-color: rgba(255, 107, 107, 0.3);
    background: linear-gradient(135deg, rgba(255, 107, 107, 0.1), rgba(238, 90, 82, 0.05));
}

.card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.card-header .card-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 30px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-switch label {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 30px;
}

.toggle-switch label:before {
    position: absolute;
    content: "";
    height: 22px;
    width: 22px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

.toggle-switch input:checked + label {
    background-color: #667eea;
}

.toggle-switch input:checked + label:before {
    transform: translateX(30px);
}

.setting-group {
    margin-bottom: 20px;
}

.setting-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: rgba(255, 255, 255, 0.9);
}

.allowed-links, .banned-words {
    margin-bottom: 15px;
}

.link-item, .word-tag {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.word-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 15px;
}

.word-tag {
    background: rgba(255, 107, 107, 0.2);
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.word-tag .remove-word {
    cursor: pointer;
    color: #ff6b6b;
    font-weight: bold;
}

.add-word-input {
    display: flex;
    gap: 10px;
}

.red-lines-list {
    margin-bottom: 20px;
}

.red-line-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px;
    background: rgba(255, 107, 107, 0.1);
    border-radius: 10px;
    margin-bottom: 15px;
    border-left: 4px solid #ff6b6b;
}

.red-line-content h5 {
    margin: 0 0 5px 0;
    color: #ff6b6b;
}

.red-line-content p {
    margin: 0;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
}

.add-red-line {
    display: flex;
    gap: 10px;
}

.save-section {
    text-align: center;
    padding: 30px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    margin-top: 30px;
}

.save-section .btn {
    margin: 0 10px;
}

@media (max-width: 768px) {
    .features-grid {
        grid-template-columns: 1fr;
    }
    
    .card-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .link-item, .add-word-input, .add-red-line {
        flex-direction: column;
    }
    
    .save-section .btn {
        display: block;
        width: 100%;
        margin: 10px 0;
    }
}
</style>

<script src="/static/js/automod.js"></script>

<%- include('../partials/footer') %>
