// ========================================
// 🏆 أمر لوحة المتصدرين - CS Discord
// ========================================
// 📺 يوتيوب: https://www.youtube.com/@CS_Discord
// 💬 ديسكورد: https://discord.gg/yqTn2EwVsd
// ========================================

const { EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const Currency = require('../../models/Currency');

module.exports = {
  name: 'leaderboard',
  aliases: ['lb', 'top', 'متصدرين', 'ترتيب'],
  description: 'عرض لوحة المتصدرين',
  usage: '[نوع]',
  category: 'economy',
  cooldown: 5,
  
  async execute(message, args) {
    try {
      const type = args[0] || 'balance';
      const page = parseInt(args[1]) || 1;
      
      await displayLeaderboard(message, type, page);
      
    } catch (error) {
      console.error('خطأ في أمر لوحة المتصدرين:', error);
      await message.reply('❌ حدث خطأ أثناء عرض لوحة المتصدرين!');
    }
  }
};

async function displayLeaderboard(message, type = 'balance', page = 1) {
  const itemsPerPage = 10;
  const skip = (page - 1) * itemsPerPage;
  
  let sortField, title, emoji, description;
  
  switch (type) {
    case 'balance':
    case 'رصيد':
      sortField = { balance: -1 };
      title = 'أغنى الأعضاء';
      emoji = '💰';
      description = 'الأعضاء مرتبين حسب الرصيد';
      break;
      
    case 'work':
    case 'عمل':
      sortField = { 'work.totalEarned': -1 };
      title = 'أكثر الأعضاء عملاً';
      emoji = '💼';
      description = 'الأعضاء مرتبين حسب أرباح العمل';
      break;
      
    case 'daily':
    case 'يومي':
      sortField = { 'dailyReward.streak': -1 };
      title = 'أطول تتالي يومي';
      emoji = '📅';
      description = 'الأعضاء مرتبين حسب التتالي اليومي';
      break;
      
    case 'gambling':
    case 'مقامرة':
      sortField = { 'gambling.totalWon': -1 };
      title = 'أكثر الأعضاء فوزاً في المقامرة';
      emoji = '🎰';
      description = 'الأعضاء مرتبين حسب انتصارات المقامرة';
      break;
      
    default:
      sortField = { balance: -1 };
      title = 'أغنى الأعضاء';
      emoji = '💰';
      description = 'الأعضاء مرتبين حسب الرصيد';
  }
  
  // الحصول على البيانات
  const users = await Currency.find({ guildId: message.guild.id })
    .sort(sortField)
    .skip(skip)
    .limit(itemsPerPage);
  
  const totalUsers = await Currency.countDocuments({ guildId: message.guild.id });
  const totalPages = Math.ceil(totalUsers / itemsPerPage);
  
  if (users.length === 0) {
    return await message.reply('❌ لا توجد بيانات لعرضها في لوحة المتصدرين!');
  }
  
  // إنشاء الـ embed
  const embed = new EmbedBuilder()
    .setColor('#ffd700')
    .setTitle(`🏆 ${title}`)
    .setDescription(description)
    .setThumbnail(message.guild.iconURL({ dynamic: true }))
    .setFooter({
      text: `CS Discord Economy System | صفحة ${page}/${totalPages}`,
      iconURL: message.client.user.displayAvatarURL()
    })
    .setTimestamp();
  
  // إضافة الأعضاء للقائمة
  let leaderboardText = '';
  
  for (let i = 0; i < users.length; i++) {
    const user = users[i];
    const rank = skip + i + 1;
    
    // الحصول على معلومات المستخدم من Discord
    let discordUser;
    try {
      discordUser = await message.client.users.fetch(user.userId);
    } catch (error) {
      discordUser = { username: 'مستخدم غير معروف', discriminator: '0000' };
    }
    
    // تحديد الإيموجي للمراكز الأولى
    let rankEmoji = `**${rank}.**`;
    if (rank === 1) rankEmoji = '🥇';
    else if (rank === 2) rankEmoji = '🥈';
    else if (rank === 3) rankEmoji = '🥉';
    
    // تحديد القيمة المعروضة حسب النوع
    let value;
    switch (type) {
      case 'balance':
      case 'رصيد':
        value = `${user.balance.toLocaleString('ar-EG')} عملة`;
        break;
      case 'work':
      case 'عمل':
        value = `${user.work.totalEarned.toLocaleString('ar-EG')} عملة`;
        break;
      case 'daily':
      case 'يومي':
        value = `${user.dailyReward.streak} يوم`;
        break;
      case 'gambling':
      case 'مقامرة':
        value = `${user.gambling.totalWon} فوز`;
        break;
      default:
        value = `${user.balance.toLocaleString('ar-EG')} عملة`;
    }
    
    leaderboardText += `${rankEmoji} **${discordUser.username}** - ${value}\n`;
  }
  
  embed.addFields({
    name: `${emoji} المتصدرين`,
    value: leaderboardText,
    inline: false
  });
  
  // البحث عن ترتيب المستخدم الحالي
  const allUsers = await Currency.find({ guildId: message.guild.id }).sort(sortField);
  const userRank = allUsers.findIndex(u => u.userId === message.author.id) + 1;
  
  if (userRank > 0) {
    const userCurrency = allUsers[userRank - 1];
    let userValue;
    
    switch (type) {
      case 'balance':
      case 'رصيد':
        userValue = `${userCurrency.balance.toLocaleString('ar-EG')} عملة`;
        break;
      case 'work':
      case 'عمل':
        userValue = `${userCurrency.work.totalEarned.toLocaleString('ar-EG')} عملة`;
        break;
      case 'daily':
      case 'يومي':
        userValue = `${userCurrency.dailyReward.streak} يوم`;
        break;
      case 'gambling':
      case 'مقامرة':
        userValue = `${userCurrency.gambling.totalWon} فوز`;
        break;
      default:
        userValue = `${userCurrency.balance.toLocaleString('ar-EG')} عملة`;
    }
    
    embed.addFields({
      name: '📍 ترتيبك',
      value: `المركز **#${userRank}** - ${userValue}`,
      inline: false
    });
  }
  
  // إنشاء أزرار التنقل والفئات
  const navigationRow = new ActionRowBuilder();
  const categoryRow = new ActionRowBuilder();
  
  // أزرار التنقل
  if (page > 1) {
    navigationRow.addComponents(
      new ButtonBuilder()
        .setCustomId(`lb_${type}_${page - 1}`)
        .setLabel('السابق')
        .setStyle(ButtonStyle.Primary)
        .setEmoji('⬅️')
    );
  }
  
  if (page < totalPages) {
    navigationRow.addComponents(
      new ButtonBuilder()
        .setCustomId(`lb_${type}_${page + 1}`)
        .setLabel('التالي')
        .setStyle(ButtonStyle.Primary)
        .setEmoji('➡️')
    );
  }
  
  // أزرار الفئات
  const categories = [
    { id: 'balance', label: 'الرصيد', emoji: '💰' },
    { id: 'work', label: 'العمل', emoji: '💼' },
    { id: 'daily', label: 'اليومي', emoji: '📅' },
    { id: 'gambling', label: 'المقامرة', emoji: '🎰' }
  ];
  
  categories.forEach(cat => {
    categoryRow.addComponents(
      new ButtonBuilder()
        .setCustomId(`lb_${cat.id}_1`)
        .setLabel(cat.label)
        .setStyle(type === cat.id ? ButtonStyle.Success : ButtonStyle.Secondary)
        .setEmoji(cat.emoji)
    );
  });
  
  const components = [];
  if (navigationRow.components.length > 0) components.push(navigationRow);
  components.push(categoryRow);
  
  await message.reply({ embeds: [embed], components });
}
