<%- include('../partials/header') %>

<div class="main-content">
    <div class="content-header">
        <h1 class="page-title">
            <i class="fas fa-poll"></i>
            نظام الاستطلاعات
        </h1>
        <p class="page-description">
            إنشاء وإدارة استطلاعات تفاعلية للأعضاء
        </p>
    </div>

    <!-- أدوات سريعة -->
    <div class="quick-actions">
        <button class="btn btn-success" onclick="openCreatePollModal()">
            <i class="fas fa-plus"></i>
            إنشاء استطلاع جديد
        </button>
        <button class="btn btn-info" onclick="openPollTemplatesModal()">
            <i class="fas fa-layer-group"></i>
            قوالب الاستطلاعات
        </button>
        <button class="btn btn-warning" onclick="showPollAnalytics()">
            <i class="fas fa-chart-pie"></i>
            تحليلات الاستطلاعات
        </button>
        <button class="btn btn-secondary" onclick="exportPolls()">
            <i class="fas fa-download"></i>
            تصدير النتائج
        </button>
    </div>

    <!-- إحصائيات الاستطلاعات -->
    <div class="stats-grid">
        <div class="stat-card active">
            <div class="stat-number" id="active-polls">0</div>
            <div class="stat-label">استطلاعات نشطة</div>
        </div>
        <div class="stat-card completed">
            <div class="stat-number" id="completed-polls">0</div>
            <div class="stat-label">استطلاعات مكتملة</div>
        </div>
        <div class="stat-card votes">
            <div class="stat-number" id="total-votes">0</div>
            <div class="stat-label">إجمالي الأصوات</div>
        </div>
        <div class="stat-card participation">
            <div class="stat-number" id="participation-rate">0%</div>
            <div class="stat-label">معدل المشاركة</div>
        </div>
    </div>

    <!-- قائمة الاستطلاعات -->
    <div class="card">
        <div class="card-header">
            <div class="card-icon">
                <i class="fas fa-list"></i>
            </div>
            <div>
                <h3 class="card-title">الاستطلاعات</h3>
            </div>
            <div class="card-actions">
                <select class="form-control form-control-sm" id="pollFilter" onchange="filterPolls()">
                    <option value="all">جميع الاستطلاعات</option>
                    <option value="active">النشطة</option>
                    <option value="completed">المكتملة</option>
                    <option value="draft">المسودات</option>
                </select>
                <button class="btn btn-sm btn-primary" onclick="refreshPolls()">
                    <i class="fas fa-sync"></i>
                    تحديث
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="polls-container" id="polls-container">
                <div class="loading-spinner">
                    <i class="fas fa-spinner fa-spin"></i>
                    جاري تحميل الاستطلاعات...
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal إنشاء استطلاع جديد -->
<div class="modal fade" id="createPollModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">📊 إنشاء استطلاع جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createPollForm">
                    <div class="mb-3">
                        <label class="form-label">عنوان الاستطلاع</label>
                        <input type="text" class="form-control" id="pollTitle" placeholder="ما هو لونك المفضل؟" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">وصف الاستطلاع (اختياري)</label>
                        <textarea class="form-control" id="pollDescription" rows="3" placeholder="وصف مختصر للاستطلاع..."></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">خيارات الاستطلاع</label>
                        <div class="poll-options" id="pollOptions">
                            <div class="option-item">
                                <input type="text" class="form-control" placeholder="الخيار الأول" required>
                                <div class="option-emoji">🔵</div>
                            </div>
                            <div class="option-item">
                                <input type="text" class="form-control" placeholder="الخيار الثاني" required>
                                <div class="option-emoji">🔴</div>
                            </div>
                        </div>
                        <button type="button" class="btn btn-sm btn-success" onclick="addPollOption()">
                            <i class="fas fa-plus"></i>
                            إضافة خيار
                        </button>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">القناة</label>
                                <select class="form-control" id="pollChannel" required>
                                    <option value="">اختر القناة</option>
                                    <option value="general">العام</option>
                                    <option value="polls">الاستطلاعات</option>
                                    <option value="announcements">الإعلانات</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">مدة الاستطلاع</label>
                                <select class="form-control" id="pollDuration">
                                    <option value="1">ساعة واحدة</option>
                                    <option value="6">6 ساعات</option>
                                    <option value="24" selected>24 ساعة</option>
                                    <option value="72">3 أيام</option>
                                    <option value="168">أسبوع</option>
                                    <option value="0">بدون انتهاء</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">إعدادات متقدمة</label>
                        <div class="advanced-settings">
                            <div class="setting-item">
                                <input type="checkbox" id="allowMultiple">
                                <label for="allowMultiple">السماح بالاختيار المتعدد</label>
                            </div>
                            <div class="setting-item">
                                <input type="checkbox" id="anonymousVoting">
                                <label for="anonymousVoting">التصويت المجهول</label>
                            </div>
                            <div class="setting-item">
                                <input type="checkbox" id="showResults">
                                <label for="showResults">إظهار النتائج فوراً</label>
                            </div>
                            <div class="setting-item">
                                <input type="checkbox" id="pingEveryone">
                                <label for="pingEveryone">تنبيه الجميع</label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">معاينة الاستطلاع</label>
                        <div class="poll-preview" id="pollPreview">
                            <div class="preview-title">عنوان الاستطلاع</div>
                            <div class="preview-description">وصف الاستطلاع</div>
                            <div class="preview-options">
                                <div class="preview-option">🔵 الخيار الأول</div>
                                <div class="preview-option">🔴 الخيار الثاني</div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-info" onclick="savePollDraft()">
                    <i class="fas fa-save"></i>
                    حفظ كمسودة
                </button>
                <button type="button" class="btn btn-success" onclick="createPoll()">
                    <i class="fas fa-paper-plane"></i>
                    إنشاء الاستطلاع
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal قوالب الاستطلاعات -->
<div class="modal fade" id="pollTemplatesModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">📋 قوالب الاستطلاعات</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="templates-grid">
                    <div class="template-card" onclick="applyPollTemplate('yesno')">
                        <div class="template-icon">✅</div>
                        <h6>نعم / لا</h6>
                        <p>استطلاع بسيط بخيارين</p>
                    </div>
                    <div class="template-card" onclick="applyPollTemplate('rating')">
                        <div class="template-icon">⭐</div>
                        <h6>تقييم</h6>
                        <p>تقييم من 1 إلى 5 نجوم</p>
                    </div>
                    <div class="template-card" onclick="applyPollTemplate('feedback')">
                        <div class="template-icon">💬</div>
                        <h6>رأي الأعضاء</h6>
                        <p>جمع آراء حول موضوع معين</p>
                    </div>
                    <div class="template-card" onclick="applyPollTemplate('event')">
                        <div class="template-icon">🎉</div>
                        <h6>تنظيم فعالية</h6>
                        <p>اختيار موعد أو نوع فعالية</p>
                    </div>
                    <div class="template-card" onclick="applyPollTemplate('game')">
                        <div class="template-icon">🎮</div>
                        <h6>اختيار لعبة</h6>
                        <p>التصويت على اللعبة التالية</p>
                    </div>
                    <div class="template-card" onclick="applyPollTemplate('suggestion')">
                        <div class="template-icon">💡</div>
                        <h6>اقتراحات</h6>
                        <p>التصويت على الاقتراحات</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.quick-actions {
    display: flex;
    gap: 15px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.stat-card.active {
    background: linear-gradient(135deg, #00ff88, #00d4aa);
    color: white;
}

.stat-card.completed {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.stat-card.votes {
    background: linear-gradient(135deg, #feca57, #ff9ff3);
    color: white;
}

.stat-card.participation {
    background: linear-gradient(135deg, #4ecdc4, #44a08d);
    color: white;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 10px;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.polls-container {
    min-height: 300px;
}

.loading-spinner {
    text-align: center;
    padding: 50px;
    color: rgba(255, 255, 255, 0.6);
}

.poll-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 15px;
    border-left: 4px solid;
    transition: all 0.3s ease;
}

.poll-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(5px);
}

.poll-item.active {
    border-left-color: #00ff88;
}

.poll-item.completed {
    border-left-color: #667eea;
}

.poll-item.draft {
    border-left-color: #feca57;
}

.poll-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.poll-title {
    font-size: 1.2rem;
    font-weight: bold;
    color: white;
    margin: 0;
}

.poll-status {
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: bold;
}

.poll-status.active {
    background: #00ff88;
    color: #333;
}

.poll-status.completed {
    background: #667eea;
    color: white;
}

.poll-status.draft {
    background: #feca57;
    color: #333;
}

.poll-description {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 15px;
}

.poll-options-preview {
    display: grid;
    gap: 10px;
    margin-bottom: 15px;
}

.option-preview {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: rgba(255, 255, 255, 0.1);
    padding: 10px 15px;
    border-radius: 8px;
}

.option-text {
    display: flex;
    align-items: center;
    gap: 10px;
}

.option-votes {
    font-weight: bold;
    color: #667eea;
}

.poll-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
}

.poll-actions {
    display: flex;
    gap: 10px;
}

.option-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.option-item input {
    flex: 1;
}

.option-emoji {
    width: 30px;
    height: 30px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.advanced-settings {
    background: rgba(255, 255, 255, 0.05);
    padding: 15px;
    border-radius: 10px;
}

.setting-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 10px;
}

.setting-item input[type="checkbox"] {
    width: 16px;
    height: 16px;
}

.setting-item label {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.9);
    cursor: pointer;
}

.poll-preview {
    background: rgba(255, 255, 255, 0.05);
    border: 2px dashed rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    padding: 20px;
}

.preview-title {
    font-size: 1.1rem;
    font-weight: bold;
    color: white;
    margin-bottom: 10px;
}

.preview-description {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 15px;
}

.preview-options {
    display: grid;
    gap: 8px;
}

.preview-option {
    background: rgba(255, 255, 255, 0.1);
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.9rem;
}

.templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.template-card {
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.template-card:hover {
    border-color: #667eea;
    transform: translateY(-5px);
    background: rgba(102, 126, 234, 0.1);
}

.template-icon {
    font-size: 2rem;
    margin-bottom: 10px;
}

.template-card h6 {
    color: white;
    margin-bottom: 10px;
}

.template-card p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    margin: 0;
}

@media (max-width: 768px) {
    .quick-actions {
        flex-direction: column;
    }
    
    .poll-header {
        flex-direction: column;
        gap: 10px;
    }
    
    .poll-meta {
        flex-direction: column;
        gap: 10px;
        align-items: flex-start;
    }
    
    .templates-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}
</style>

<script src="/static/js/polls.js"></script>

<%- include('../partials/footer') %>
