<% layout('layout') -%>

<div class="main-content">
    <div class="content-header">
        <h1 class="page-title">
            <i class="fas fa-coins"></i>
            نظام العملات
        </h1>
        <p class="page-description">
            إدارة نظام العملات والاقتصاد في الخادم
        </p>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number" id="total-users">0</div>
            <div class="stat-label">إجمالي المستخدمين</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" id="total-currency">0</div>
            <div class="stat-label">إجمالي العملات</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" id="daily-claims">0</div>
            <div class="stat-label">رواتب يومية اليوم</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" id="shop-sales">0</div>
            <div class="stat-label">مبيعات المتجر</div>
        </div>
    </div>

    <!-- بطاقات الإدارة -->
    <div class="cards-grid">
        <!-- إعدادات العملة -->
        <div class="card">
            <div class="card-header">
                <div class="card-icon">
                    <i class="fas fa-cog"></i>
                </div>
                <div>
                    <h3 class="card-title">إعدادات العملة</h3>
                </div>
            </div>
            <div class="card-description">
                تخصيص إعدادات نظام العملات والمكافآت
            </div>
            <div class="card-actions">
                <button class="btn btn-primary" onclick="openCurrencySettings()">
                    <i class="fas fa-edit"></i>
                    تعديل الإعدادات
                </button>
            </div>
        </div>

        <!-- إدارة المتجر -->
        <div class="card">
            <div class="card-header">
                <div class="card-icon">
                    <i class="fas fa-store"></i>
                </div>
                <div>
                    <h3 class="card-title">إدارة المتجر</h3>
                </div>
            </div>
            <div class="card-description">
                إضافة وتعديل عناصر المتجر والأسعار
            </div>
            <div class="card-actions">
                <button class="btn btn-primary" onclick="openShopManagement()">
                    <i class="fas fa-shopping-cart"></i>
                    إدارة المتجر
                </button>
            </div>
        </div>

        <!-- لوحة المتصدرين -->
        <div class="card">
            <div class="card-header">
                <div class="card-icon">
                    <i class="fas fa-trophy"></i>
                </div>
                <div>
                    <h3 class="card-title">لوحة المتصدرين</h3>
                </div>
            </div>
            <div class="card-description">
                عرض أغنى الأعضاء وأكثرهم نشاطاً
            </div>
            <div class="card-actions">
                <button class="btn btn-primary" onclick="showLeaderboard()">
                    <i class="fas fa-chart-line"></i>
                    عرض المتصدرين
                </button>
            </div>
        </div>

        <!-- إدارة الأرصدة -->
        <div class="card">
            <div class="card-header">
                <div class="card-icon">
                    <i class="fas fa-wallet"></i>
                </div>
                <div>
                    <h3 class="card-title">إدارة الأرصدة</h3>
                </div>
            </div>
            <div class="card-description">
                إضافة أو خصم عملات من أعضاء الخادم
            </div>
            <div class="card-actions">
                <button class="btn btn-primary" onclick="openBalanceManager()">
                    <i class="fas fa-plus-minus"></i>
                    إدارة الأرصدة
                </button>
            </div>
        </div>

        <!-- تقارير النشاط -->
        <div class="card">
            <div class="card-header">
                <div class="card-icon">
                    <i class="fas fa-chart-bar"></i>
                </div>
                <div>
                    <h3 class="card-title">تقارير النشاط</h3>
                </div>
            </div>
            <div class="card-description">
                عرض إحصائيات مفصلة عن النشاط الاقتصادي
            </div>
            <div class="card-actions">
                <button class="btn btn-primary" onclick="showReports()">
                    <i class="fas fa-file-chart-line"></i>
                    عرض التقارير
                </button>
            </div>
        </div>

        <!-- النسخ الاحتياطي -->
        <div class="card">
            <div class="card-header">
                <div class="card-icon">
                    <i class="fas fa-download"></i>
                </div>
                <div>
                    <h3 class="card-title">النسخ الاحتياطي</h3>
                </div>
            </div>
            <div class="card-description">
                إنشاء واستعادة نسخ احتياطية لبيانات العملات
            </div>
            <div class="card-actions">
                <button class="btn btn-secondary" onclick="createBackup()">
                    <i class="fas fa-save"></i>
                    إنشاء نسخة احتياطية
                </button>
            </div>
        </div>
    </div>

    <!-- جدول أحدث المعاملات -->
    <div class="card">
        <div class="card-header">
            <div class="card-icon">
                <i class="fas fa-history"></i>
            </div>
            <div>
                <h3 class="card-title">أحدث المعاملات</h3>
            </div>
        </div>
        <div class="card-description">
            <div class="table-responsive">
                <table class="table" id="transactions-table">
                    <thead>
                        <tr>
                            <th>المستخدم</th>
                            <th>النوع</th>
                            <th>المبلغ</th>
                            <th>الوصف</th>
                            <th>التاريخ</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td colspan="5" class="text-center">جاري التحميل...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Modal إعدادات العملة -->
<div class="modal fade" id="currencySettingsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إعدادات العملة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="currencySettingsForm">
                    <div class="mb-3">
                        <label class="form-label">الراتب اليومي الأساسي</label>
                        <input type="number" class="form-control" id="dailyReward" value="100">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">مكافأة التتالي (لكل يوم)</label>
                        <input type="number" class="form-control" id="streakBonus" value="10">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الحد الأدنى للعمل</label>
                        <input type="number" class="form-control" id="minWork" value="30">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الحد الأقصى للعمل</label>
                        <input type="number" class="form-control" id="maxWork" value="150">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveCurrencySettings()">حفظ</button>
            </div>
        </div>
    </div>
</div>

<script>
// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadEconomyStats();
    loadRecentTransactions();
});

// تحميل إحصائيات الاقتصاد
async function loadEconomyStats() {
    try {
        // محاكاة البيانات (في التطبيق الحقيقي ستأتي من API)
        document.getElementById('total-users').textContent = '156';
        document.getElementById('total-currency').textContent = '2.5M';
        document.getElementById('daily-claims').textContent = '42';
        document.getElementById('shop-sales').textContent = '18';
    } catch (error) {
        console.error('خطأ في تحميل الإحصائيات:', error);
    }
}

// تحميل أحدث المعاملات
async function loadRecentTransactions() {
    try {
        // محاكاة البيانات
        const transactions = [
            { user: 'أحمد محمد', type: 'daily', amount: '+150', description: 'راتب يومي', date: 'منذ 5 دقائق' },
            { user: 'سارة علي', type: 'work', amount: '+85', description: 'عمل كمصممة', date: 'منذ 12 دقيقة' },
            { user: 'محمد خالد', type: 'shop', amount: '-1000', description: 'شراء تعزيز XP', date: 'منذ 20 دقيقة' },
            { user: 'فاطمة أحمد', type: 'gambling', amount: '+500', description: 'فوز في المقامرة', date: 'منذ 35 دقيقة' },
            { user: 'عبدالله محمد', type: 'daily', amount: '+120', description: 'راتب يومي', date: 'منذ ساعة' }
        ];
        
        const tbody = document.querySelector('#transactions-table tbody');
        tbody.innerHTML = '';
        
        transactions.forEach(transaction => {
            const row = document.createElement('tr');
            const typeClass = transaction.amount.startsWith('+') ? 'text-success' : 'text-danger';
            
            row.innerHTML = `
                <td>${transaction.user}</td>
                <td><span class="badge badge-${getTypeColor(transaction.type)}">${getTypeLabel(transaction.type)}</span></td>
                <td class="${typeClass}">${transaction.amount}</td>
                <td>${transaction.description}</td>
                <td>${transaction.date}</td>
            `;
            
            tbody.appendChild(row);
        });
    } catch (error) {
        console.error('خطأ في تحميل المعاملات:', error);
    }
}

// دوال مساعدة
function getTypeColor(type) {
    const colors = {
        daily: 'primary',
        work: 'success',
        shop: 'warning',
        gambling: 'danger',
        admin: 'info'
    };
    return colors[type] || 'secondary';
}

function getTypeLabel(type) {
    const labels = {
        daily: 'راتب يومي',
        work: 'عمل',
        shop: 'متجر',
        gambling: 'مقامرة',
        admin: 'إداري'
    };
    return labels[type] || type;
}

// دوال الأزرار
function openCurrencySettings() {
    const modal = new bootstrap.Modal(document.getElementById('currencySettingsModal'));
    modal.show();
}

function saveCurrencySettings() {
    // حفظ الإعدادات
    alert('تم حفظ الإعدادات بنجاح!');
    bootstrap.Modal.getInstance(document.getElementById('currencySettingsModal')).hide();
}

function openShopManagement() {
    window.location.href = '/dashboard/shop-management';
}

function showLeaderboard() {
    window.location.href = '/dashboard/leaderboard';
}

function openBalanceManager() {
    alert('سيتم إضافة هذه الميزة قريباً!');
}

function showReports() {
    alert('سيتم إضافة هذه الميزة قريباً!');
}

function createBackup() {
    if (confirm('هل تريد إنشاء نسخة احتياطية من بيانات العملات؟')) {
        alert('تم إنشاء النسخة الاحتياطية بنجاح!');
    }
}
</script>
