// ========================================
// 🏓 أمر Ping - CS Discord
// ========================================
// 📺 يوتيوب: https://www.youtube.com/@CS_Discord
// 💬 ديسكورد: https://discord.gg/yqTn2EwVsd
// ========================================

const { EmbedBuilder } = require('discord.js');

module.exports = {
  name: 'ping',
  aliases: ['بينغ', 'p'],
  description: 'عرض ping البوت',
  usage: 'ping',
  category: 'general',
  cooldown: 3,
  
  async execute(message, args, client) {
    try {
      const sent = await message.reply('🏓 جاري قياس البينغ...');
      
      const embed = new EmbedBuilder()
        .setColor('#00ff88')
        .setTitle('🏓 Pong!')
        .addFields(
          {
            name: '📡 Latency',
            value: `${sent.createdTimestamp - message.createdTimestamp}ms`,
            inline: true
          },
          {
            name: '💓 API Latency',
            value: `${Math.round(client.ws.ping)}ms`,
            inline: true
          },
          {
            name: '🤖 Status',
            value: 'Online ✅',
            inline: true
          }
        )
        .setFooter({
          text: 'CS Discord Bot',
          iconURL: client.user.displayAvatarURL()
        })
        .setTimestamp();
      
      await sent.edit({ content: null, embeds: [embed] });
      
    } catch (error) {
      console.error('خطأ في أمر ping:', error);
      await message.reply('❌ حدث خطأ أثناء قياس البينغ!');
    }
  }
};
