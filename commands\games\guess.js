// ========================================
// 🎯 لعبة تخمين الرقم - CS Discord
// ========================================
// 📺 يوتيوب: https://www.youtube.com/@CS_Discord
// 💬 ديسكورد: https://discord.gg/yqTn2EwVsd
// ========================================

const { EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');

// تخزين الألعاب النشطة
const activeGames = new Map();

module.exports = {
  name: 'guess',
  aliases: ['تخمين', 'number', 'رقم'],
  description: 'خمن الرقم السري من 1 إلى 100',
  usage: '[الرقم] أو بدون معاملات للعب التفاعلي',
  category: 'games',
  cooldown: 5,
  
  async execute(message, args) {
    try {
      // إذا كان هناك رقم مُدخل
      if (args[0]) {
        const guess = parseInt(args[0]);
        
        if (isNaN(guess) || guess < 1 || guess > 100) {
          return await message.reply('❌ يجب أن يكون الرقم بين 1 و 100!');
        }
        
        return await handleQuickGuess(message, guess);
      }
      
      // بدء لعبة تفاعلية
      await startInteractiveGame(message);
      
    } catch (error) {
      console.error('خطأ في لعبة تخمين الرقم:', error);
      await message.reply('❌ حدث خطأ أثناء اللعبة!');
    }
  }
};

// تخمين سريع
async function handleQuickGuess(message, guess) {
  const secretNumber = Math.floor(Math.random() * 100) + 1;
  const difference = Math.abs(guess - secretNumber);
  
  let result, color, emoji;
  
  if (guess === secretNumber) {
    result = 'مبروك! خمنت الرقم بالضبط!';
    color = '#00ff00';
    emoji = '🎉';
  } else if (difference <= 5) {
    result = 'قريب جداً! كنت على بُعد خطوات قليلة!';
    color = '#ffff00';
    emoji = '🔥';
  } else if (difference <= 15) {
    result = 'قريب! لكن ليس بما فيه الكفاية!';
    color = '#ffa500';
    emoji = '👍';
  } else {
    result = 'بعيد جداً! حاول مرة أخرى!';
    color = '#ff0000';
    emoji = '😅';
  }
  
  const embed = new EmbedBuilder()
    .setColor(color)
    .setTitle(`${emoji} نتيجة التخمين`)
    .addFields(
      {
        name: '🎯 تخمينك',
        value: `**${guess}**`,
        inline: true
      },
      {
        name: '🔢 الرقم السري',
        value: `**${secretNumber}**`,
        inline: true
      },
      {
        name: '📊 الفرق',
        value: `**${difference}**`,
        inline: true
      },
      {
        name: '📝 النتيجة',
        value: result,
        inline: false
      }
    )
    .setFooter({
      text: 'CS Discord Games',
      iconURL: message.client.user.displayAvatarURL()
    })
    .setTimestamp();
  
  await message.reply({ embeds: [embed] });
}

// بدء لعبة تفاعلية
async function startInteractiveGame(message) {
  const gameId = `${message.author.id}-${Date.now()}`;
  const secretNumber = Math.floor(Math.random() * 100) + 1;
  
  const game = {
    secretNumber,
    attempts: 0,
    maxAttempts: 7,
    guesses: [],
    startTime: Date.now(),
    playerId: message.author.id
  };
  
  activeGames.set(gameId, game);
  
  const embed = new EmbedBuilder()
    .setColor('#0099ff')
    .setTitle('🎯 لعبة تخمين الرقم')
    .setDescription('لقد اخترت رقم سري بين **1** و **100**!\nلديك **7 محاولات** لتخمينه!')
    .addFields(
      {
        name: '🎮 كيفية اللعب',
        value: '• اختر رقم من 1 إلى 100\n• سأخبرك إذا كان الرقم أعلى أم أقل\n• لديك 7 محاولات فقط!',
        inline: false
      },
      {
        name: '📊 المحاولات المتبقية',
        value: `**${game.maxAttempts}** محاولة`,
        inline: true
      }
    )
    .setFooter({
      text: 'CS Discord Games',
      iconURL: message.client.user.displayAvatarURL()
    })
    .setTimestamp();
  
  const buttons = createNumberButtons();
  const gameMessage = await message.reply({ embeds: [embed], components: buttons });
  
  // إعداد collector
  const filter = (interaction) => {
    return interaction.user.id === message.author.id && 
           interaction.customId.startsWith('guess_');
  };
  
  const collector = gameMessage.createMessageComponentCollector({ 
    filter, 
    time: 300000 // 5 دقائق
  });
  
  collector.on('collect', async (interaction) => {
    const guess = parseInt(interaction.customId.split('_')[1]);
    await handleGuess(interaction, gameId, guess);
  });
  
  collector.on('end', (collected, reason) => {
    if (reason === 'time') {
      activeGames.delete(gameId);
      
      const timeoutEmbed = new EmbedBuilder()
        .setColor('#ffa500')
        .setTitle('⏰ انتهت مهلة اللعبة')
        .setDescription(`انتهت مهلة اللعبة!\nالرقم السري كان: **${game.secretNumber}**`)
        .setTimestamp();
      
      gameMessage.edit({ embeds: [timeoutEmbed], components: [] });
    }
  });
}

// التعامل مع التخمين
async function handleGuess(interaction, gameId, guess) {
  const game = activeGames.get(gameId);
  
  if (!game) {
    await interaction.reply({ content: '❌ اللعبة غير موجودة!', ephemeral: true });
    return;
  }
  
  game.attempts++;
  game.guesses.push(guess);
  
  let result, color, gameOver = false;
  
  if (guess === game.secretNumber) {
    // فوز!
    result = '🎉 مبروك! خمنت الرقم الصحيح!';
    color = '#00ff00';
    gameOver = true;
  } else if (game.attempts >= game.maxAttempts) {
    // انتهت المحاولات
    result = `😢 انتهت محاولاتك! الرقم السري كان: **${game.secretNumber}**`;
    color = '#ff0000';
    gameOver = true;
  } else {
    // استمرار اللعبة
    const hint = guess > game.secretNumber ? 'أقل ⬇️' : 'أعلى ⬆️';
    result = `الرقم السري ${hint}`;
    color = '#ffa500';
  }
  
  const embed = new EmbedBuilder()
    .setColor(color)
    .setTitle('🎯 لعبة تخمين الرقم')
    .addFields(
      {
        name: '🎯 تخمينك',
        value: `**${guess}**`,
        inline: true
      },
      {
        name: '📊 المحاولات المتبقية',
        value: `**${game.maxAttempts - game.attempts}**`,
        inline: true
      },
      {
        name: '📝 النتيجة',
        value: result,
        inline: false
      }
    )
    .setTimestamp();
  
  // إضافة تاريخ التخمينات
  if (game.guesses.length > 1) {
    embed.addFields({
      name: '📋 تخميناتك السابقة',
      value: game.guesses.slice(0, -1).join(' - '),
      inline: false
    });
  }
  
  if (gameOver) {
    activeGames.delete(gameId);
    
    // إضافة إحصائيات اللعبة
    const gameTime = Math.floor((Date.now() - game.startTime) / 1000);
    const score = calculateScore(game.attempts, game.maxAttempts, gameTime);
    
    embed.addFields(
      {
        name: '📈 إحصائيات اللعبة',
        value: `⏱️ الوقت: ${gameTime} ثانية\n🎯 المحاولات: ${game.attempts}/${game.maxAttempts}\n⭐ النقاط: ${score}`,
        inline: false
      }
    );
    
    await interaction.update({ embeds: [embed], components: [] });
  } else {
    const buttons = createNumberButtons();
    await interaction.update({ embeds: [embed], components: buttons });
  }
}

// إنشاء أزرار الأرقام
function createNumberButtons() {
  const rows = [];
  
  // أرقام 1-25
  let row1 = new ActionRowBuilder();
  for (let i = 1; i <= 5; i++) {
    row1.addComponents(
      new ButtonBuilder()
        .setCustomId(`guess_${i}`)
        .setLabel(`${i}`)
        .setStyle(ButtonStyle.Primary)
    );
  }
  rows.push(row1);
  
  // أرقام خاصة
  let row2 = new ActionRowBuilder();
  const specialNumbers = [10, 25, 50, 75, 100];
  specialNumbers.forEach(num => {
    row2.addComponents(
      new ButtonBuilder()
        .setCustomId(`guess_${num}`)
        .setLabel(`${num}`)
        .setStyle(ButtonStyle.Secondary)
    );
  });
  rows.push(row2);
  
  return rows;
}

// حساب النقاط
function calculateScore(attempts, maxAttempts, timeSeconds) {
  const baseScore = 1000;
  const attemptPenalty = (attempts - 1) * 100;
  const timePenalty = Math.floor(timeSeconds / 10) * 5;
  
  return Math.max(baseScore - attemptPenalty - timePenalty, 100);
}
