// ========================================
// ⏱️ أمر السلو مود - CS Discord
// ========================================
// 📺 يوتيوب: https://www.youtube.com/@CS_Discord
// 💬 ديسكورد: https://discord.gg/yqTn2EwVsd
// ========================================

const { EmbedBuilder, PermissionFlagsBits } = require('discord.js');

module.exports = {
  name: 'slowmode',
  aliases: ['slow', 'sm', 'سلو', 'بطيء'],
  description: 'ضبط السلو مود في الروم',
  usage: '<الثواني> [الروم]',
  category: 'moderation',
  permissions: [PermissionFlagsBits.ManageChannels],
  cooldown: 3,
  
  async execute(message, args) {
    try {
      // التحقق من الصلاحيات
      if (!message.member.permissions.has(PermissionFlagsBits.ManageChannels)) {
        return await message.reply('❌ ليس لديك صلاحية لإدارة الرومات!');
      }
      
      // التحقق من وجود المدة
      if (!args[0]) {
        return await message.reply('❌ يجب تحديد مدة السلو مود بالثواني!\nمثال: `slowmode 10`');
      }
      
      // تحويل المدة إلى رقم
      const seconds = parseInt(args[0]);
      
      // التحقق من صحة المدة
      if (isNaN(seconds) || seconds < 0 || seconds > 21600) {
        return await message.reply('❌ يجب أن تكون المدة بين 0 و 21600 ثانية (6 ساعات)!');
      }
      
      // تحديد الروم
      const channel = message.mentions.channels.first() || message.channel;
      
      // التحقق من نوع الروم
      if (!channel.isTextBased()) {
        return await message.reply('❌ يمكن تطبيق السلو مود على الرومات النصية فقط!');
      }
      
      // تطبيق السلو مود
      await channel.setRateLimitPerUser(seconds);
      
      // إنشاء الـ embed
      const embed = new EmbedBuilder()
        .setColor(seconds === 0 ? '#00ff00' : '#ffa500')
        .setTitle('⏱️ تم تحديث السلو مود')
        .addFields(
          {
            name: '📍 الروم',
            value: `${channel}`,
            inline: true
          },
          {
            name: '⏰ المدة',
            value: seconds === 0 ? 'تم إلغاء السلو مود' : `${seconds} ثانية`,
            inline: true
          },
          {
            name: '👤 بواسطة',
            value: `${message.author}`,
            inline: true
          }
        )
        .setFooter({
          text: 'CS Discord Moderation System',
          iconURL: message.client.user.displayAvatarURL()
        })
        .setTimestamp();
      
      // إضافة وصف حسب المدة
      if (seconds === 0) {
        embed.setDescription('✅ تم إلغاء السلو مود من الروم');
      } else {
        embed.setDescription(`🔒 الآن الأعضاء يجب أن ينتظروا ${seconds} ثانية بين كل رسالة`);
      }
      
      await message.reply({ embeds: [embed] });
      
      // إرسال لوج إذا كان مفعل
      const logChannel = message.guild.channels.cache.find(ch => ch.name === 'mod-logs');
      if (logChannel) {
        const logEmbed = new EmbedBuilder()
          .setColor('#ffa500')
          .setTitle('📝 سجل الإدارة - السلو مود')
          .addFields(
            {
              name: 'المشرف',
              value: `${message.author.tag} (${message.author.id})`,
              inline: true
            },
            {
              name: 'الروم',
              value: `${channel.name} (${channel.id})`,
              inline: true
            },
            {
              name: 'المدة الجديدة',
              value: seconds === 0 ? 'تم الإلغاء' : `${seconds} ثانية`,
              inline: true
            }
          )
          .setTimestamp();
        
        await logChannel.send({ embeds: [logEmbed] });
      }
      
    } catch (error) {
      console.error('خطأ في أمر السلو مود:', error);
      await message.reply('❌ حدث خطأ أثناء تطبيق السلو مود!');
    }
  }
};
