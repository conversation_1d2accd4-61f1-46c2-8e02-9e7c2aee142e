"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const ava_1 = __importDefault(require("ava"));
const mongodb_1 = require("mongodb");
const MongoStore_1 = __importDefault(require("./MongoStore"));
const testHelper_1 = require("../test/testHelper");
let { store, storePromise } = (0, testHelper_1.createStoreHelper)();
ava_1.default.before(async () => {
    await storePromise.clear().catch((err) => {
        if (err.message.match(/ns not found/)) {
            return null;
        }
        else {
            throw err;
        }
    });
});
ava_1.default.afterEach.always(async () => {
    await storePromise.close();
});
ava_1.default.serial('create store w/o provide required options', (t) => {
    t.throws(() => MongoStore_1.default.create({}), {
        message: /Cannot init client/,
    });
});
ava_1.default.serial('create store with clientPromise', async (t) => {
    const clientP = mongodb_1.MongoClient.connect('**************************************');
    const store = MongoStore_1.default.create({ clientPromise: clientP });
    t.not(store, null);
    t.not(store, undefined);
    await store.collectionP;
    store.close();
});
ava_1.default.serial('create store with client', async (t) => {
    const client = await mongodb_1.MongoClient.connect('**************************************');
    const store = MongoStore_1.default.create({ client: client });
    t.not(store, null);
    t.not(store, undefined);
    await store.collectionP;
    store.close();
});
ava_1.default.serial('length should be 0', async (t) => {
    ;
    ({ store, storePromise } = (0, testHelper_1.createStoreHelper)());
    const length = await storePromise.length();
    t.is(length, 0);
});
ava_1.default.serial('get non-exist session should throw error', async (t) => {
    ;
    ({ store, storePromise } = (0, testHelper_1.createStoreHelper)());
    const res = await storePromise.get('fake-sid');
    t.is(res, null);
});
ava_1.default.serial('get all session should work for no session', async (t) => {
    ;
    ({ store, storePromise } = (0, testHelper_1.createStoreHelper)());
    const allSessions = await storePromise.all();
    t.deepEqual(allSessions, []);
});
ava_1.default.serial('basic operation flow', async (t) => {
    ;
    ({ store, storePromise } = (0, testHelper_1.createStoreHelper)());
    let orgSession = (0, testHelper_1.makeData)();
    const sid = 'test-basic-flow';
    const res = await storePromise.set(sid, orgSession);
    t.is(res, undefined);
    const session = await storePromise.get(sid);
    t.is(typeof session, 'object');
    orgSession = JSON.parse(JSON.stringify(orgSession));
    t.deepEqual(session, orgSession);
    const allSessions = await storePromise.all();
    t.deepEqual(allSessions, [orgSession]);
    t.is(await storePromise.length(), 1);
    const err = await storePromise.destroy(sid);
    t.is(err, undefined);
    t.is(await storePromise.length(), 0);
});
ava_1.default.serial.cb('set and listen to event', (t) => {
    ;
    ({ store, storePromise } = (0, testHelper_1.createStoreHelper)());
    let orgSession = (0, testHelper_1.makeData)();
    const sid = 'test-set-event';
    store.set(sid, orgSession);
    orgSession = JSON.parse(JSON.stringify(orgSession));
    store.on('set', (sessionId) => {
        t.is(sessionId, sid);
        store.get(sid, (err, session) => {
            t.is(err, null);
            t.is(typeof session, 'object');
            t.deepEqual(session, orgSession);
            t.end();
        });
    });
});
ava_1.default.serial.cb('set and listen to create event', (t) => {
    ;
    ({ store, storePromise } = (0, testHelper_1.createStoreHelper)());
    const orgSession = (0, testHelper_1.makeData)();
    const sid = 'test-create-event';
    store.set(sid, orgSession);
    store.on('create', (sessionId) => {
        t.is(sessionId, sid);
        t.end();
    });
});
ava_1.default.serial.cb('set and listen to update event', (t) => {
    ;
    ({ store, storePromise } = (0, testHelper_1.createStoreHelper)());
    const orgSession = (0, testHelper_1.makeData)();
    const sid = 'test-update-event';
    store.set(sid, orgSession);
    store.set(sid, { ...orgSession, foo: 'new-bar' });
    store.on('update', (sessionId) => {
        t.is(sessionId, sid);
        t.end();
    });
});
ava_1.default.serial('set with no stringify', async (t) => {
    ;
    ({ store, storePromise } = (0, testHelper_1.createStoreHelper)({ stringify: false }));
    const orgSession = (0, testHelper_1.makeData)();
    const cookie = orgSession.cookie;
    const sid = 'test-no-stringify';
    const res = await storePromise.set(sid, orgSession);
    t.is(res, undefined);
    const session = await storePromise.get(sid);
    t.is(typeof session, 'object');
    t.deepEqual(orgSession.cookie, cookie);
    // @ts-ignore
    t.deepEqual(cookie.expires.toJSON(), session.cookie.expires.toJSON());
    // @ts-ignore
    t.deepEqual(cookie.secure, session.cookie.secure);
    const err = await storePromise.clear();
    t.is(err, undefined);
    t.is(await storePromise.length(), 0);
});
ava_1.default.serial.cb('test destory event', (t) => {
    ;
    ({ store, storePromise } = (0, testHelper_1.createStoreHelper)());
    const orgSession = (0, testHelper_1.makeData)();
    const sid = 'test-destory-event';
    store.on('destroy', (sessionId) => {
        t.is(sessionId, sid);
        t.end();
    });
    storePromise.set(sid, orgSession).then(() => {
        store.destroy(sid);
    });
});
ava_1.default.serial('test set default TTL', async (t) => {
    var _a;
    const defaultTTL = 10;
    ({ store, storePromise } = (0, testHelper_1.createStoreHelper)({
        ttl: defaultTTL,
    }));
    const orgSession = (0, testHelper_1.makeDataNoCookie)();
    const sid = 'test-set-default-ttl';
    const timeBeforeSet = new Date().valueOf();
    // @ts-ignore
    await storePromise.set(sid, orgSession);
    const collection = await store.collectionP;
    const session = await collection.findOne({ _id: sid });
    const timeAfterSet = new Date().valueOf();
    const expires = (_a = session === null || session === void 0 ? void 0 : session.expires) === null || _a === void 0 ? void 0 : _a.valueOf();
    t.truthy(expires);
    if (expires) {
        t.truthy(timeBeforeSet + defaultTTL * 1000 <= expires);
        t.truthy(expires <= timeAfterSet + defaultTTL * 1000);
    }
});
ava_1.default.serial('test default TTL', async (t) => {
    var _a;
    const defaultExpirationTime = 1000 * 60 * 60 * 24 * 14;
    ({ store, storePromise } = (0, testHelper_1.createStoreHelper)());
    const orgSession = (0, testHelper_1.makeDataNoCookie)();
    const sid = 'test-no-set-default-ttl';
    const timeBeforeSet = new Date().valueOf();
    // @ts-ignore
    await storePromise.set(sid, orgSession);
    const collection = await store.collectionP;
    const session = await collection.findOne({ _id: sid });
    const timeAfterSet = new Date().valueOf();
    const expires = (_a = session === null || session === void 0 ? void 0 : session.expires) === null || _a === void 0 ? void 0 : _a.valueOf();
    t.truthy(expires);
    if (expires) {
        t.truthy(timeBeforeSet + defaultExpirationTime <= expires);
        t.truthy(expires <= timeAfterSet + defaultExpirationTime);
    }
});
ava_1.default.serial('test custom serializer', async (t) => {
    ;
    ({ store, storePromise } = (0, testHelper_1.createStoreHelper)({
        serialize: (obj) => {
            obj.ice = 'test-ice-serializer';
            return JSON.stringify(obj);
        },
    }));
    const orgSession = (0, testHelper_1.makeData)();
    const sid = 'test-custom-serializer';
    await storePromise.set(sid, orgSession);
    const session = await storePromise.get(sid);
    t.is(typeof session, 'string');
    t.not(session, undefined);
    // @ts-ignore
    orgSession.ice = 'test-ice-serializer';
    // @ts-ignore
    t.is(session, JSON.stringify(orgSession));
});
ava_1.default.serial('test custom deserializer', async (t) => {
    ;
    ({ store, storePromise } = (0, testHelper_1.createStoreHelper)({
        unserialize: (obj) => {
            obj.ice = 'test-ice-deserializer';
            return obj;
        },
    }));
    const orgSession = (0, testHelper_1.makeData)();
    const sid = 'test-custom-deserializer';
    await storePromise.set(sid, orgSession);
    const session = await storePromise.get(sid);
    t.is(typeof session, 'object');
    // @ts-ignore
    orgSession.cookie = orgSession.cookie.toJSON();
    // @ts-ignore
    orgSession.ice = 'test-ice-deserializer';
    t.not(session, undefined);
    t.deepEqual(session, orgSession);
});
ava_1.default.serial('touch ops', async (t) => {
    var _a, _b, _c, _d;
    ;
    ({ store, storePromise } = (0, testHelper_1.createStoreHelper)());
    const orgSession = (0, testHelper_1.makeDataNoCookie)();
    const sid = 'test-touch';
    // @ts-ignore
    await storePromise.set(sid, orgSession);
    const collection = await store.collectionP;
    const session = await collection.findOne({ _id: sid });
    await new Promise((resolve) => setTimeout(resolve, 500));
    t.not(session, undefined);
    await storePromise.touch(sid, session === null || session === void 0 ? void 0 : session.session);
    const session2 = await collection.findOne({ _id: sid });
    t.not(session2, undefined);
    // Check if both expiry date are different
    t.truthy((_a = session2 === null || session2 === void 0 ? void 0 : session2.expires) === null || _a === void 0 ? void 0 : _a.getTime());
    t.truthy((_b = session === null || session === void 0 ? void 0 : session.expires) === null || _b === void 0 ? void 0 : _b.getTime());
    if (((_c = session === null || session === void 0 ? void 0 : session.expires) === null || _c === void 0 ? void 0 : _c.getTime()) && ((_d = session2 === null || session2 === void 0 ? void 0 : session2.expires) === null || _d === void 0 ? void 0 : _d.getTime())) {
        t.truthy((session2 === null || session2 === void 0 ? void 0 : session2.expires.getTime()) > (session === null || session === void 0 ? void 0 : session.expires.getTime()));
    }
});
ava_1.default.serial('touch ops with touchAfter', async (t) => {
    var _a, _b;
    ;
    ({ store, storePromise } = (0, testHelper_1.createStoreHelper)({ touchAfter: 1 }));
    const orgSession = (0, testHelper_1.makeDataNoCookie)();
    const sid = 'test-touch-with-touchAfter';
    // @ts-ignore
    await storePromise.set(sid, orgSession);
    const collection = await store.collectionP;
    const session = await collection.findOne({ _id: sid });
    const lastModifiedBeforeTouch = (_a = session === null || session === void 0 ? void 0 : session.lastModified) === null || _a === void 0 ? void 0 : _a.getTime();
    t.not(session, undefined);
    await storePromise.touch(sid, session);
    const session2 = await collection.findOne({ _id: sid });
    t.not(session2, undefined);
    const lastModifiedAfterTouch = (_b = session2 === null || session2 === void 0 ? void 0 : session2.lastModified) === null || _b === void 0 ? void 0 : _b.getTime();
    // Check if both expiry date are different
    t.is(lastModifiedBeforeTouch, lastModifiedAfterTouch);
});
ava_1.default.serial('touch ops with touchAfter with touch', async (t) => {
    var _a, _b;
    ;
    ({ store, storePromise } = (0, testHelper_1.createStoreHelper)({ touchAfter: 1 }));
    const orgSession = (0, testHelper_1.makeDataNoCookie)();
    const sid = 'test-touch-with-touchAfter-should-touch';
    // @ts-ignore
    await storePromise.set(sid, orgSession);
    const collection = await store.collectionP;
    const session = await collection.findOne({ _id: sid });
    const lastModifiedBeforeTouch = (_a = session === null || session === void 0 ? void 0 : session.lastModified) === null || _a === void 0 ? void 0 : _a.getTime();
    await new Promise((resolve) => setTimeout(resolve, 1200));
    t.not(session, undefined);
    await storePromise.touch(sid, session);
    const session2 = await collection.findOne({ _id: sid });
    t.not(session2, undefined);
    const lastModifiedAfterTouch = (_b = session2 === null || session2 === void 0 ? void 0 : session2.lastModified) === null || _b === void 0 ? void 0 : _b.getTime();
    // Check if both expiry date are different
    t.truthy(lastModifiedAfterTouch);
    t.truthy(lastModifiedBeforeTouch);
    if (lastModifiedAfterTouch && lastModifiedBeforeTouch) {
        t.truthy(lastModifiedAfterTouch > lastModifiedBeforeTouch);
    }
});
ava_1.default.serial('basic operation flow with crypto', async (t) => {
    ;
    ({ store, storePromise } = (0, testHelper_1.createStoreHelper)({
        crypto: { secret: 'secret' },
        collectionName: 'crypto-test',
        autoRemove: 'disabled',
    }));
    let orgSession = (0, testHelper_1.makeData)();
    const sid = 'test-basic-flow-with-crypto';
    const res = await storePromise.set(sid, orgSession);
    t.is(res, undefined);
    const session = await storePromise.get(sid);
    orgSession = JSON.parse(JSON.stringify(orgSession));
    // @ts-ignore
    t.deepEqual(session, orgSession);
    const sessions = await storePromise.all();
    t.not(sessions, undefined);
    t.not(sessions, null);
    t.is(sessions === null || sessions === void 0 ? void 0 : sessions.length, 1);
});
ava_1.default.serial('with touch after and get non-exist session', async (t) => {
    ;
    ({ store, storePromise } = (0, testHelper_1.createStoreHelper)({
        touchAfter: 10,
    }));
    const sid = 'fake-sid-for-test-touch-after';
    const res = await storePromise.get(sid);
    t.is(res, null);
});
//# sourceMappingURL=data:application/json;base64,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