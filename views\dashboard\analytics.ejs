<%- include('../partials/header') %>

<div class="main-content">
    <div class="content-header">
        <h1 class="page-title">
            <i class="fas fa-chart-line"></i>
            التحليلات المتقدمة
        </h1>
        <p class="page-description">
            تحليلات شاملة ومفصلة لأداء الخادم والبوت
        </p>
    </div>

    <!-- فلاتر التحليلات -->
    <div class="analytics-filters">
        <div class="filter-group">
            <label>الفترة الزمنية:</label>
            <select class="form-control" id="timeRange" onchange="updateAnalytics()">
                <option value="24h">آخر 24 ساعة</option>
                <option value="7d" selected>آخر 7 أيام</option>
                <option value="30d">آخر 30 يوم</option>
                <option value="90d">آخر 3 أشهر</option>
                <option value="1y">آخر سنة</option>
            </select>
        </div>
        
        <div class="filter-group">
            <label>نوع التحليل:</label>
            <select class="form-control" id="analyticsType" onchange="updateAnalytics()">
                <option value="overview">نظرة عامة</option>
                <option value="members">الأعضاء</option>
                <option value="messages">الرسائل</option>
                <option value="voice">النشاط الصوتي</option>
                <option value="commands">الأوامر</option>
            </select>
        </div>
        
        <div class="filter-group">
            <button class="btn btn-primary" onclick="exportAnalytics()">
                <i class="fas fa-download"></i>
                تصدير التقرير
            </button>
        </div>
    </div>

    <!-- إحصائيات رئيسية -->
    <div class="stats-grid">
        <div class="stat-card members">
            <div class="stat-icon">
                <i class="fas fa-users"></i>
            </div>
            <div class="stat-content">
                <div class="stat-number" id="total-members">1,247</div>
                <div class="stat-label">إجمالي الأعضاء</div>
                <div class="stat-change positive">+15% هذا الأسبوع</div>
            </div>
        </div>
        
        <div class="stat-card messages">
            <div class="stat-icon">
                <i class="fas fa-comments"></i>
            </div>
            <div class="stat-content">
                <div class="stat-number" id="total-messages">45,678</div>
                <div class="stat-label">إجمالي الرسائل</div>
                <div class="stat-change positive">+23% هذا الأسبوع</div>
            </div>
        </div>
        
        <div class="stat-card voice">
            <div class="stat-icon">
                <i class="fas fa-microphone"></i>
            </div>
            <div class="stat-content">
                <div class="stat-number" id="voice-hours">2,340</div>
                <div class="stat-label">ساعات صوتية</div>
                <div class="stat-change positive">+8% هذا الأسبوع</div>
            </div>
        </div>
        
        <div class="stat-card engagement">
            <div class="stat-icon">
                <i class="fas fa-heart"></i>
            </div>
            <div class="stat-content">
                <div class="stat-number" id="engagement-rate">87%</div>
                <div class="stat-label">معدل التفاعل</div>
                <div class="stat-change positive">+5% هذا الأسبوع</div>
            </div>
        </div>
    </div>

    <!-- الرسوم البيانية -->
    <div class="charts-container">
        <div class="chart-card">
            <div class="chart-header">
                <h3>نمو الأعضاء</h3>
                <div class="chart-controls">
                    <button class="btn btn-sm btn-outline-primary active" onclick="switchChart('members', 'daily')">يومي</button>
                    <button class="btn btn-sm btn-outline-primary" onclick="switchChart('members', 'weekly')">أسبوعي</button>
                    <button class="btn btn-sm btn-outline-primary" onclick="switchChart('members', 'monthly')">شهري</button>
                </div>
            </div>
            <div class="chart-body">
                <canvas id="membersChart" width="400" height="200"></canvas>
            </div>
        </div>
        
        <div class="chart-card">
            <div class="chart-header">
                <h3>نشاط الرسائل</h3>
                <div class="chart-controls">
                    <button class="btn btn-sm btn-outline-success active" onclick="switchChart('messages', 'hourly')">ساعي</button>
                    <button class="btn btn-sm btn-outline-success" onclick="switchChart('messages', 'daily')">يومي</button>
                </div>
            </div>
            <div class="chart-body">
                <canvas id="messagesChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>

    <!-- تحليلات مفصلة -->
    <div class="detailed-analytics">
        <div class="card">
            <div class="card-header">
                <div class="card-icon">
                    <i class="fas fa-trophy"></i>
                </div>
                <div>
                    <h3 class="card-title">أكثر الأعضاء نشاطاً</h3>
                </div>
            </div>
            <div class="card-body">
                <div class="top-members" id="top-members">
                    <div class="loading-spinner">
                        <i class="fas fa-spinner fa-spin"></i>
                        جاري تحميل البيانات...
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <div class="card-icon">
                    <i class="fas fa-hashtag"></i>
                </div>
                <div>
                    <h3 class="card-title">أكثر القنوات استخداماً</h3>
                </div>
            </div>
            <div class="card-body">
                <div class="top-channels" id="top-channels">
                    <div class="loading-spinner">
                        <i class="fas fa-spinner fa-spin"></i>
                        جاري تحميل البيانات...
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <div class="card-icon">
                    <i class="fas fa-terminal"></i>
                </div>
                <div>
                    <h3 class="card-title">أكثر الأوامر استخداماً</h3>
                </div>
            </div>
            <div class="card-body">
                <div class="top-commands" id="top-commands">
                    <div class="loading-spinner">
                        <i class="fas fa-spinner fa-spin"></i>
                        جاري تحميل البيانات...
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- تحليل الأوقات -->
    <div class="card">
        <div class="card-header">
            <div class="card-icon">
                <i class="fas fa-clock"></i>
            </div>
            <div>
                <h3 class="card-title">تحليل أوقات النشاط</h3>
            </div>
        </div>
        <div class="card-body">
            <div class="activity-heatmap">
                <div class="heatmap-header">
                    <div class="heatmap-title">خريطة النشاط الأسبوعية</div>
                    <div class="heatmap-legend">
                        <span>أقل</span>
                        <div class="legend-scale">
                            <div class="legend-item level-1"></div>
                            <div class="legend-item level-2"></div>
                            <div class="legend-item level-3"></div>
                            <div class="legend-item level-4"></div>
                            <div class="legend-item level-5"></div>
                        </div>
                        <span>أكثر</span>
                    </div>
                </div>
                <div class="heatmap-grid" id="activity-heatmap">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.analytics-filters {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
    background: rgba(255, 255, 255, 0.05);
    padding: 20px;
    border-radius: 15px;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
    min-width: 200px;
}

.filter-group label {
    color: rgba(255, 255, 255, 0.9);
    font-weight: bold;
    font-size: 0.9rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 25px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 20px;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.stat-card.members {
    border-left: 4px solid #4ecdc4;
}

.stat-card.messages {
    border-left: 4px solid #667eea;
}

.stat-card.voice {
    border-left: 4px solid #feca57;
}

.stat-card.engagement {
    border-left: 4px solid #ff6b6b;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-card.members .stat-icon {
    background: linear-gradient(45deg, #4ecdc4, #44a08d);
}

.stat-card.messages .stat-icon {
    background: linear-gradient(45deg, #667eea, #764ba2);
}

.stat-card.voice .stat-icon {
    background: linear-gradient(45deg, #feca57, #ff9ff3);
}

.stat-card.engagement .stat-icon {
    background: linear-gradient(45deg, #ff6b6b, #ee5a52);
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: white;
    margin-bottom: 5px;
}

.stat-label {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.stat-change {
    font-size: 0.8rem;
    font-weight: bold;
}

.stat-change.positive {
    color: #00ff88;
}

.stat-change.negative {
    color: #ff6b6b;
}

.charts-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 30px;
    margin-bottom: 30px;
}

.chart-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    overflow: hidden;
}

.chart-header {
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chart-header h3 {
    color: white;
    margin: 0;
    font-size: 1.1rem;
}

.chart-controls {
    display: flex;
    gap: 5px;
}

.chart-body {
    padding: 20px;
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.detailed-analytics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.top-members, .top-channels, .top-commands {
    min-height: 200px;
}

.ranking-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    margin-bottom: 10px;
    transition: all 0.3s ease;
}

.ranking-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(5px);
}

.ranking-number {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: linear-gradient(45deg, #667eea, #764ba2);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 0.9rem;
}

.ranking-info {
    flex: 1;
}

.ranking-name {
    color: white;
    font-weight: bold;
    margin-bottom: 5px;
}

.ranking-details {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

.ranking-value {
    color: #667eea;
    font-weight: bold;
    font-size: 1.1rem;
}

.activity-heatmap {
    text-align: center;
}

.heatmap-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.heatmap-title {
    color: white;
    font-weight: bold;
}

.heatmap-legend {
    display: flex;
    align-items: center;
    gap: 10px;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.8rem;
}

.legend-scale {
    display: flex;
    gap: 2px;
}

.legend-item {
    width: 12px;
    height: 12px;
    border-radius: 2px;
}

.legend-item.level-1 { background: rgba(102, 126, 234, 0.2); }
.legend-item.level-2 { background: rgba(102, 126, 234, 0.4); }
.legend-item.level-3 { background: rgba(102, 126, 234, 0.6); }
.legend-item.level-4 { background: rgba(102, 126, 234, 0.8); }
.legend-item.level-5 { background: rgba(102, 126, 234, 1); }

.heatmap-grid {
    display: grid;
    grid-template-columns: repeat(24, 1fr);
    gap: 2px;
    max-width: 800px;
    margin: 0 auto;
}

.heatmap-cell {
    width: 20px;
    height: 20px;
    border-radius: 2px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.heatmap-cell:hover {
    transform: scale(1.2);
}

@media (max-width: 768px) {
    .analytics-filters {
        flex-direction: column;
    }
    
    .charts-container {
        grid-template-columns: 1fr;
    }
    
    .detailed-analytics {
        grid-template-columns: 1fr;
    }
    
    .stat-card {
        flex-direction: column;
        text-align: center;
    }
    
    .heatmap-grid {
        grid-template-columns: repeat(12, 1fr);
    }
    
    .heatmap-cell {
        width: 15px;
        height: 15px;
    }
}
</style>

<script>
// بيانات تجريبية
const analyticsData = {
    topMembers: [
        { name: 'ActiveUser1', messages: 1247, rank: 1 },
        { name: 'PowerUser2', messages: 956, rank: 2 },
        { name: 'ChatMaster3', messages: 834, rank: 3 },
        { name: 'SocialBee4', messages: 723, rank: 4 },
        { name: 'TalkativeUser5', messages: 612, rank: 5 }
    ],
    topChannels: [
        { name: 'العام', messages: 5678, rank: 1 },
        { name: 'الألعاب', messages: 3456, rank: 2 },
        { name: 'الموسيقى', messages: 2345, rank: 3 },
        { name: 'التقنية', messages: 1890, rank: 4 },
        { name: 'الترفيه', messages: 1234, rank: 5 }
    ],
    topCommands: [
        { name: '!help', uses: 456, rank: 1 },
        { name: '!ping', uses: 234, rank: 2 },
        { name: '!music', uses: 189, rank: 3 },
        { name: '!level', uses: 156, rank: 4 },
        { name: '!balance', uses: 123, rank: 5 }
    ]
};

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadAnalytics();
    generateActivityHeatmap();
});

// تحميل التحليلات
function loadAnalytics() {
    displayTopMembers();
    displayTopChannels();
    displayTopCommands();
    
    // محاكاة الرسوم البيانية
    setTimeout(() => {
        drawMembersChart();
        drawMessagesChart();
    }, 1000);
}

// عرض أكثر الأعضاء نشاطاً
function displayTopMembers() {
    const container = document.getElementById('top-members');
    container.innerHTML = analyticsData.topMembers.map(member => `
        <div class="ranking-item">
            <div class="ranking-number">${member.rank}</div>
            <div class="ranking-info">
                <div class="ranking-name">${member.name}</div>
                <div class="ranking-details">عضو نشط</div>
            </div>
            <div class="ranking-value">${member.messages.toLocaleString()}</div>
        </div>
    `).join('');
}

// عرض أكثر القنوات استخداماً
function displayTopChannels() {
    const container = document.getElementById('top-channels');
    container.innerHTML = analyticsData.topChannels.map(channel => `
        <div class="ranking-item">
            <div class="ranking-number">${channel.rank}</div>
            <div class="ranking-info">
                <div class="ranking-name">#${channel.name}</div>
                <div class="ranking-details">قناة نصية</div>
            </div>
            <div class="ranking-value">${channel.messages.toLocaleString()}</div>
        </div>
    `).join('');
}

// عرض أكثر الأوامر استخداماً
function displayTopCommands() {
    const container = document.getElementById('top-commands');
    container.innerHTML = analyticsData.topCommands.map(command => `
        <div class="ranking-item">
            <div class="ranking-number">${command.rank}</div>
            <div class="ranking-info">
                <div class="ranking-name">${command.name}</div>
                <div class="ranking-details">أمر البوت</div>
            </div>
            <div class="ranking-value">${command.uses.toLocaleString()}</div>
        </div>
    `).join('');
}

// رسم مخطط الأعضاء
function drawMembersChart() {
    const canvas = document.getElementById('membersChart');
    const ctx = canvas.getContext('2d');
    
    // محاكاة رسم بياني بسيط
    ctx.fillStyle = '#667eea';
    ctx.fillRect(50, 150, 300, 30);
    ctx.fillStyle = 'white';
    ctx.font = '14px Arial';
    ctx.fillText('نمو الأعضاء: +15% هذا الأسبوع', 50, 140);
}

// رسم مخطط الرسائل
function drawMessagesChart() {
    const canvas = document.getElementById('messagesChart');
    const ctx = canvas.getContext('2d');
    
    // محاكاة رسم بياني بسيط
    ctx.fillStyle = '#4ecdc4';
    ctx.fillRect(50, 150, 300, 30);
    ctx.fillStyle = 'white';
    ctx.font = '14px Arial';
    ctx.fillText('نشاط الرسائل: +23% هذا الأسبوع', 50, 140);
}

// إنشاء خريطة النشاط
function generateActivityHeatmap() {
    const container = document.getElementById('activity-heatmap');
    const days = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
    
    let html = '';
    
    // إنشاء 7 أيام × 24 ساعة
    for (let day = 0; day < 7; day++) {
        for (let hour = 0; hour < 24; hour++) {
            const activity = Math.floor(Math.random() * 5) + 1;
            const title = `${days[day]} - ${hour}:00`;
            html += `<div class="heatmap-cell legend-item level-${activity}" title="${title}"></div>`;
        }
    }
    
    container.innerHTML = html;
}

// تحديث التحليلات
function updateAnalytics() {
    const timeRange = document.getElementById('timeRange').value;
    const analyticsType = document.getElementById('analyticsType').value;
    
    showNotification(`تم تحديث التحليلات للفترة: ${timeRange}`, 'info');
    
    // إعادة تحميل البيانات
    loadAnalytics();
}

// تبديل الرسم البياني
function switchChart(chartType, period) {
    // إزالة الفئة النشطة من جميع الأزرار
    event.target.parentElement.querySelectorAll('.btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    // إضافة الفئة النشطة للزر المحدد
    event.target.classList.add('active');
    
    showNotification(`تم تبديل الرسم البياني إلى: ${period}`, 'info');
}

// تصدير التحليلات
function exportAnalytics() {
    const data = {
        تاريخ_التقرير: new Date().toLocaleDateString('ar-EG'),
        إجمالي_الأعضاء: '1,247',
        إجمالي_الرسائل: '45,678',
        ساعات_صوتية: '2,340',
        معدل_التفاعل: '87%',
        أكثر_الأعضاء_نشاطاً: analyticsData.topMembers.map(m => m.name).join(', '),
        أكثر_القنوات_استخداماً: analyticsData.topChannels.map(c => c.name).join(', '),
        أكثر_الأوامر_استخداماً: analyticsData.topCommands.map(c => c.name).join(', ')
    };
    
    const csv = Object.entries(data).map(([key, value]) => `${key},${value}`).join('\n');
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `analytics-report-${new Date().toISOString().split('T')[0]}.csv`;
    link.click();
    
    URL.revokeObjectURL(link.href);
    showNotification('تم تصدير تقرير التحليلات بنجاح!', 'success');
}

// إظهار الإشعارات
function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type} notification`;
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        animation: slideIn 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}
</script>

<%- include('../partials/footer') %>
