/* ملف CSS الرئيسي للوحة التحكم */

/* استيراد الخطوط */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

/* المتغيرات العامة */
:root {
    --primary-color: #7289da;
    --secondary-color: #99aab5;
    --success-color: #43b581;
    --warning-color: #faa61a;
    --danger-color: #f04747;
    --info-color: #00b0f4;
    --dark-color: #2c2f33;
    --darker-color: #23272a;
    --light-color: #ffffff;
    --muted-color: #72767d;
    --background-color: #36393f;
    --card-background: #40444b;
    --border-color: #2c2f33;
    --text-color: #ffffff;
    --text-muted: #b9bbbe;
    --sidebar-width: 280px;
    --header-height: 70px;
    --border-radius: 10px;
    --box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    --transition: all 0.3s ease;
}

/* إعادة تعيين الأساسيات */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Cairo', sans-serif;
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.6;
    overflow-x: hidden;
    direction: rtl;
}

/* تحسينات النصوص */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 0.5rem;
}

p {
    margin-bottom: 1rem;
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: #677bc4;
}

/* الأزرار */
.btn {
    border-radius: 8px;
    font-weight: 600;
    padding: 10px 20px;
    border: none;
    cursor: pointer;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.btn:focus {
    box-shadow: 0 0 0 0.2rem rgba(114, 137, 218, 0.25);
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
    border: 1px solid var(--primary-color);
}

.btn-primary:hover {
    background-color: #677bc4;
    border-color: #677bc4;
    color: white;
    transform: translateY(-1px);
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: white;
    border: 1px solid var(--secondary-color);
}

.btn-success {
    background-color: var(--success-color);
    color: white;
    border: 1px solid var(--success-color);
}

.btn-warning {
    background-color: var(--warning-color);
    color: white;
    border: 1px solid var(--warning-color);
}

.btn-danger {
    background-color: var(--danger-color);
    color: white;
    border: 1px solid var(--danger-color);
}

.btn-outline-primary {
    background-color: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    color: white;
}

.btn-outline-secondary {
    background-color: transparent;
    color: var(--text-muted);
    border: 1px solid var(--border-color);
}

.btn-outline-secondary:hover {
    background-color: var(--border-color);
    color: var(--text-color);
}

/* البطاقات */
.card {
    background-color: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 20px;
    overflow: hidden;
}

.card-header {
    background-color: var(--dark-color);
    border-bottom: 1px solid var(--border-color);
    padding: 20px;
}

.card-title {
    color: var(--text-color);
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.card-body {
    padding: 25px;
}

.card-footer {
    background-color: var(--dark-color);
    border-top: 1px solid var(--border-color);
    padding: 15px 20px;
}

/* النماذج */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    color: var(--text-muted);
    font-weight: 600;
    margin-bottom: 8px;
    display: block;
}

.form-control {
    background-color: var(--dark-color);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    border-radius: 8px;
    padding: 12px 15px;
    width: 100%;
    transition: var(--transition);
}

.form-control:focus {
    background-color: var(--dark-color);
    border-color: var(--primary-color);
    color: var(--text-color);
    box-shadow: 0 0 0 0.2rem rgba(114, 137, 218, 0.25);
    outline: none;
}

.form-control::placeholder {
    color: var(--muted-color);
}

.form-select {
    background-color: var(--dark-color);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    border-radius: 8px;
    padding: 12px 15px;
    width: 100%;
}

.form-check-input {
    background-color: var(--dark-color);
    border: 1px solid var(--border-color);
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.form-check-label {
    color: var(--text-color);
}

/* التنبيهات */
.alert {
    border: none;
    border-radius: 8px;
    padding: 15px 20px;
    margin-bottom: 20px;
    border-right: 4px solid;
}

.alert-success {
    background-color: rgba(67, 181, 129, 0.15);
    color: var(--success-color);
    border-right-color: var(--success-color);
}

.alert-danger {
    background-color: rgba(240, 71, 71, 0.15);
    color: var(--danger-color);
    border-right-color: var(--danger-color);
}

.alert-warning {
    background-color: rgba(250, 166, 26, 0.15);
    color: var(--warning-color);
    border-right-color: var(--warning-color);
}

.alert-info {
    background-color: rgba(0, 176, 244, 0.15);
    color: var(--info-color);
    border-right-color: var(--info-color);
}

/* الشارات */
.badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.badge.bg-primary {
    background-color: var(--primary-color) !important;
}

.badge.bg-success {
    background-color: var(--success-color) !important;
}

.badge.bg-warning {
    background-color: var(--warning-color) !important;
}

.badge.bg-danger {
    background-color: var(--danger-color) !important;
}

.badge.bg-info {
    background-color: var(--info-color) !important;
}

.badge.bg-secondary {
    background-color: var(--secondary-color) !important;
}

/* الجداول */
.table {
    color: var(--text-color);
    margin-bottom: 0;
}

.table th {
    background-color: var(--dark-color);
    border-color: var(--border-color);
    color: var(--text-muted);
    font-weight: 600;
    padding: 15px;
}

.table td {
    border-color: var(--border-color);
    padding: 15px;
    vertical-align: middle;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(255, 255, 255, 0.02);
}

.table-hover tbody tr:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

/* التصفح */
.pagination {
    justify-content: center;
}

.page-link {
    background-color: var(--card-background);
    border-color: var(--border-color);
    color: var(--text-color);
    padding: 10px 15px;
}

.page-link:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* المساعدات */
.text-muted {
    color: var(--text-muted) !important;
}

.text-primary {
    color: var(--primary-color) !important;
}

.text-success {
    color: var(--success-color) !important;
}

.text-warning {
    color: var(--warning-color) !important;
}

.text-danger {
    color: var(--danger-color) !important;
}

.text-info {
    color: var(--info-color) !important;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

.bg-success {
    background-color: var(--success-color) !important;
}

.bg-warning {
    background-color: var(--warning-color) !important;
}

.bg-danger {
    background-color: var(--danger-color) !important;
}

.bg-info {
    background-color: var(--info-color) !important;
}

/* تأثيرات الحركة */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
    from { opacity: 0; transform: translateX(30px); }
    to { opacity: 1; transform: translateX(0); }
}

/* شريط التمرير المخصص */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--dark-color);
}

::-webkit-scrollbar-thumb {
    background: var(--muted-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}

/* تحسينات الوصولية */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* حالات التحميل */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid transparent;
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
