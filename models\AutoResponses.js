const mongoose = require('mongoose');

const autoResponsesSchema = new mongoose.Schema({
  guildId: {
    type: String,
    required: true,
    index: true
  },

  // معرف فريد للرد
  responseId: {
    type: String,
    required: true,
    unique: true
  },

  // اسم الرد (للإدارة)
  name: {
    type: String,
    required: true
  },

  // وصف الرد
  description: String,

  // الكلمات أو العبارات المفعلة
  triggers: {
    keywords: [String], // الكلمات المفتاحية
    exactMatch: {
      type: Boolean,
      default: false // هل يجب أن تكون مطابقة تماماً
    },
    caseSensitive: {
      type: Boolean,
      default: false // هل يهم حجم الأحرف
    },
    wholeWord: {
      type: Boolean,
      default: false // هل يجب أن تكون كلمة كاملة
    },
    regex: String // تعبير نمطي متقدم
  },

  // نوع الرد
  responseType: {
    type: String,
    required: true,
    enum: ['text', 'embed', 'file', 'reaction', 'dm', 'role']
  },

  // محتوى الرد
  content: {
    // للنص العادي
    text: String,
    
    // للـ Embed
    embed: {
      title: String,
      description: String,
      color: {
        type: String,
        default: '#0099ff'
      },
      thumbnail: String,
      image: String,
      footer: {
        text: String,
        iconUrl: String
      },
      author: {
        name: String,
        iconUrl: String,
        url: String
      },
      fields: [{
        name: String,
        value: String,
        inline: {
          type: Boolean,
          default: false
        }
      }],
      timestamp: {
        type: Boolean,
        default: false
      }
    },

    // للملفات
    file: {
      url: String,
      filename: String,
      description: String
    },

    // للتفاعلات
    reactions: [String], // إيموجي أو معرفات الإيموجي المخصصة

    // للرسائل الخاصة
    dmMessage: String,

    // للرتب
    roleAction: {
      type: String,
      enum: ['add', 'remove', 'toggle']
    },
    roleId: String
  },

  // إعدادات التفعيل
  settings: {
    enabled: {
      type: Boolean,
      default: true
    },
    
    // القنوات المسموحة
    allowedChannels: [String],
    
    // القنوات المحظورة
    blockedChannels: [String],
    
    // الرتب المسموحة
    allowedRoles: [String],
    
    // الرتب المحظورة
    blockedRoles: [String],
    
    // المستخدمون المحظورون
    blockedUsers: [String],

    // فترة التهدئة (بالثواني)
    cooldown: {
      global: {
        type: Number,
        default: 0 // فترة تهدئة عامة
      },
      perUser: {
        type: Number,
        default: 5 // فترة تهدئة لكل مستخدم
      },
      perChannel: {
        type: Number,
        default: 0 // فترة تهدئة لكل قناة
      }
    },

    // حد أقصى للاستخدام
    usageLimit: {
      enabled: {
        type: Boolean,
        default: false
      },
      maxUses: Number,
      resetPeriod: {
        type: String,
        enum: ['hourly', 'daily', 'weekly', 'monthly'],
        default: 'daily'
      }
    },

    // شروط إضافية
    conditions: {
      minMessageLength: Number,
      maxMessageLength: Number,
      requireMention: {
        type: Boolean,
        default: false
      },
      onlyInThreads: {
        type: Boolean,
        default: false
      },
      onlyFromBots: {
        type: Boolean,
        default: false
      },
      excludeBots: {
        type: Boolean,
        default: true
      }
    },

    // إجراءات إضافية
    actions: {
      deleteOriginal: {
        type: Boolean,
        default: false
      },
      deleteAfter: Number, // حذف الرد بعد X ثانية
      pinResponse: {
        type: Boolean,
        default: false
      },
      createThread: {
        enabled: {
          type: Boolean,
          default: false
        },
        name: String,
        autoArchive: Number
      }
    }
  },

  // إحصائيات الاستخدام
  stats: {
    totalUses: {
      type: Number,
      default: 0
    },
    lastUsed: Date,
    usageHistory: [{
      userId: String,
      channelId: String,
      timestamp: {
        type: Date,
        default: Date.now
      }
    }]
  },

  // معلومات المنشئ
  createdBy: {
    id: String,
    username: String
  },

  // آخر تحديث
  lastModifiedBy: {
    id: String,
    username: String,
    modifiedAt: Date
  },

  // الأولوية (للترتيب)
  priority: {
    type: Number,
    default: 0
  },

  // العلامات للتصنيف
  tags: [String]
}, {
  timestamps: true,
  versionKey: false
});

// إنشاء فهارس للبحث السريع
autoResponsesSchema.index({ guildId: 1, responseId: 1 }, { unique: true });
autoResponsesSchema.index({ guildId: 1, 'settings.enabled': 1 });
autoResponsesSchema.index({ guildId: 1, 'triggers.keywords': 1 });
autoResponsesSchema.index({ guildId: 1, responseType: 1 });
autoResponsesSchema.index({ guildId: 1, priority: -1 });
autoResponsesSchema.index({ 'stats.totalUses': -1 });

// إنشاء معرف فريد للرد
autoResponsesSchema.pre('save', function(next) {
  if (this.isNew && !this.responseId) {
    this.responseId = `${this.guildId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  next();
});

// دالة للبحث عن الردود المطابقة
autoResponsesSchema.statics.findMatchingResponses = function(guildId, message, channelId, userId, userRoles) {
  const query = {
    guildId: guildId,
    'settings.enabled': true,
    $and: [
      // التحقق من القنوات المسموحة/المحظورة
      {
        $or: [
          { 'settings.allowedChannels': { $size: 0 } },
          { 'settings.allowedChannels': channelId }
        ]
      },
      { 'settings.blockedChannels': { $ne: channelId } },
      
      // التحقق من المستخدمين المحظورين
      { 'settings.blockedUsers': { $ne: userId } },
      
      // التحقق من الرتب
      {
        $or: [
          { 'settings.allowedRoles': { $size: 0 } },
          { 'settings.allowedRoles': { $in: userRoles } }
        ]
      },
      { 'settings.blockedRoles': { $not: { $in: userRoles } } }
    ]
  };

  return this.find(query).sort({ priority: -1 });
};

// دالة لتحديث إحصائيات الاستخدام
autoResponsesSchema.methods.incrementUsage = function(userId, channelId) {
  this.stats.totalUses += 1;
  this.stats.lastUsed = new Date();
  this.stats.usageHistory.push({
    userId: userId,
    channelId: channelId,
    timestamp: new Date()
  });

  // الاحتفاظ بآخر 100 استخدام فقط
  if (this.stats.usageHistory.length > 100) {
    this.stats.usageHistory = this.stats.usageHistory.slice(-100);
  }

  return this.save();
};

// دالة للحصول على إحصائيات الردود التلقائية
autoResponsesSchema.statics.getStats = function(guildId, days = 30) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  return this.aggregate([
    {
      $match: {
        guildId: guildId,
        'stats.lastUsed': { $gte: startDate }
      }
    },
    {
      $group: {
        _id: null,
        totalResponses: { $sum: 1 },
        totalUses: { $sum: '$stats.totalUses' },
        avgUsesPerResponse: { $avg: '$stats.totalUses' }
      }
    }
  ]);
};

module.exports = mongoose.model('AutoResponses', autoResponsesSchema);
