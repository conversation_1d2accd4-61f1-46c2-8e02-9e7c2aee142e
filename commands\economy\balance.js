// ========================================
// 💰 أمر عرض الرصيد - CS Discord
// ========================================
// 📺 يوتيوب: https://www.youtube.com/@CS_Discord
// 💬 ديسكورد: https://discord.gg/yqTn2EwVsd
// ========================================

const { EmbedBuilder } = require('discord.js');
const Currency = require('../../models/Currency');

module.exports = {
  name: 'balance',
  aliases: ['bal', 'money', 'رصيد', 'فلوس'],
  description: 'عرض رصيدك أو رصيد مستخدم آخر',
  usage: '[المستخدم]',
  category: 'economy',
  cooldown: 3,
  
  async execute(message, args) {
    try {
      // تحديد المستخدم المطلوب
      const targetUser = message.mentions.users.first() || message.author;
      const isOwnBalance = targetUser.id === message.author.id;
      
      // الحصول على بيانات العملة
      const userCurrency = await Currency.getOrCreate(targetUser.id, message.guild.id);
      
      // تنسيق الرصيد
      const formattedBalance = userCurrency.balance.toLocaleString('ar-EG');
      
      // إنشاء الـ embed
      const embed = new EmbedBuilder()
        .setColor('#00ff00')
        .setTitle(`💰 ${isOwnBalance ? 'رصيدك' : `رصيد ${targetUser.username}`}`)
        .setThumbnail(targetUser.displayAvatarURL({ dynamic: true }))
        .addFields(
          {
            name: '💵 الرصيد الحالي',
            value: `**${formattedBalance}** عملة`,
            inline: true
          },
          {
            name: '📊 الترتيب',
            value: `#${await getUserRank(targetUser.id, message.guild.id)}`,
            inline: true
          },
          {
            name: '📈 إجمالي الأرباح',
            value: `${userCurrency.work.totalEarned.toLocaleString('ar-EG')} عملة`,
            inline: true
          }
        )
        .setFooter({
          text: 'CS Discord Economy System',
          iconURL: message.client.user.displayAvatarURL()
        })
        .setTimestamp();
      
      // إضافة معلومات إضافية للرصيد الشخصي
      if (isOwnBalance) {
        embed.addFields(
          {
            name: '🎯 التتالي اليومي',
            value: `${userCurrency.dailyReward.streak} يوم`,
            inline: true
          },
          {
            name: '💼 مرات العمل',
            value: `${userCurrency.work.totalWorked} مرة`,
            inline: true
          },
          {
            name: '🎰 نسبة الفوز',
            value: `${calculateWinRate(userCurrency)}%`,
            inline: true
          }
        );
        
        // إضافة نصائح
        const tips = [
          '💡 استخدم `daily` للحصول على راتبك اليومي!',
          '💡 استخدم `work` للعمل وكسب المال!',
          '💡 استخدم `shop` لرؤية المتجر!',
          '💡 استخدم `gamble` للمقامرة (بحذر)!'
        ];
        
        embed.addFields({
          name: '💡 نصيحة',
          value: tips[Math.floor(Math.random() * tips.length)],
          inline: false
        });
      }
      
      await message.reply({ embeds: [embed] });
      
    } catch (error) {
      console.error('خطأ في أمر الرصيد:', error);
      await message.reply('❌ حدث خطأ أثناء عرض الرصيد!');
    }
  }
};

// دالة للحصول على ترتيب المستخدم
async function getUserRank(userId, guildId) {
  try {
    const users = await Currency.find({ guildId })
      .sort({ balance: -1 })
      .select('userId balance');
    
    const userIndex = users.findIndex(user => user.userId === userId);
    return userIndex !== -1 ? userIndex + 1 : 'غير محدد';
  } catch (error) {
    return 'غير محدد';
  }
}

// دالة لحساب نسبة الفوز
function calculateWinRate(userCurrency) {
  const totalGames = userCurrency.gambling.totalBet;
  if (totalGames === 0) return 0;
  
  const wins = userCurrency.gambling.totalWon;
  const losses = userCurrency.gambling.totalLost;
  const totalPlayed = wins + losses;
  
  if (totalPlayed === 0) return 0;
  
  return Math.round((wins / totalPlayed) * 100);
}
