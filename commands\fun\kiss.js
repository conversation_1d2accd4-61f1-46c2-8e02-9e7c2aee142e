// ========================================
// 💋 أمر البوسة - CS Discord
// ========================================
// 📺 يوتيوب: https://www.youtube.com/@CS_Discord
// 💬 ديسكورد: https://discord.gg/yqTn2EwVsd
// ========================================

const { EmbedBuilder } = require('discord.js');

module.exports = {
  name: 'kiss',
  aliases: ['بوسة', 'قبلة', 'بوس'],
  description: 'بوس عضو (صورة متحركة)',
  usage: '<@المستخدم>',
  category: 'fun',
  cooldown: 3,
  
  async execute(message, args) {
    try {
      // التحقق من وجود المستخدم المذكور
      const targetUser = message.mentions.users.first();
      
      if (!targetUser) {
        return await message.reply('❌ يجب ذكر المستخدم الذي تريد بوسه!\nمثال: `kiss @أحمد`');
      }
      
      // التحقق من عدم بوس النفس
      if (targetUser.id === message.author.id) {
        return await message.reply('😅 لا يمكنك بوس نفسك! جرب بوس شخص آخر!');
      }
      
      // التحقق من عدم بوس البوت
      if (targetUser.bot) {
        return await message.reply('🤖 البوتات لا تحب البوس! جرب مع إنسان حقيقي!');
      }
      
      // صور البوس المتحركة
      const kissGifs = [
        'https://media.giphy.com/media/G3va31oEEnIkM/giphy.gif',
        'https://media.giphy.com/media/bm2O3nXTcKJeU/giphy.gif',
        'https://media.giphy.com/media/KH1CTZtw1iP3W/giphy.gif',
        'https://media.giphy.com/media/nyNS6Cfrnkdj2/giphy.gif',
        'https://media.giphy.com/media/zkppEMFvRX5FC/giphy.gif',
        'https://media.giphy.com/media/FqBTvSNjNzeZG/giphy.gif',
        'https://media.giphy.com/media/3o6fJ1BM7R2EBRDnxK/giphy.gif',
        'https://media.giphy.com/media/11rWoZKkRJHjfW/giphy.gif',
        'https://media.giphy.com/media/ZOln4JxCoZay4/giphy.gif',
        'https://media.giphy.com/media/3o6fJeJGYpJWvzgbQs/giphy.gif'
      ];
      
      // اختيار صورة عشوائية
      const randomGif = kissGifs[Math.floor(Math.random() * kissGifs.length)];
      
      // رسائل البوس المختلفة
      const kissMessages = [
        `💋 **${message.author.username}** بوس **${targetUser.username}** بوسة حلوة!`,
        `😘 **${message.author.username}** أعطى **${targetUser.username}** قبلة رومانسية!`,
        `💕 **${message.author.username}** بوس **${targetUser.username}** بحب!`,
        `🥰 **${message.author.username}** قبّل **${targetUser.username}** بحنان!`,
        `💖 بوسة جميلة من **${message.author.username}** إلى **${targetUser.username}**!`,
        `😍 **${message.author.username}** لا يستطيع مقاومة بوس **${targetUser.username}**!`,
        `💋 قبلة سحرية من **${message.author.username}** لـ **${targetUser.username}**!`,
        `🌹 **${message.author.username}** بوس **${targetUser.username}** مثل الأفلام!`
      ];
      
      // اختيار رسالة عشوائية
      const randomMessage = kissMessages[Math.floor(Math.random() * kissMessages.length)];
      
      // إنشاء الـ embed
      const embed = new EmbedBuilder()
        .setColor('#ff69b4')
        .setTitle('💋 بوسة حلوة!')
        .setDescription(randomMessage)
        .setImage(randomGif)
        .setFooter({
          text: 'CS Discord Fun Commands',
          iconURL: message.client.user.displayAvatarURL()
        })
        .setTimestamp();
      
      // إضافة حقول إضافية أحياناً
      if (Math.random() < 0.3) {
        const funFacts = [
          '💡 هل تعلم؟ البوسة تحرق 6 سعرات حرارية!',
          '💡 البوسة تقوي جهاز المناعة!',
          '💡 البوسة تقلل من التوتر والضغط!',
          '💡 البوسة تزيد من هرمون السعادة!',
          '💡 البوسة تحسن من الحالة المزاجية!'
        ];
        
        embed.addFields({
          name: '💡 معلومة طريفة',
          value: funFacts[Math.floor(Math.random() * funFacts.length)],
          inline: false
        });
      }
      
      await message.reply({ embeds: [embed] });
      
      // إضافة ردود فعل عشوائية
      const reactions = ['💋', '😘', '💕', '❤️', '💖'];
      const randomReaction = reactions[Math.floor(Math.random() * reactions.length)];
      
      try {
        await message.react(randomReaction);
      } catch (error) {
        // تجاهل خطأ الردود إذا لم تكن متاحة
      }
      
    } catch (error) {
      console.error('خطأ في أمر البوسة:', error);
      await message.reply('❌ حدث خطأ أثناء إرسال البوسة!');
    }
  }
};
