// ========================================
// 💼 أمر العمل - CS Discord
// ========================================
// 📺 يوتيوب: https://www.youtube.com/@CS_Discord
// 💬 ديسكورد: https://discord.gg/yqTn2EwVsd
// ========================================

const { EmbedBuilder } = require('discord.js');
const Currency = require('../../models/Currency');

module.exports = {
  name: 'work',
  aliases: ['عمل', 'شغل', 'وظيفة'],
  description: 'اعمل لكسب المال',
  usage: '',
  category: 'economy',
  cooldown: 5,
  
  async execute(message, args) {
    try {
      // الحصول على بيانات المستخدم
      const userCurrency = await Currency.getOrCreate(message.author.id, message.guild.id);
      
      // فحص إمكانية العمل
      if (!userCurrency.canWork()) {
        const nextWork = getNextWorkTime(userCurrency.work.lastWorked);
        
        const embed = new EmbedBuilder()
          .setColor('#ff6b6b')
          .setTitle('😴 أنت متعب!')
          .setDescription(`يجب أن تستريح قبل العمل مرة أخرى.\nيمكنك العمل خلال: **${nextWork}**`)
          .addFields(
            {
              name: '📊 إحصائيات العمل',
              value: `💼 مرات العمل: **${userCurrency.work.totalWorked}**\n💰 إجمالي الأرباح: **${userCurrency.work.totalEarned.toLocaleString('ar-EG')}** عملة`,
              inline: false
            }
          )
          .setFooter({
            text: 'CS Discord Economy System',
            iconURL: message.client.user.displayAvatarURL()
          })
          .setTimestamp();
        
        return await message.reply({ embeds: [embed] });
      }
      
      // العمل والحصول على النتيجة
      const workResult = userCurrency.doWork();
      await userCurrency.save();
      
      // تحديد الإيموجي والرسالة بناءً على نوع العمل
      const workEmojis = {
        'برمجة': '💻',
        'تصميم': '🎨',
        'كتابة': '✍️',
        'تدريس': '👨‍🏫',
        'ترجمة': '🌐'
      };
      
      const workMessages = {
        'برمجة': [
          'كتبت كود رائع وحصلت على مكافأة!',
          'أصلحت bug معقد وأعجب بك العميل!',
          'طورت تطبيق جديد وحققت نجاحاً باهراً!',
          'برمجت موقع ويب احترافي!'
        ],
        'تصميم': [
          'صممت لوجو مذهل للعميل!',
          'أنشأت تصميم إعلان جذاب!',
          'صممت واجهة مستخدم رائعة!',
          'أبدعت في تصميم بوستر فني!'
        ],
        'كتابة': [
          'كتبت مقال رائع وحاز على إعجاب الجميع!',
          'ألفت قصة قصيرة مؤثرة!',
          'كتبت محتوى تسويقي ناجح!',
          'أنجزت ترجمة مقال مهم!'
        ],
        'تدريس': [
          'درّست طلاب وساعدتهم على النجاح!',
          'شرحت درس صعب بطريقة مبسطة!',
          'ساعدت طالب في حل واجباته!',
          'أعطيت دورة تدريبية مفيدة!'
        ],
        'ترجمة': [
          'ترجمت نص مهم بدقة عالية!',
          'ساعدت في ترجمة مؤتمر دولي!',
          'ترجمت كتاب إلى العربية!',
          'أنجزت ترجمة فورية ممتازة!'
        ]
      };
      
      const emoji = workEmojis[workResult.job] || '💼';
      const messages = workMessages[workResult.job] || [workResult.description];
      const randomMessage = messages[Math.floor(Math.random() * messages.length)];
      
      // تحديد لون الـ embed بناءً على المكافأة
      let embedColor = '#00ff00';
      if (workResult.reward >= 120) embedColor = '#ffd700'; // ذهبي للمكافآت العالية
      else if (workResult.reward >= 80) embedColor = '#ff69b4'; // وردي للمكافآت المتوسطة
      
      // إنشاء الـ embed
      const embed = new EmbedBuilder()
        .setColor(embedColor)
        .setTitle(`${emoji} نجحت في العمل!`)
        .setDescription(randomMessage)
        .setThumbnail(message.author.displayAvatarURL({ dynamic: true }))
        .addFields(
          {
            name: '💼 نوع العمل',
            value: `**${workResult.job}**`,
            inline: true
          },
          {
            name: '💰 المكافأة',
            value: `**+${workResult.reward.toLocaleString('ar-EG')}** عملة`,
            inline: true
          },
          {
            name: '💵 رصيدك الجديد',
            value: `**${userCurrency.balance.toLocaleString('ar-EG')}** عملة`,
            inline: true
          },
          {
            name: '📊 إحصائيات العمل',
            value: `💼 مرات العمل: **${userCurrency.work.totalWorked}**\n💰 إجمالي الأرباح: **${userCurrency.work.totalEarned.toLocaleString('ar-EG')}** عملة`,
            inline: false
          }
        )
        .setFooter({
          text: 'CS Discord Economy System',
          iconURL: message.client.user.displayAvatarURL()
        })
        .setTimestamp();
      
      // إضافة نصائح عشوائية
      const tips = [
        '💡 استخدم `daily` للحصول على راتبك اليومي!',
        '💡 استخدم `balance` لرؤية رصيدك!',
        '💡 استخدم `shop` لرؤية المتجر!',
        '💡 كلما عملت أكثر، كلما زادت خبرتك!',
        '💡 العمل المنتظم يحقق أرباح أكثر!'
      ];
      
      embed.addFields({
        name: '💡 نصيحة',
        value: tips[Math.floor(Math.random() * tips.length)],
        inline: false
      });
      
      // إضافة إنجازات العمل
      const achievements = checkWorkAchievements(userCurrency.work.totalWorked, userCurrency.work.totalEarned);
      if (achievements.length > 0) {
        embed.addFields({
          name: '🏅 إنجازات جديدة!',
          value: achievements.join('\n'),
          inline: false
        });
      }
      
      await message.reply({ embeds: [embed] });
      
    } catch (error) {
      console.error('خطأ في أمر العمل:', error);
      await message.reply('❌ حدث خطأ أثناء العمل!');
    }
  }
};

// دالة لحساب الوقت المتبقي للعمل التالي
function getNextWorkTime(lastWorked) {
  const now = new Date();
  const nextWork = new Date(lastWorked);
  nextWork.setHours(nextWork.getHours() + 1);
  
  const diff = nextWork - now;
  const minutes = Math.floor(diff / (1000 * 60));
  const seconds = Math.floor((diff % (1000 * 60)) / 1000);
  
  if (minutes > 0) {
    return `${minutes} دقيقة و ${seconds} ثانية`;
  } else {
    return `${seconds} ثانية`;
  }
}

// دالة للتحقق من إنجازات العمل
function checkWorkAchievements(totalWorked, totalEarned) {
  const achievements = [];
  
  if (totalWorked === 10) {
    achievements.push('🎯 **العامل المجتهد** - 10 مرات عمل!');
  }
  
  if (totalWorked === 50) {
    achievements.push('💪 **العامل المحترف** - 50 مرة عمل!');
  }
  
  if (totalWorked === 100) {
    achievements.push('🏆 **سيد المهنة** - 100 مرة عمل!');
  }
  
  if (totalEarned >= 10000) {
    achievements.push('💎 **الثري** - ربحت 10,000 عملة من العمل!');
  }
  
  if (totalEarned >= 50000) {
    achievements.push('👑 **المليونير** - ربحت 50,000 عملة من العمل!');
  }
  
  return achievements;
}
