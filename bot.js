// ========================================
// 🤖 البوت الرئيسي - CS Discord
// ========================================
// 📺 يوتيوب: https://www.youtube.com/@CS_Discord
// 💬 ديسكورد: https://discord.gg/yqTn2EwVsd
// ========================================

const { Client, GatewayIntentBits, Collection, ActivityType } = require('discord.js');
const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');
const config = require('./config');

console.log('🚀 بدء تشغيل البوت...');
console.log('📺 يوتيوب: https://www.youtube.com/@CS_Discord');
console.log('💬 ديسكورد: https://discord.gg/yqTn2EwVsd');

// إنشاء البوت
const client = new Client({
  intents: [
    GatewayIntentBits.Guilds,
    GatewayIntentBits.GuildMessages,
    GatewayIntentBits.MessageContent,
    GatewayIntentBits.GuildMembers,
    GatewayIntentBits.GuildVoiceStates,
    GatewayIntentBits.GuildMessageReactions
  ]
});

// مجموعة الأوامر
client.commands = new Collection();

// تحميل الأوامر
function loadCommands() {
  const commandsPath = path.join(__dirname, 'commands');
  const commandFolders = fs.readdirSync(commandsPath);

  for (const folder of commandFolders) {
    const folderPath = path.join(commandsPath, folder);
    if (!fs.statSync(folderPath).isDirectory()) continue;

    const commandFiles = fs.readdirSync(folderPath).filter(file => file.endsWith('.js'));

    for (const file of commandFiles) {
      const filePath = path.join(folderPath, file);
      try {
        const command = require(filePath);
        if (command.name) {
          client.commands.set(command.name, command);
          console.log(`✅ تم تحميل الأمر: ${command.name}`);
        }
      } catch (error) {
        console.error(`❌ خطأ في تحميل الأمر ${file}:`, error);
      }
    }
  }
}

// اتصال قاعدة البيانات
async function connectDatabase() {
  try {
    console.log('🔗 محاولة الاتصال بقاعدة البيانات...');
    await mongoose.connect(config.database.mongoUri, config.database.options);
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');
  } catch (error) {
    console.error('❌ خطأ في الاتصال بقاعدة البيانات:', error.message);
    console.log('⚠️ سيتم المتابعة بدون قاعدة البيانات...');
    // لا نوقف البوت إذا فشل الاتصال بقاعدة البيانات
  }
}

// عند تشغيل البوت
client.once('ready', async () => {
  console.log(`🤖 البوت ${client.user.tag} جاهز!`);
  console.log(`📊 متصل بـ ${client.guilds.cache.size} خادم`);
  console.log(`👥 يخدم ${client.guilds.cache.reduce((acc, guild) => acc + guild.memberCount, 0)} مستخدم`);

  // تحديث النشاط
  client.user.setActivity('CS Discord Dashboard', { type: ActivityType.Watching });

  // بدء الخادم مع البوت
  startWebServer();

  console.log('🔗 تم ربط البوت بلوحة التحكم');
});

// دالة بدء الخادم
function startWebServer() {
  const express = require('express');
  const mongoose = require('mongoose');
  const session = require('express-session');
  const MongoStore = require('connect-mongo');
  const passport = require('passport');
  const path = require('path');
  const helmet = require('helmet');
  const compression = require('compression');
  const rateLimit = require('express-rate-limit');
  const morgan = require('morgan');
  const cors = require('cors');

  // استيراد المسارات
  const authRoutes = require('./routes/auth');
  const dashboardRoutes = require('./routes/dashboard');
  const apiRoutes = require('./routes/api');

  // استيراد حماية CS
  const {
    csProtectionMiddleware,
    antiTamperingMiddleware,
    injectCreditsMiddleware,
    startPeriodicCheck
  } = require('./middleware/csProtection');

  // إنشاء تطبيق Express
  const app = express();

  // تمرير البوت للتطبيق
  app.set('client', client);

  // إعداد محرك العرض
  app.set('view engine', 'ejs');
  app.set('views', path.join(__dirname, 'views'));

  // إعداد الملفات الثابتة
  app.use('/static', express.static(path.join(__dirname, 'public')));
  app.use('/assets', express.static(path.join(__dirname, 'assets')));

  // إعداد الأمان
  app.use(helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'", "https://cdnjs.cloudflare.com", "https://fonts.googleapis.com"],
        scriptSrc: ["'self'", "'unsafe-inline'", "https://cdnjs.cloudflare.com"],
        fontSrc: ["'self'", "https://fonts.gstatic.com", "https://cdnjs.cloudflare.com"],
        imgSrc: ["'self'", "data:", "https:", "http:"],
        connectSrc: ["'self'", "https://discord.com", "https://discordapp.com"]
      }
    }
  }));

  app.use(compression());
  app.use(cors());
  app.use(morgan('combined'));

  // إعداد معدل الطلبات
  const limiter = rateLimit({
    windowMs: 15 * 60 * 1000,
    max: 100,
    message: 'تم تجاوز الحد المسموح من الطلبات'
  });
  app.use(limiter);

  // إعداد معالجة البيانات
  app.use(express.json({ limit: '10mb' }));
  app.use(express.urlencoded({ extended: true, limit: '10mb' }));

  // إعداد الجلسات
  app.use(session({
    secret: config.session.secret,
    resave: false,
    saveUninitialized: false,
    store: MongoStore.create({
      mongoUrl: config.database.uri
    }),
    cookie: {
      secure: false,
      httpOnly: true,
      maxAge: 24 * 60 * 60 * 1000
    }
  }));

  // إعداد Passport
  require('./auth/passport-config')(passport);
  app.use(passport.initialize());
  app.use(passport.session());

  // حماية CS Discord
  app.use(csProtectionMiddleware);
  app.use(antiTamperingMiddleware);
  app.use(injectCreditsMiddleware);

  // المسارات
  app.use('/auth', authRoutes);
  app.use('/dashboard', dashboardRoutes);
  app.use('/api', apiRoutes);

  // الصفحة الرئيسية
  app.get('/', (req, res) => {
    res.render('index', {
      title: 'CS Discord Bot Dashboard',
      user: req.user
    });
  });

  // معالجة الأخطاء 404
  app.use((req, res) => {
    res.status(404).render('errors/404', {
      title: 'الصفحة غير موجودة'
    });
  });

  // معالجة الأخطاء العامة
  app.use((error, req, res, next) => {
    console.error('خطأ في الخادم:', error);
    res.status(error.status || 500).render('errors/500', {
      title: 'خطأ في الخادم',
      error: {}
    });
  });

  // بدء الخادم
  const PORT = config.server.port;
  const HOST = config.server.host;

  app.listen(PORT, HOST, () => {
    console.log(`🚀 الخادم يعمل على ${config.server.baseUrl}`);
    console.log(`🛡️ حماية CS Discord: مفعلة`);
    console.log(`📺 يوتيوب: https://www.youtube.com/@CS_Discord`);
    console.log(`💬 ديسكورد: https://discord.gg/yqTn2EwVsd`);

    // بدء الفحص الدوري لحماية CS
    startPeriodicCheck();
  });
}

// عند انضمام البوت لخادم جديد
client.on('guildCreate', guild => {
  console.log(`📈 انضم البوت لخادم جديد: ${guild.name} (${guild.memberCount} عضو)`);
});

// عند مغادرة البوت لخادم
client.on('guildDelete', guild => {
  console.log(`📉 غادر البوت الخادم: ${guild.name}`);
});

// معالجة الرسائل
client.on('messageCreate', async message => {
  if (message.author.bot) return;
  if (!message.guild) return;

  const prefix = config.bot.prefix;
  if (!message.content.startsWith(prefix)) return;

  const args = message.content.slice(prefix.length).trim().split(/ +/);
  const commandName = args.shift().toLowerCase();

  const command = client.commands.get(commandName) || 
                 client.commands.find(cmd => cmd.aliases && cmd.aliases.includes(commandName));

  if (!command) return;

  // التحقق من الصلاحيات
  if (command.ownerOnly && message.author.id !== config.bot.ownerId) {
    return message.reply('❌ هذا الأمر متاح للمالك فقط!');
  }

  if (command.permissions) {
    if (!message.member.permissions.has(command.permissions)) {
      return message.reply('❌ ليس لديك الصلاحيات المطلوبة!');
    }
  }

  // تنفيذ الأمر
  try {
    await command.execute(message, args, client);
  } catch (error) {
    console.error(`❌ خطأ في تنفيذ الأمر ${commandName}:`, error);
    message.reply('❌ حدث خطأ أثناء تنفيذ الأمر!');
  }
});

// معالجة الأخطاء
client.on('error', error => {
  console.error('❌ خطأ في البوت:', error);
});

client.on('warn', warning => {
  console.warn('⚠️ تحذير:', warning);
});

// إيقاف البوت بشكل صحيح
process.on('SIGINT', () => {
  console.log('🛑 إيقاف البوت...');
  client.destroy();
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('🛑 إيقاف البوت...');
  client.destroy();
  process.exit(0);
});

// بدء البوت
async function startBot() {
  try {
    // اتصال قاعدة البيانات
    await connectDatabase();
    
    // تحميل الأوامر
    loadCommands();
    
    // تسجيل الدخول
    await client.login(config.bot.token);
    
  } catch (error) {
    console.error('❌ خطأ في تشغيل البوت:', error);
    process.exit(1);
  }
}

// تشغيل البوت
startBot();

module.exports = client;
