// ========================================
// 🤖 البوت الرئيسي - CS Discord
// ========================================
// 📺 يوتيوب: https://www.youtube.com/@CS_Discord
// 💬 ديسكورد: https://discord.gg/yqTn2EwVsd
// ========================================

const { Client, GatewayIntentBits, Collection, ActivityType } = require('discord.js');
const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');
const config = require('./config');

// إنشاء البوت
const client = new Client({
  intents: [
    GatewayIntentBits.Guilds,
    GatewayIntentBits.GuildMessages,
    GatewayIntentBits.MessageContent,
    GatewayIntentBits.GuildMembers,
    GatewayIntentBits.GuildVoiceStates,
    GatewayIntentBits.GuildMessageReactions
  ]
});

// مجموعة الأوامر
client.commands = new Collection();

// تحميل الأوامر
function loadCommands() {
  const commandsPath = path.join(__dirname, 'commands');
  const commandFolders = fs.readdirSync(commandsPath);

  for (const folder of commandFolders) {
    const folderPath = path.join(commandsPath, folder);
    if (!fs.statSync(folderPath).isDirectory()) continue;

    const commandFiles = fs.readdirSync(folderPath).filter(file => file.endsWith('.js'));

    for (const file of commandFiles) {
      const filePath = path.join(folderPath, file);
      try {
        const command = require(filePath);
        if (command.name) {
          client.commands.set(command.name, command);
          console.log(`✅ تم تحميل الأمر: ${command.name}`);
        }
      } catch (error) {
        console.error(`❌ خطأ في تحميل الأمر ${file}:`, error);
      }
    }
  }
}

// اتصال قاعدة البيانات
async function connectDatabase() {
  try {
    await mongoose.connect(config.database.uri, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    console.log('✅ تم الاتصال بقاعدة البيانات');
  } catch (error) {
    console.error('❌ خطأ في الاتصال بقاعدة البيانات:', error);
    process.exit(1);
  }
}

// عند تشغيل البوت
client.once('ready', async () => {
  console.log(`🤖 البوت ${client.user.tag} جاهز!`);
  console.log(`📊 متصل بـ ${client.guilds.cache.size} خادم`);
  console.log(`👥 يخدم ${client.guilds.cache.reduce((acc, guild) => acc + guild.memberCount, 0)} مستخدم`);
  
  // تحديث النشاط
  client.user.setActivity('CS Discord Dashboard', { type: ActivityType.Watching });
  
  // تمرير البوت للتطبيق
  const app = require('./index');
  app.set('client', client);
  
  console.log('🔗 تم ربط البوت بلوحة التحكم');
});

// عند انضمام البوت لخادم جديد
client.on('guildCreate', guild => {
  console.log(`📈 انضم البوت لخادم جديد: ${guild.name} (${guild.memberCount} عضو)`);
});

// عند مغادرة البوت لخادم
client.on('guildDelete', guild => {
  console.log(`📉 غادر البوت الخادم: ${guild.name}`);
});

// معالجة الرسائل
client.on('messageCreate', async message => {
  if (message.author.bot) return;
  if (!message.guild) return;

  const prefix = config.bot.prefix;
  if (!message.content.startsWith(prefix)) return;

  const args = message.content.slice(prefix.length).trim().split(/ +/);
  const commandName = args.shift().toLowerCase();

  const command = client.commands.get(commandName) || 
                 client.commands.find(cmd => cmd.aliases && cmd.aliases.includes(commandName));

  if (!command) return;

  // التحقق من الصلاحيات
  if (command.ownerOnly && message.author.id !== config.bot.ownerId) {
    return message.reply('❌ هذا الأمر متاح للمالك فقط!');
  }

  if (command.permissions) {
    if (!message.member.permissions.has(command.permissions)) {
      return message.reply('❌ ليس لديك الصلاحيات المطلوبة!');
    }
  }

  // تنفيذ الأمر
  try {
    await command.execute(message, args, client);
  } catch (error) {
    console.error(`❌ خطأ في تنفيذ الأمر ${commandName}:`, error);
    message.reply('❌ حدث خطأ أثناء تنفيذ الأمر!');
  }
});

// معالجة الأخطاء
client.on('error', error => {
  console.error('❌ خطأ في البوت:', error);
});

client.on('warn', warning => {
  console.warn('⚠️ تحذير:', warning);
});

// إيقاف البوت بشكل صحيح
process.on('SIGINT', () => {
  console.log('🛑 إيقاف البوت...');
  client.destroy();
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('🛑 إيقاف البوت...');
  client.destroy();
  process.exit(0);
});

// بدء البوت
async function startBot() {
  try {
    // اتصال قاعدة البيانات
    await connectDatabase();
    
    // تحميل الأوامر
    loadCommands();
    
    // تسجيل الدخول
    await client.login(config.bot.token);
    
  } catch (error) {
    console.error('❌ خطأ في تشغيل البوت:', error);
    process.exit(1);
  }
}

// تشغيل البوت
startBot();

module.exports = client;
