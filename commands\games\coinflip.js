// ========================================
// 🪙 لعبة قلب العملة - CS Discord
// ========================================
// 📺 يوتيوب: https://www.youtube.com/@CS_Discord
// 💬 ديسكورد: https://discord.gg/yqTn2EwVsd
// ========================================

const { EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');

module.exports = {
  name: 'coinflip',
  aliases: ['coin', 'flip', 'عملة', 'قلب_عملة'],
  description: 'قلب عملة واختر وجه أو كتابة',
  usage: '[heads/tails] أو [وجه/كتابة]',
  category: 'games',
  cooldown: 3,
  
  async execute(message, args) {
    try {
      // إذا لم يتم تحديد اختيار، عرض الأزرار
      if (!args[0]) {
        return await showCoinflipButtons(message);
      }
      
      // تحديد اختيار اللاعب
      const playerChoice = parseChoice(args[0].toLowerCase());
      
      if (!playerChoice) {
        return await message.reply('❌ اختيار غير صحيح! استخدم: `heads`, `tails` أو `وجه`, `كتابة`');
      }
      
      // قلب العملة
      const result = flipCoin();
      
      // تحديد النتيجة
      const won = playerChoice === result;
      
      // إرسال النتيجة
      await sendResult(message, playerChoice, result, won);
      
    } catch (error) {
      console.error('خطأ في لعبة قلب العملة:', error);
      await message.reply('❌ حدث خطأ أثناء قلب العملة!');
    }
  }
};

// عرض أزرار اللعبة
async function showCoinflipButtons(message) {
  const embed = new EmbedBuilder()
    .setColor('#ffd700')
    .setTitle('🪙 لعبة قلب العملة')
    .setDescription('اختر وجه أو كتابة، ثم سأقلب العملة!')
    .addFields(
      {
        name: '👑 وجه (Heads)',
        value: 'الجانب الذي يحتوي على الصورة',
        inline: true
      },
      {
        name: '📜 كتابة (Tails)',
        value: 'الجانب الذي يحتوي على النص',
        inline: true
      }
    )
    .setImage('https://media.giphy.com/media/DfSXiR60W9MVq/giphy.gif') // صورة عملة تدور
    .setFooter({
      text: 'CS Discord Games',
      iconURL: message.client.user.displayAvatarURL()
    })
    .setTimestamp();
  
  const row = new ActionRowBuilder()
    .addComponents(
      new ButtonBuilder()
        .setCustomId('coin_heads')
        .setLabel('👑 وجه')
        .setStyle(ButtonStyle.Primary),
      new ButtonBuilder()
        .setCustomId('coin_tails')
        .setLabel('📜 كتابة')
        .setStyle(ButtonStyle.Secondary)
    );
  
  const gameMessage = await message.reply({ embeds: [embed], components: [row] });
  
  // انتظار التفاعل
  const filter = (interaction) => {
    return interaction.user.id === message.author.id && 
           interaction.customId.startsWith('coin_');
  };
  
  try {
    const interaction = await gameMessage.awaitMessageComponent({ 
      filter, 
      time: 30000 
    });
    
    const playerChoice = interaction.customId.split('_')[1];
    
    // إظهار العملة تدور
    await showFlippingAnimation(interaction, playerChoice);
    
  } catch (error) {
    // انتهت المهلة
    const timeoutEmbed = new EmbedBuilder()
      .setColor('#ffa500')
      .setTitle('⏰ انتهت المهلة')
      .setDescription('انتهت مهلة اللعبة!')
      .setTimestamp();
    
    await gameMessage.edit({ embeds: [timeoutEmbed], components: [] });
  }
}

// عرض انيميشن قلب العملة
async function showFlippingAnimation(interaction, playerChoice) {
  const flippingEmbed = new EmbedBuilder()
    .setColor('#ffa500')
    .setTitle('🪙 العملة تدور...')
    .setDescription('انتظر... العملة في الهواء! 🌪️')
    .setImage('https://media.giphy.com/media/DfSXiR60W9MVq/giphy.gif')
    .setTimestamp();
  
  await interaction.update({ embeds: [flippingEmbed], components: [] });
  
  // انتظار للتشويق
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  // قلب العملة والحصول على النتيجة
  const result = flipCoin();
  const won = playerChoice === result;
  
  // إرسال النتيجة النهائية
  await sendResultInteraction(interaction, playerChoice, result, won);
}

// تحليل اختيار اللاعب
function parseChoice(choice) {
  const choices = {
    'heads': 'heads',
    'وجه': 'heads',
    'h': 'heads',
    'tails': 'tails',
    'كتابة': 'tails',
    't': 'tails'
  };
  
  return choices[choice] || null;
}

// قلب العملة
function flipCoin() {
  return Math.random() < 0.5 ? 'heads' : 'tails';
}

// إرسال النتيجة (للأمر النصي)
async function sendResult(message, playerChoice, result, won) {
  const embed = createResultEmbed(message.author, playerChoice, result, won);
  await message.reply({ embeds: [embed] });
}

// إرسال النتيجة (للتفاعل)
async function sendResultInteraction(interaction, playerChoice, result, won) {
  const embed = createResultEmbed(interaction.user, playerChoice, result, won);
  await interaction.editReply({ embeds: [embed], components: [] });
}

// إنشاء embed النتيجة
function createResultEmbed(user, playerChoice, result, won) {
  const choiceEmojis = {
    'heads': '👑',
    'tails': '📜'
  };
  
  const choiceNames = {
    'heads': 'وجه',
    'tails': 'كتابة'
  };
  
  let color, title, description, resultEmoji;
  
  if (won) {
    color = '#00ff00';
    title = '🎉 فزت!';
    description = 'تهانينا! توقعت النتيجة بشكل صحيح!';
    resultEmoji = '🏆';
  } else {
    color = '#ff0000';
    title = '😢 خسرت!';
    description = 'للأسف! لم تتوقع النتيجة الصحيحة. حاول مرة أخرى!';
    resultEmoji = '💔';
  }
  
  const embed = new EmbedBuilder()
    .setColor(color)
    .setTitle(`${resultEmoji} ${title}`)
    .setDescription(description)
    .addFields(
      {
        name: '🎯 اختيارك',
        value: `${choiceEmojis[playerChoice]} ${choiceNames[playerChoice]}`,
        inline: true
      },
      {
        name: '🪙 نتيجة العملة',
        value: `${choiceEmojis[result]} ${choiceNames[result]}`,
        inline: true
      },
      {
        name: '📊 النتيجة',
        value: won ? '✅ صحيح!' : '❌ خطأ!',
        inline: true
      }
    )
    .setFooter({
      text: 'CS Discord Games',
      iconURL: user.client.user.displayAvatarURL()
    })
    .setTimestamp();
  
  // إضافة صورة حسب النتيجة
  if (result === 'heads') {
    embed.setThumbnail('https://upload.wikimedia.org/wikipedia/commons/thumb/a/a0/2006_Quarter_Proof.png/256px-2006_Quarter_Proof.png');
  } else {
    embed.setThumbnail('https://upload.wikimedia.org/wikipedia/commons/thumb/6/64/Quarter_reverse.png/256px-Quarter_reverse.png');
  }
  
  // إضافة إحصائيات وهمية
  const stats = generateStats();
  embed.addFields({
    name: '📈 إحصائياتك',
    value: `🏆 انتصارات: ${stats.wins} | 💔 هزائم: ${stats.losses} | 📊 نسبة الفوز: ${stats.winRate}%`,
    inline: false
  });
  
  // إضافة حقائق مثيرة عن العملات
  const facts = [
    '💡 حقيقة: احتمالية الحصول على وجه أو كتابة هي 50% لكل منهما!',
    '💡 حقيقة: أول عملة معدنية صُنعت في ليديا (تركيا الحالية) حوالي 650 ق.م!',
    '💡 حقيقة: قلب العملة يُستخدم لاتخاذ القرارات منذ آلاف السنين!',
    '💡 حقيقة: في بعض الثقافات، يُعتبر قلب العملة طريقة للتواصل مع الآلهة!',
    '💡 حقيقة: العملة المعدنية الأمريكية لها 119 خط على الحافة!'
  ];
  
  embed.addFields({
    name: '🤓 هل تعلم؟',
    value: facts[Math.floor(Math.random() * facts.length)],
    inline: false
  });
  
  return embed;
}

// إنشاء إحصائيات وهمية
function generateStats() {
  const wins = Math.floor(Math.random() * 100) + 10;
  const losses = Math.floor(Math.random() * 80) + 5;
  const winRate = Math.round((wins / (wins + losses)) * 100);
  
  return { wins, losses, winRate };
}
