// ملف JavaScript الرئيسي للوحة التحكم
// 📺 CS Discord: https://www.youtube.com/@CS_Discord
// 💬 Discord: https://discord.gg/yqTn2EwVsd

// 🛡️ حماية حقوق CS Discord
(function() {
    'use strict';

    // حماية ضد F12 وأدوات المطور
    const CS_PROTECTION = {
        youtube: 'https://www.youtube.com/@CS_Discord',
        discord: 'https://discord.gg/yqTn2EwVsd',
        author: 'CS Discord'
    };

    // منع فتح أدوات المطور
    let devtools = {
        open: false,
        orientation: null
    };

    const threshold = 160;
    setInterval(() => {
        if (window.outerHeight - window.innerHeight > threshold ||
            window.outerWidth - window.innerWidth > threshold) {
            if (!devtools.open) {
                devtools.open = true;
                console.clear();
                console.log('%c🛡️ تحذير أمني!', 'color: red; font-size: 30px; font-weight: bold;');
                console.log('%c📺 CS Discord: https://www.youtube.com/@CS_Discord', 'color: #ff0000; font-size: 16px;');
                console.log('%c💬 Discord: https://discord.gg/yqTn2EwVsd', 'color: #7289da; font-size: 16px;');
                console.log('%c⚠️ يرجى عدم العبث بالكود أو حذف حقوق المطور', 'color: orange; font-size: 14px;');
            }
        } else {
            devtools.open = false;
        }
    }, 500);

    // منع النقر بالزر الأيمن
    document.addEventListener('contextmenu', function(e) {
        e.preventDefault();
        return false;
    });

    // منع اختصارات لوحة المفاتيح
    document.addEventListener('keydown', function(e) {
        // منع F12, Ctrl+Shift+I, Ctrl+Shift+J, Ctrl+U
        if (e.keyCode === 123 ||
            (e.ctrlKey && e.shiftKey && (e.keyCode === 73 || e.keyCode === 74)) ||
            (e.ctrlKey && e.keyCode === 85)) {
            e.preventDefault();
            return false;
        }
    });

    // التحقق من سلامة الحقوق
    function validateCredits() {
        const elements = document.querySelectorAll('[data-cs-credit]');
        elements.forEach(el => {
            if (!el.textContent.includes('CS Discord') ||
                !el.href ||
                (!el.href.includes('CS_Discord') && !el.href.includes('yqTn2EwVsd'))) {
                console.error('❌ تم اكتشاف تعديل في حقوق CS Discord');
                window.location.href = '/';
            }
        });
    }

    // فحص دوري للحقوق
    setInterval(validateCredits, 5000);

    // حماية ضد تعديل الكونسول
    Object.defineProperty(window, 'console', {
        value: console,
        writable: false,
        configurable: false
    });
})();

// إعدادات عامة
const DashboardApp = {
    // إعدادات API
    api: {
        baseUrl: '/api',
        timeout: 10000
    },
    
    // إعدادات التنبيهات
    notifications: {
        duration: 5000,
        position: 'top-left'
    },
    
    // حالة التطبيق
    state: {
        currentGuild: null,
        user: null,
        theme: 'dark'
    },
    
    // تهيئة التطبيق
    init() {
        this.setupEventListeners();
        this.setupAxiosDefaults();
        this.loadUserPreferences();
        this.initializeComponents();
        console.log('تم تهيئة لوحة التحكم بنجاح');
    },
    
    // إعداد مستمعي الأحداث
    setupEventListeners() {
        // إغلاق التنبيهات تلقائياً
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('alert-dismissible')) {
                this.dismissAlert(e.target);
            }
        });
        
        // معالجة النماذج
        document.addEventListener('submit', (e) => {
            if (e.target.classList.contains('ajax-form')) {
                e.preventDefault();
                this.handleFormSubmit(e.target);
            }
        });
        
        // معالجة الأزرار التفاعلية
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('btn-loading')) {
                this.showButtonLoading(e.target);
            }
        });
        
        // حفظ تلقائي للنماذج
        document.addEventListener('input', (e) => {
            if (e.target.classList.contains('auto-save')) {
                this.debounce(() => {
                    this.autoSaveForm(e.target.closest('form'));
                }, 1000)();
            }
        });
    },
    
    // إعداد Axios
    setupAxiosDefaults() {
        axios.defaults.timeout = this.api.timeout;
        axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';
        
        // معالج الاستجابات
        axios.interceptors.response.use(
            (response) => response,
            (error) => {
                this.handleApiError(error);
                return Promise.reject(error);
            }
        );
    },
    
    // تحميل تفضيلات المستخدم
    loadUserPreferences() {
        const savedTheme = localStorage.getItem('dashboard-theme');
        if (savedTheme) {
            this.setTheme(savedTheme);
        }
        
        const savedLanguage = localStorage.getItem('dashboard-language');
        if (savedLanguage) {
            this.setLanguage(savedLanguage);
        }
    },
    
    // تهيئة المكونات
    initializeComponents() {
        this.initTooltips();
        this.initModals();
        this.initTabs();
        this.initCharts();
    },
    
    // تهيئة التلميحات
    initTooltips() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    },
    
    // تهيئة النوافذ المنبثقة
    initModals() {
        // إضافة معالجات للنوافذ المنبثقة
        document.querySelectorAll('.modal').forEach(modal => {
            modal.addEventListener('show.bs.modal', (e) => {
                console.log('فتح النافذة المنبثقة:', modal.id);
            });
        });
    },
    
    // تهيئة التبويبات
    initTabs() {
        document.querySelectorAll('[data-bs-toggle="tab"]').forEach(tab => {
            tab.addEventListener('shown.bs.tab', (e) => {
                const targetId = e.target.getAttribute('data-bs-target');
                console.log('تم تغيير التبويب إلى:', targetId);
            });
        });
    },
    
    // تهيئة الرسوم البيانية
    initCharts() {
        // سيتم إضافة مكتبة الرسوم البيانية لاحقاً
        console.log('تهيئة الرسوم البيانية...');
    },
    
    // معالجة إرسال النماذج
    async handleFormSubmit(form) {
        const formData = new FormData(form);
        const url = form.action || window.location.pathname;
        const method = form.method || 'POST';
        
        try {
            this.showFormLoading(form);
            
            const response = await axios({
                method: method,
                url: url,
                data: formData,
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            });
            
            this.hideFormLoading(form);
            
            if (response.data.success) {
                this.showNotification('success', response.data.message || 'تم الحفظ بنجاح');
                
                // إعادة تحميل الصفحة إذا لزم الأمر
                if (response.data.reload) {
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                }
            } else {
                this.showNotification('error', response.data.message || 'حدث خطأ');
            }
        } catch (error) {
            this.hideFormLoading(form);
            this.showNotification('error', 'حدث خطأ أثناء الحفظ');
        }
    },
    
    // حفظ تلقائي للنماذج
    async autoSaveForm(form) {
        if (!form) return;
        
        const formData = new FormData(form);
        const url = form.dataset.autoSaveUrl || form.action;
        
        try {
            const response = await axios.post(url, formData);
            
            if (response.data.success) {
                this.showMiniNotification('تم الحفظ تلقائياً');
            }
        } catch (error) {
            console.error('خطأ في الحفظ التلقائي:', error);
        }
    },
    
    // عرض حالة التحميل للنموذج
    showFormLoading(form) {
        const submitBtn = form.querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.disabled = true;
            submitBtn.classList.add('loading');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
        }
    },
    
    // إخفاء حالة التحميل للنموذج
    hideFormLoading(form) {
        const submitBtn = form.querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.disabled = false;
            submitBtn.classList.remove('loading');
            submitBtn.innerHTML = submitBtn.dataset.originalText || 'حفظ';
        }
    },
    
    // عرض حالة التحميل للزر
    showButtonLoading(button) {
        button.disabled = true;
        button.dataset.originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحميل...';
    },
    
    // إخفاء حالة التحميل للزر
    hideButtonLoading(button) {
        button.disabled = false;
        button.innerHTML = button.dataset.originalText || button.innerHTML;
    },
    
    // عرض التنبيهات
    showNotification(type, message, duration = null) {
        const alertTypes = {
            success: 'alert-success',
            error: 'alert-danger',
            warning: 'alert-warning',
            info: 'alert-info'
        };
        
        const alertClass = alertTypes[type] || 'alert-info';
        const alertId = 'alert-' + Date.now();
        
        const alertHtml = `
            <div id="${alertId}" class="alert ${alertClass} alert-dismissible fade show" role="alert">
                <i class="fas fa-${this.getAlertIcon(type)}"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        // إضافة التنبيه إلى الصفحة
        let alertContainer = document.getElementById('alert-container');
        if (!alertContainer) {
            alertContainer = document.createElement('div');
            alertContainer.id = 'alert-container';
            alertContainer.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                max-width: 400px;
            `;
            document.body.appendChild(alertContainer);
        }
        
        alertContainer.insertAdjacentHTML('beforeend', alertHtml);
        
        // إزالة التنبيه تلقائياً
        const alertElement = document.getElementById(alertId);
        setTimeout(() => {
            if (alertElement) {
                alertElement.remove();
            }
        }, duration || this.notifications.duration);
    },
    
    // عرض تنبيه صغير
    showMiniNotification(message) {
        const notification = document.createElement('div');
        notification.className = 'mini-notification';
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: var(--success-color);
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            z-index: 9999;
            font-size: 0.9rem;
            opacity: 0;
            transition: opacity 0.3s ease;
        `;
        
        document.body.appendChild(notification);
        
        // إظهار التنبيه
        setTimeout(() => {
            notification.style.opacity = '1';
        }, 100);
        
        // إخفاء التنبيه
        setTimeout(() => {
            notification.style.opacity = '0';
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 2000);
    },
    
    // الحصول على أيقونة التنبيه
    getAlertIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    },
    
    // معالجة أخطاء API
    handleApiError(error) {
        let message = 'حدث خطأ غير متوقع';
        
        if (error.response) {
            // خطأ من الخادم
            if (error.response.status === 401) {
                message = 'انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى.';
                setTimeout(() => {
                    window.location.href = '/auth/login';
                }, 2000);
            } else if (error.response.status === 403) {
                message = 'ليس لديك صلاحية لتنفيذ هذا الإجراء';
            } else if (error.response.status === 429) {
                message = 'تم تجاوز الحد المسموح من الطلبات. يرجى المحاولة لاحقاً.';
            } else if (error.response.data && error.response.data.message) {
                message = error.response.data.message;
            }
        } else if (error.request) {
            // خطأ في الشبكة
            message = 'خطأ في الاتصال. يرجى التحقق من اتصال الإنترنت.';
        }
        
        this.showNotification('error', message);
    },
    
    // تغيير المظهر
    setTheme(theme) {
        this.state.theme = theme;
        document.documentElement.setAttribute('data-theme', theme);
        localStorage.setItem('dashboard-theme', theme);
        
        // تحديث رابط CSS المظهر
        const themeLink = document.getElementById('theme-css');
        if (themeLink) {
            themeLink.href = `/static/css/themes/${theme}.css`;
        }
    },
    
    // تغيير اللغة
    setLanguage(language) {
        localStorage.setItem('dashboard-language', language);
        // يمكن إضافة منطق تغيير اللغة هنا
    },
    
    // دالة التأخير (Debounce)
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    // تأكيد الإجراء
    confirmAction(message, callback) {
        if (confirm(message)) {
            callback();
        }
    },
    
    // نسخ النص إلى الحافظة
    async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            this.showMiniNotification('تم النسخ إلى الحافظة');
        } catch (error) {
            console.error('خطأ في النسخ:', error);
            this.showNotification('error', 'فشل في النسخ إلى الحافظة');
        }
    },
    
    // تنسيق التاريخ
    formatDate(date, options = {}) {
        const defaultOptions = {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        };
        
        return new Date(date).toLocaleDateString('ar-SA', { ...defaultOptions, ...options });
    },
    
    // تنسيق الأرقام
    formatNumber(number) {
        return new Intl.NumberFormat('ar-SA').format(number);
    }
};

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    DashboardApp.init();
});

// تصدير للاستخدام العام
window.DashboardApp = DashboardApp;
