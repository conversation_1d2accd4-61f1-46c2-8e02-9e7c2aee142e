# 🔧 حل مشاكل قاعدة البيانات

## 🚨 المشكلة الحالية
```
MongooseServerSelectionError: connect ECONNREFUSED ::1:27017
```

## ⚡ الحل السريع

### الخيار 1: استخدام MongoDB Atlas (مجاني - موصى به)

1. **اذهب إلى:** https://www.mongodb.com/atlas
2. **أنشئ حساب مجاني**
3. **أنشئ cluster مجاني**
4. **احصل على connection string**
5. **افتح ملف `config.js`**
6. **ابحث عن:**
```javascript
mongoUri: 'mongodb://Abdullah:<EMAIL>:27017/db_Abdullah?authSource=admin',
```
7. **استبدله بـ:**
```javascript
mongoUri: 'mongodb+srv://username:<EMAIL>/database',
```

---

### الخيار 2: قاعدة بيانات محلية بدون مصادقة

1. **افتح ملف `config.js`**
2. **ابحث عن:**
```javascript
mongoUri: 'mongodb://Abdullah:<EMAIL>:27017/db_Abdullah?authSource=admin',
```
3. **استبدله بـ:**
```javascript
mongoUri: 'mongodb://localhost:27017/discord-bot-dashboard',
```
4. **تأكد من تشغيل MongoDB على جهازك**

---

### الخيار 3: استخدام خدمة أخرى

يمكنك استخدام أي خدمة MongoDB مجانية مثل:
- **MongoDB Atlas** (الأفضل)
- **Railway**
- **Heroku MongoDB**

---

## 🔧 خطوات التطبيق

### 1. افتح ملف config.js
```bash
notepad config.js  # Windows
nano config.js     # Linux/Mac
```

### 2. ابحث عن قسم database
```javascript
// إعدادات قاعدة البيانات
database: {
  mongoUri: 'الرابط_الحالي_هنا',
  options: {
    // ...
  }
},
```

### 3. استبدل mongoUri بالرابط الجديد

### 4. احفظ الملف

### 5. أعد تشغيل الخادم
```bash
npm start
```

---

## 📋 روابط قواعد البيانات الجاهزة

### للاختبار المحلي:
```javascript
mongoUri: 'mongodb://localhost:27017/discord-bot-dashboard',
```

### لـ MongoDB Atlas:
```javascript
mongoUri: 'mongodb+srv://username:<EMAIL>/database',
```

### لخادم خارجي:
```javascript
mongoUri: '********************************:port/database',
```

---

## ✅ التأكد من نجاح الحل

عند تشغيل الخادم، يجب أن ترى:
```
🚀 الخادم يعمل على http://localhost:3000
✅ تم الاتصال بقاعدة البيانات بنجاح
```

بدلاً من:
```
❌ خطأ في الاتصال بقاعدة البيانات
```

---

## 📞 الدعم

📺 **يوتيوب**: https://www.youtube.com/@CS_Discord  
💬 **ديسكورد**: https://discord.gg/yqTn2EwVsd

---
**تطوير: CS Discord** 🗄️
