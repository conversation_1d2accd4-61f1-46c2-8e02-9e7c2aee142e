const express = require('express');
const passport = require('passport');
const router = express.Router();
const config = require('../config');

// صفحة تسجيل الدخول
router.get('/login', (req, res) => {
  if (req.user) {
    return res.redirect('/dashboard');
  }
  
  res.render('auth/login', {
    title: 'تسجيل الدخول',
    error: req.query.error
  });
});

// بدء عملية تسجيل الدخول عبر Discord
router.get('/discord', passport.authenticate('discord'));

// معالجة الاستجابة من Discord
router.get('/discord/callback', 
  passport.authenticate('discord', { 
    failureRedirect: '/auth/login?error=auth_failed' 
  }),
  async (req, res) => {
    try {
      // تحديث إحصائيات تسجيل الدخول
      if (req.user && req.user.updateLoginStats) {
        await req.user.updateLoginStats(req);
      }
      
      // إعادة التوجيه إلى لوحة التحكم
      res.redirect('/dashboard');
    } catch (error) {
      console.error('خطأ في معالجة تسجيل الدخول:', error);
      res.redirect('/auth/login?error=login_error');
    }
  }
);

// تسجيل الخروج
router.get('/logout', (req, res) => {
  req.logout((err) => {
    if (err) {
      console.error('خطأ في تسجيل الخروج:', err);
    }
    req.session.destroy((err) => {
      if (err) {
        console.error('خطأ في إنهاء الجلسة:', err);
      }
      res.redirect('/');
    });
  });
});

// التحقق من حالة تسجيل الدخول (API)
router.get('/status', (req, res) => {
  res.json({
    authenticated: !!req.user,
    user: req.user ? {
      id: req.user.discordId,
      username: req.user.username,
      avatar: req.user.avatar
    } : null
  });
});

module.exports = router;
