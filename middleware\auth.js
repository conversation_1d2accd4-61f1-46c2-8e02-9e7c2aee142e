const config = require('../config');

// التحقق من تسجيل الدخول
const requireAuth = (req, res, next) => {
  if (req.isAuthenticated()) {
    return next();
  }
  
  // إذا كان طلب API، إرجاع خطأ JSON
  if (req.path.startsWith('/api/')) {
    return res.status(401).json({
      success: false,
      message: config.messages.errors.noPermission
    });
  }
  
  // إعادة توجيه إلى صفحة تسجيل الدخول
  res.redirect('/auth/login');
};

// التحقق من صلاحيات الخادم
const requireGuildAccess = (req, res, next) => {
  const { guildId } = req.params;
  
  if (!guildId) {
    return res.status(400).json({
      success: false,
      message: 'معرف الخادم مطلوب'
    });
  }

  // التحقق من أن المستخدم لديه صلاحية للوصول لهذا الخادم
  const hasAccess = req.user.permissions.guilds.some(guild => 
    guild.guildId === guildId
  );

  if (!hasAccess) {
    // إذا كان طلب API، إرجاع خطأ JSON
    if (req.path.startsWith('/api/')) {
      return res.status(403).json({
        success: false,
        message: config.messages.errors.noPermission
      });
    }
    
    // إعادة توجيه إلى صفحة اختيار الخادم
    return res.redirect('/dashboard/select-guild');
  }

  next();
};

// التحقق من صلاحية محددة في الخادم
const requireGuildPermission = (permission) => {
  return (req, res, next) => {
    const { guildId } = req.params;
    
    if (!req.user.hasGuildPermission(guildId, permission)) {
      if (req.path.startsWith('/api/')) {
        return res.status(403).json({
          success: false,
          message: config.messages.errors.noPermission
        });
      }
      
      return res.status(403).render('errors/403', {
        title: 'ممنوع الوصول',
        message: config.messages.errors.noPermission
      });
    }
    
    next();
  };
};

// التحقق من صلاحيات النظام
const requireSystemPermission = (permission) => {
  return (req, res, next) => {
    if (!req.user.permissions.system[permission]) {
      if (req.path.startsWith('/api/')) {
        return res.status(403).json({
          success: false,
          message: config.messages.errors.noPermission
        });
      }
      
      return res.status(403).render('errors/403', {
        title: 'ممنوع الوصول',
        message: config.messages.errors.noPermission
      });
    }
    
    next();
  };
};

// التحقق من أن المستخدم مدير
const requireAdmin = requireSystemPermission('isAdmin');

// التحقق من أن المستخدم مشرف
const requireModerator = (req, res, next) => {
  if (!req.user.permissions.system.isAdmin && !req.user.permissions.system.isModerator) {
    if (req.path.startsWith('/api/')) {
      return res.status(403).json({
        success: false,
        message: config.messages.errors.noPermission
      });
    }
    
    return res.status(403).render('errors/403', {
      title: 'ممنوع الوصول',
      message: config.messages.errors.noPermission
    });
  }
  
  next();
};

// التحقق من حالة الحساب
const requireActiveAccount = (req, res, next) => {
  if (req.user.status !== 'active') {
    if (req.path.startsWith('/api/')) {
      return res.status(403).json({
        success: false,
        message: 'حسابك معطل أو محظور'
      });
    }
    
    return res.status(403).render('errors/account-suspended', {
      title: 'حساب معطل',
      message: 'حسابك معطل أو محظور. يرجى التواصل مع الإدارة.'
    });
  }
  
  next();
};

// middleware لتسجيل النشاطات
const logActivity = (action, getDetails) => {
  return async (req, res, next) => {
    try {
      const details = typeof getDetails === 'function' ? getDetails(req) : getDetails;
      const guildId = req.params.guildId || null;
      
      await req.user.logActivity(action, details, guildId, req);
    } catch (error) {
      console.error('خطأ في تسجيل النشاط:', error);
      // لا نوقف العملية إذا فشل تسجيل النشاط
    }
    
    next();
  };
};

// middleware للتحقق من معدل الطلبات لكل مستخدم
const userRateLimit = (maxRequests = 100, windowMs = 15 * 60 * 1000) => {
  const userRequests = new Map();
  
  return (req, res, next) => {
    if (!req.user) {
      return next();
    }
    
    const userId = req.user.discordId;
    const now = Date.now();
    const windowStart = now - windowMs;
    
    // الحصول على طلبات المستخدم
    let requests = userRequests.get(userId) || [];
    
    // تصفية الطلبات القديمة
    requests = requests.filter(timestamp => timestamp > windowStart);
    
    // التحقق من تجاوز الحد
    if (requests.length >= maxRequests) {
      return res.status(429).json({
        success: false,
        message: config.messages.errors.rateLimited
      });
    }
    
    // إضافة الطلب الحالي
    requests.push(now);
    userRequests.set(userId, requests);
    
    next();
  };
};

// middleware للتحقق من صحة البيانات
const validateGuildId = (req, res, next) => {
  const { guildId } = req.params;
  
  if (!guildId || !/^\d{17,19}$/.test(guildId)) {
    if (req.path.startsWith('/api/')) {
      return res.status(400).json({
        success: false,
        message: 'معرف الخادم غير صحيح'
      });
    }
    
    return res.status(400).render('errors/400', {
      title: 'طلب غير صحيح',
      message: 'معرف الخادم غير صحيح'
    });
  }
  
  next();
};

module.exports = {
  requireAuth,
  requireGuildAccess,
  requireGuildPermission,
  requireSystemPermission,
  requireAdmin,
  requireModerator,
  requireActiveAccount,
  logActivity,
  userRateLimit,
  validateGuildId
};
