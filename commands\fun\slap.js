// ========================================
// 👋 أمر اللطشة - CS Discord
// ========================================
// 📺 يوتيوب: https://www.youtube.com/@CS_Discord
// 💬 ديسكورد: https://discord.gg/yqTn2EwVsd
// ========================================

const { EmbedBuilder } = require('discord.js');

module.exports = {
  name: 'slap',
  aliases: ['لطش', 'ضرب', 'صفع'],
  description: 'لطش عضو 😂',
  usage: '<@المستخدم>',
  category: 'fun',
  cooldown: 3,
  
  async execute(message, args) {
    try {
      // التحقق من وجود المستخدم المذكور
      const targetUser = message.mentions.users.first();
      
      if (!targetUser) {
        return await message.reply('❌ يجب ذكر المستخدم الذي تريد لطشه!\nمثال: `slap @أحمد`');
      }
      
      // التحقق من عدم لطش النفس
      if (targetUser.id === message.author.id) {
        return await message.reply('😅 لا يمكنك لطش نفسك! هذا مؤذي!');
      }
      
      // التحقق من عدم لطش البوت
      if (targetUser.bot) {
        return await message.reply('🤖 البوتات محمية من اللطش! جرب مع إنسان!');
      }
      
      // صور اللطش المتحركة
      const slapGifs = [
        'https://media.giphy.com/media/Zau0yrl17uzdK/giphy.gif',
        'https://media.giphy.com/media/3XlEk2RxPS1m8/giphy.gif',
        'https://media.giphy.com/media/mEtSQlxVLhPJmNONgr/giphy.gif',
        'https://media.giphy.com/media/xUO4t2gkWBxDi/giphy.gif',
        'https://media.giphy.com/media/JLmTbxBZSKUKI/giphy.gif',
        'https://media.giphy.com/media/i3RA5wLyWjCRa/giphy.gif',
        'https://media.giphy.com/media/QvBoMEcQ7DQXK/giphy.gif',
        'https://media.giphy.com/media/tX29X2Dx3sAXS/giphy.gif',
        'https://media.giphy.com/media/xTiTnslQqnN4W6gCis/giphy.gif',
        'https://media.giphy.com/media/l3V0doGbp2EDaLHJC/giphy.gif'
      ];
      
      // اختيار صورة عشوائية
      const randomGif = slapGifs[Math.floor(Math.random() * slapGifs.length)];
      
      // رسائل اللطش المختلفة
      const slapMessages = [
        `👋 **${message.author.username}** لطش **${targetUser.username}** لطشة قوية!`,
        `😂 **${message.author.username}** صفع **${targetUser.username}** بقوة!`,
        `💥 **${message.author.username}** ضرب **${targetUser.username}** ضربة مؤلمة!`,
        `🤕 **${message.author.username}** لطش **${targetUser.username}** لطشة تستاهل!`,
        `😤 **${message.author.username}** صفع **${targetUser.username}** عشان يفوق!`,
        `👊 **${message.author.username}** ضرب **${targetUser.username}** ضربة محترمة!`,
        `😅 **${message.author.username}** لطش **${targetUser.username}** للمزح!`,
        `🎯 **${message.author.username}** صفع **${targetUser.username}** في المكان المناسب!`
      ];
      
      // اختيار رسالة عشوائية
      const randomMessage = slapMessages[Math.floor(Math.random() * slapMessages.length)];
      
      // حساب قوة اللطشة (عشوائي)
      const slapPower = Math.floor(Math.random() * 100) + 1;
      let powerDescription = '';
      
      if (slapPower >= 90) {
        powerDescription = '🔥 لطشة نووية!';
      } else if (slapPower >= 70) {
        powerDescription = '💪 لطشة قوية جداً!';
      } else if (slapPower >= 50) {
        powerDescription = '👋 لطشة متوسطة';
      } else if (slapPower >= 30) {
        powerDescription = '😊 لطشة خفيفة';
      } else {
        powerDescription = '🤏 لطشة ناعمة';
      }
      
      // إنشاء الـ embed
      const embed = new EmbedBuilder()
        .setColor('#ff4444')
        .setTitle('👋 لطشة قوية!')
        .setDescription(randomMessage)
        .setImage(randomGif)
        .addFields(
          {
            name: '💥 قوة اللطشة',
            value: `**${slapPower}%** - ${powerDescription}`,
            inline: true
          },
          {
            name: '🎯 الهدف',
            value: `${targetUser}`,
            inline: true
          }
        )
        .setFooter({
          text: 'CS Discord Fun Commands',
          iconURL: message.client.user.displayAvatarURL()
        })
        .setTimestamp();
      
      // إضافة نصائح مضحكة أحياناً
      if (Math.random() < 0.4) {
        const funTips = [
          '💡 نصيحة: اللطش بالحب أفضل من اللطش بالغضب!',
          '💡 تذكر: هذا مجرد مزح، لا تلطش في الحقيقة!',
          '💡 معلومة: اللطش الافتراضي لا يؤذي!',
          '💡 تحذير: لا تجرب هذا في المنزل!',
          '💡 نصيحة: الصداقة أهم من اللطش!'
        ];
        
        embed.addFields({
          name: '😄 نصيحة مضحكة',
          value: funTips[Math.floor(Math.random() * funTips.length)],
          inline: false
        });
      }
      
      await message.reply({ embeds: [embed] });
      
      // إضافة ردود فعل عشوائية
      const reactions = ['👋', '😂', '💥', '🤕', '😅'];
      const randomReaction = reactions[Math.floor(Math.random() * reactions.length)];
      
      try {
        await message.react(randomReaction);
      } catch (error) {
        // تجاهل خطأ الردود إذا لم تكن متاحة
      }
      
    } catch (error) {
      console.error('خطأ في أمر اللطشة:', error);
      await message.reply('❌ حدث خطأ أثناء اللطش!');
    }
  }
};
