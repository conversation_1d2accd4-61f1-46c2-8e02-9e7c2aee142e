const mongoose = require('mongoose');

// نموذج حماية حقوق CS Discord
const csProtectionSchema = new mongoose.Schema({
  // معلومات المطور الأصلي
  originalCredits: {
    youtube: {
      type: String,
      required: true,
      default: 'https://www.youtube.com/@CS_Discord',
      immutable: true
    },
    discord: {
      type: String,
      required: true,
      default: 'https://discord.gg/yqTn2EwVsd',
      immutable: true
    },
    author: {
      type: String,
      required: true,
      default: 'CS Discord',
      immutable: true
    }
  },

  // معلومات النسخة
  version: {
    type: String,
    required: true,
    default: '1.0.0'
  },

  // تاريخ التثبيت
  installDate: {
    type: Date,
    default: Date.now,
    immutable: true
  },

  // معلومات الخادم
  serverInfo: {
    hostname: String,
    platform: String,
    nodeVersion: String,
    installPath: String
  },

  // حالة الحماية
  protectionStatus: {
    isActive: {
      type: Boolean,
      default: true
    },
    lastCheck: {
      type: Date,
      default: Date.now
    },
    violations: [{
      type: {
        type: String,
        enum: ['credits_modified', 'credits_removed', 'unauthorized_modification']
      },
      timestamp: {
        type: Date,
        default: Date.now
      },
      details: String,
      severity: {
        type: String,
        enum: ['low', 'medium', 'high', 'critical'],
        default: 'medium'
      }
    }]
  },

  // معلومات المستخدم
  userInfo: {
    discordId: String,
    username: String,
    email: String,
    agreedToTerms: {
      type: Boolean,
      default: false
    },
    agreementDate: Date
  },

  // إعدادات الحماية
  protectionSettings: {
    enableRealTimeCheck: {
      type: Boolean,
      default: true
    },
    checkInterval: {
      type: Number,
      default: 3600000 // ساعة واحدة بالميلي ثانية
    },
    maxViolations: {
      type: Number,
      default: 3
    },
    autoDisableOnViolation: {
      type: Boolean,
      default: true
    }
  },

  // معلومات إضافية
  metadata: {
    licenseType: {
      type: String,
      default: 'free',
      enum: ['free', 'premium', 'enterprise']
    },
    features: [String],
    customizations: [{
      type: String,
      description: String,
      authorizedBy: String,
      timestamp: {
        type: Date,
        default: Date.now
      }
    }]
  }
}, {
  timestamps: true,
  versionKey: false
});

// فهارس للبحث السريع
csProtectionSchema.index({ 'userInfo.discordId': 1 });
csProtectionSchema.index({ 'protectionStatus.isActive': 1 });
csProtectionSchema.index({ installDate: 1 });

// التحقق من سلامة الحقوق قبل الحفظ
csProtectionSchema.pre('save', function(next) {
  // التأكد من عدم تعديل الحقوق الأصلية
  if (this.isModified('originalCredits')) {
    const requiredCredits = {
      youtube: 'https://www.youtube.com/@CS_Discord',
      discord: 'https://discord.gg/yqTn2EwVsd',
      author: 'CS Discord'
    };

    if (this.originalCredits.youtube !== requiredCredits.youtube ||
        this.originalCredits.discord !== requiredCredits.discord ||
        this.originalCredits.author !== requiredCredits.author) {
      
      // تسجيل انتهاك
      this.protectionStatus.violations.push({
        type: 'credits_modified',
        details: 'محاولة تعديل حقوق المطور الأصلي',
        severity: 'critical',
        timestamp: new Date()
      });

      // إعادة تعيين الحقوق الصحيحة
      this.originalCredits = requiredCredits;
    }
  }

  // تحديث آخر فحص
  this.protectionStatus.lastCheck = new Date();
  next();
});

// دالة للتحقق من سلامة النظام
csProtectionSchema.statics.validateSystem = async function() {
  try {
    let protection = await this.findOne({});
    
    if (!protection) {
      // إنشاء سجل حماية جديد
      protection = new this({
        serverInfo: {
          hostname: require('os').hostname(),
          platform: process.platform,
          nodeVersion: process.version,
          installPath: process.cwd()
        }
      });
      await protection.save();
    }

    // التحقق من الحقوق
    const requiredCredits = {
      youtube: 'https://www.youtube.com/@CS_Discord',
      discord: 'https://discord.gg/yqTn2EwVsd',
      author: 'CS Discord'
    };

    let hasViolation = false;
    if (protection.originalCredits.youtube !== requiredCredits.youtube ||
        protection.originalCredits.discord !== requiredCredits.discord ||
        protection.originalCredits.author !== requiredCredits.author) {
      hasViolation = true;
    }

    if (hasViolation) {
      protection.protectionStatus.violations.push({
        type: 'credits_modified',
        details: 'تم اكتشاف تعديل في حقوق المطور',
        severity: 'critical'
      });

      if (protection.protectionSettings.autoDisableOnViolation) {
        protection.protectionStatus.isActive = false;
      }

      await protection.save();
      return false;
    }

    return true;
  } catch (error) {
    console.error('خطأ في التحقق من حماية النظام:', error);
    return false;
  }
};

// دالة لتسجيل انتهاك
csProtectionSchema.methods.logViolation = function(type, details, severity = 'medium') {
  this.protectionStatus.violations.push({
    type,
    details,
    severity,
    timestamp: new Date()
  });

  // إذا تجاوز عدد الانتهاكات الحد المسموح
  if (this.protectionStatus.violations.length >= this.protectionSettings.maxViolations) {
    this.protectionStatus.isActive = false;
  }

  return this.save();
};

// دالة للحصول على معلومات الحماية
csProtectionSchema.statics.getProtectionInfo = async function() {
  const protection = await this.findOne({});
  if (!protection) {
    return null;
  }

  return {
    isActive: protection.protectionStatus.isActive,
    version: protection.version,
    installDate: protection.installDate,
    lastCheck: protection.protectionStatus.lastCheck,
    violationsCount: protection.protectionStatus.violations.length,
    credits: protection.originalCredits
  };
};

module.exports = mongoose.model('CSProtection', csProtectionSchema);
