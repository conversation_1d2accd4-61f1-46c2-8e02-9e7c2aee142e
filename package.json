{"name": "discord-bot-dashboard", "version": "1.0.0", "description": "لوحة تحكم شاملة لبوت ديسكورد مع جميع الميزات المطلوبة", "main": "index.js", "scripts": {"start": "node bot.js", "dev": "nodemon bot.js", "web": "node index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["discord", "bot", "dashboard", "express", "mongodb", "arabic"], "author": "Discord Bot Dashboard", "license": "MIT", "dependencies": {"express": "^4.18.2", "express-session": "^1.17.3", "ejs": "^3.1.9", "mongoose": "^7.5.0", "discord.js": "^14.13.0", "passport": "^0.6.0", "passport-discord": "^0.1.4", "dotenv": "^16.3.1", "body-parser": "^1.20.2", "cors": "^2.8.5", "helmet": "^7.0.0", "express-rate-limit": "^6.10.0", "multer": "^1.4.5-lts.1", "moment": "^2.29.4", "axios": "^1.5.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "express-validator": "^7.0.1", "compression": "^1.7.4", "morgan": "^1.10.0", "connect-mongo": "^5.0.0"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=16.0.0"}}