// ========================================
// 💥 أمر النووي - CS Discord
// ========================================
// 📺 يوتيوب: https://www.youtube.com/@CS_Discord
// 💬 ديسكورد: https://discord.gg/yqTn2EwVsd
// ========================================

const { EmbedBuilder, PermissionFlagsBits, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');

module.exports = {
  name: 'nuke',
  aliases: ['نووي', 'مسح', 'تدمير'],
  description: 'مسح الروم وإعادة إنشاؤه بنظافة',
  usage: '[الروم]',
  category: 'moderation',
  permissions: [PermissionFlagsBits.ManageChannels],
  cooldown: 10,
  
  async execute(message, args) {
    try {
      // التحقق من الصلاحيات
      if (!message.member.permissions.has(PermissionFlagsBits.ManageChannels)) {
        return await message.reply('❌ ليس لديك صلاحية لإدارة الرومات!');
      }
      
      // تحديد الروم
      const channel = message.mentions.channels.first() || message.channel;
      
      // التحقق من نوع الروم
      if (!channel.isTextBased()) {
        return await message.reply('❌ يمكن تطبيق النووي على الرومات النصية فقط!');
      }
      
      // حفظ معلومات الروم
      const channelData = {
        name: channel.name,
        topic: channel.topic,
        position: channel.position,
        parent: channel.parent,
        permissionOverwrites: channel.permissionOverwrites.cache,
        rateLimitPerUser: channel.rateLimitPerUser,
        nsfw: channel.nsfw
      };
      
      // إنشاء embed التأكيد
      const confirmEmbed = new EmbedBuilder()
        .setColor('#ff0000')
        .setTitle('💥 تحذير - عملية نووية!')
        .setDescription(`⚠️ **هل أنت متأكد من أنك تريد تدمير الروم ${channel}؟**\n\n🔥 سيتم:\n• حذف جميع الرسائل\n• إعادة إنشاء الروم\n• الحفاظ على الإعدادات والصلاحيات\n\n**هذا الإجراء لا يمكن التراجع عنه!**`)
        .setFooter({
          text: 'لديك 30 ثانية للتأكيد',
          iconURL: message.client.user.displayAvatarURL()
        })
        .setTimestamp();
      
      // إنشاء أزرار التأكيد
      const confirmRow = new ActionRowBuilder()
        .addComponents(
          new ButtonBuilder()
            .setCustomId('nuke_confirm')
            .setLabel('💥 تأكيد التدمير')
            .setStyle(ButtonStyle.Danger),
          new ButtonBuilder()
            .setCustomId('nuke_cancel')
            .setLabel('❌ إلغاء')
            .setStyle(ButtonStyle.Secondary)
        );
      
      const confirmMessage = await message.reply({ 
        embeds: [confirmEmbed], 
        components: [confirmRow] 
      });
      
      // انتظار التفاعل
      const filter = (interaction) => {
        return interaction.user.id === message.author.id && 
               (interaction.customId === 'nuke_confirm' || interaction.customId === 'nuke_cancel');
      };
      
      try {
        const interaction = await confirmMessage.awaitMessageComponent({ 
          filter, 
          time: 30000 
        });
        
        if (interaction.customId === 'nuke_cancel') {
          const cancelEmbed = new EmbedBuilder()
            .setColor('#00ff00')
            .setTitle('✅ تم إلغاء العملية')
            .setDescription('تم إلغاء عملية التدمير النووي')
            .setTimestamp();
          
          await interaction.update({ embeds: [cancelEmbed], components: [] });
          return;
        }
        
        // تأكيد التدمير
        await interaction.update({ 
          content: '💥 جاري التدمير النووي...', 
          embeds: [], 
          components: [] 
        });
        
        // حذف الروم وإعادة إنشاؤه
        const newChannel = await channel.clone({
          name: channelData.name,
          topic: channelData.topic,
          position: channelData.position,
          parent: channelData.parent,
          permissionOverwrites: channelData.permissionOverwrites,
          rateLimitPerUser: channelData.rateLimitPerUser,
          nsfw: channelData.nsfw,
          reason: `Nuke command by ${message.author.tag}`
        });
        
        // حذف الروم القديم
        await channel.delete(`Nuke command by ${message.author.tag}`);
        
        // إرسال رسالة النجاح في الروم الجديد
        const successEmbed = new EmbedBuilder()
          .setColor('#00ff00')
          .setTitle('💥 تم التدمير النووي بنجاح!')
          .setDescription('🔥 تم تدمير الروم وإعادة إنشاؤه بنظافة تامة!')
          .addFields(
            {
              name: '👤 بواسطة',
              value: `${message.author}`,
              inline: true
            },
            {
              name: '📅 التاريخ',
              value: `<t:${Math.floor(Date.now() / 1000)}:F>`,
              inline: true
            }
          )
          .setImage('https://media.giphy.com/media/oe33xf3B50fsc/giphy.gif') // صورة انفجار
          .setFooter({
            text: 'CS Discord Moderation System',
            iconURL: message.client.user.displayAvatarURL()
          })
          .setTimestamp();
        
        await newChannel.send({ embeds: [successEmbed] });
        
        // إرسال لوج إذا كان مفعل
        const logChannel = message.guild.channels.cache.find(ch => ch.name === 'mod-logs');
        if (logChannel && logChannel.id !== newChannel.id) {
          const logEmbed = new EmbedBuilder()
            .setColor('#ff0000')
            .setTitle('📝 سجل الإدارة - تدمير نووي')
            .addFields(
              {
                name: 'المشرف',
                value: `${message.author.tag} (${message.author.id})`,
                inline: true
              },
              {
                name: 'الروم المدمر',
                value: `${channelData.name} (${channel.id})`,
                inline: true
              },
              {
                name: 'الروم الجديد',
                value: `${newChannel.name} (${newChannel.id})`,
                inline: true
              }
            )
            .setTimestamp();
          
          await logChannel.send({ embeds: [logEmbed] });
        }
        
      } catch (error) {
        // انتهت مهلة التأكيد
        const timeoutEmbed = new EmbedBuilder()
          .setColor('#ffa500')
          .setTitle('⏰ انتهت المهلة')
          .setDescription('تم إلغاء عملية التدمير النووي بسبب انتهاء المهلة')
          .setTimestamp();
        
        await confirmMessage.edit({ embeds: [timeoutEmbed], components: [] });
      }
      
    } catch (error) {
      console.error('خطأ في أمر النووي:', error);
      await message.reply('❌ حدث خطأ أثناء تنفيذ العملية النووية!');
    }
  }
};
