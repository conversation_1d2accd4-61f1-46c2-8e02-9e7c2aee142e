<%- include('../partials/header') %>

<div class="main-content">
    <div class="content-header">
        <h1 class="page-title">
            <i class="fas fa-users-cog"></i>
            إدارة الرتب
        </h1>
        <p class="page-description">
            إنشاء وتعديل وإدارة رتب الخادم بسهولة
        </p>
    </div>

    <!-- أدوات سريعة -->
    <div class="quick-actions">
        <button class="btn btn-success" onclick="openCreateRoleModal()">
            <i class="fas fa-plus"></i>
            إنشاء رتبة جديدة
        </button>
        <button class="btn btn-info" onclick="openRoleTemplatesModal()">
            <i class="fas fa-layer-group"></i>
            قوالب الرتب
        </button>
        <button class="btn btn-warning" onclick="openMassAssignModal()">
            <i class="fas fa-users"></i>
            تعيين جماعي
        </button>
        <button class="btn btn-secondary" onclick="exportRoles()">
            <i class="fas fa-download"></i>
            تصدير الرتب
        </button>
    </div>

    <!-- إحصائيات الرتب -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number" id="total-roles">0</div>
            <div class="stat-label">إجمالي الرتب</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" id="admin-roles">0</div>
            <div class="stat-label">رتب إدارية</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" id="member-roles">0</div>
            <div class="stat-label">رتب الأعضاء</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" id="bot-roles">0</div>
            <div class="stat-label">رتب البوتات</div>
        </div>
    </div>

    <!-- قائمة الرتب -->
    <div class="card">
        <div class="card-header">
            <div class="card-icon">
                <i class="fas fa-list"></i>
            </div>
            <div>
                <h3 class="card-title">رتب الخادم</h3>
            </div>
            <div class="card-actions">
                <button class="btn btn-sm btn-primary" onclick="refreshRoles()">
                    <i class="fas fa-sync"></i>
                    تحديث
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="roles-container" id="roles-container">
                <div class="loading-spinner">
                    <i class="fas fa-spinner fa-spin"></i>
                    جاري تحميل الرتب...
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal إنشاء رتبة جديدة -->
<div class="modal fade" id="createRoleModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">✨ إنشاء رتبة جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createRoleForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">اسم الرتبة</label>
                                <input type="text" class="form-control" id="roleName" placeholder="مثال: مشرف" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">لون الرتبة</label>
                                <div class="color-picker-container">
                                    <input type="color" class="form-control color-picker" id="roleColor" value="#667eea">
                                    <div class="color-presets">
                                        <div class="color-preset" style="background: #ff6b6b" onclick="setRoleColor('#ff6b6b')"></div>
                                        <div class="color-preset" style="background: #4ecdc4" onclick="setRoleColor('#4ecdc4')"></div>
                                        <div class="color-preset" style="background: #45b7d1" onclick="setRoleColor('#45b7d1')"></div>
                                        <div class="color-preset" style="background: #96ceb4" onclick="setRoleColor('#96ceb4')"></div>
                                        <div class="color-preset" style="background: #feca57" onclick="setRoleColor('#feca57')"></div>
                                        <div class="color-preset" style="background: #ff9ff3" onclick="setRoleColor('#ff9ff3')"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">الصلاحيات</label>
                        <div class="permissions-grid">
                            <div class="permission-category">
                                <h6>🛡️ صلاحيات عامة</h6>
                                <div class="permission-item">
                                    <input type="checkbox" id="perm_administrator">
                                    <label for="perm_administrator">مدير (جميع الصلاحيات)</label>
                                </div>
                                <div class="permission-item">
                                    <input type="checkbox" id="perm_manage_guild">
                                    <label for="perm_manage_guild">إدارة الخادم</label>
                                </div>
                                <div class="permission-item">
                                    <input type="checkbox" id="perm_manage_roles">
                                    <label for="perm_manage_roles">إدارة الرتب</label>
                                </div>
                                <div class="permission-item">
                                    <input type="checkbox" id="perm_manage_channels">
                                    <label for="perm_manage_channels">إدارة القنوات</label>
                                </div>
                            </div>
                            
                            <div class="permission-category">
                                <h6>💬 صلاحيات الرسائل</h6>
                                <div class="permission-item">
                                    <input type="checkbox" id="perm_send_messages">
                                    <label for="perm_send_messages">إرسال الرسائل</label>
                                </div>
                                <div class="permission-item">
                                    <input type="checkbox" id="perm_manage_messages">
                                    <label for="perm_manage_messages">إدارة الرسائل</label>
                                </div>
                                <div class="permission-item">
                                    <input type="checkbox" id="perm_embed_links">
                                    <label for="perm_embed_links">تضمين الروابط</label>
                                </div>
                                <div class="permission-item">
                                    <input type="checkbox" id="perm_attach_files">
                                    <label for="perm_attach_files">إرفاق الملفات</label>
                                </div>
                            </div>
                            
                            <div class="permission-category">
                                <h6>🔊 صلاحيات صوتية</h6>
                                <div class="permission-item">
                                    <input type="checkbox" id="perm_connect">
                                    <label for="perm_connect">الاتصال</label>
                                </div>
                                <div class="permission-item">
                                    <input type="checkbox" id="perm_speak">
                                    <label for="perm_speak">التحدث</label>
                                </div>
                                <div class="permission-item">
                                    <input type="checkbox" id="perm_mute_members">
                                    <label for="perm_mute_members">كتم الأعضاء</label>
                                </div>
                                <div class="permission-item">
                                    <input type="checkbox" id="perm_move_members">
                                    <label for="perm_move_members">نقل الأعضاء</label>
                                </div>
                            </div>
                            
                            <div class="permission-category">
                                <h6>👥 صلاحيات الأعضاء</h6>
                                <div class="permission-item">
                                    <input type="checkbox" id="perm_kick_members">
                                    <label for="perm_kick_members">طرد الأعضاء</label>
                                </div>
                                <div class="permission-item">
                                    <input type="checkbox" id="perm_ban_members">
                                    <label for="perm_ban_members">حظر الأعضاء</label>
                                </div>
                                <div class="permission-item">
                                    <input type="checkbox" id="perm_manage_nicknames">
                                    <label for="perm_manage_nicknames">إدارة الألقاب</label>
                                </div>
                                <div class="permission-item">
                                    <input type="checkbox" id="perm_mention_everyone">
                                    <label for="perm_mention_everyone">منشن الجميع</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">إعدادات إضافية</label>
                                <div class="additional-settings">
                                    <div class="setting-item">
                                        <input type="checkbox" id="roleHoist">
                                        <label for="roleHoist">عرض منفصل في قائمة الأعضاء</label>
                                    </div>
                                    <div class="setting-item">
                                        <input type="checkbox" id="roleMentionable">
                                        <label for="roleMentionable">قابلة للمنشن</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">معاينة الرتبة</label>
                                <div class="role-preview">
                                    <div class="preview-role" id="rolePreview">
                                        <div class="role-color-indicator" id="previewColor"></div>
                                        <span id="previewName">اسم الرتبة</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" onclick="createRole()">
                    <i class="fas fa-plus"></i>
                    إنشاء الرتبة
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal قوالب الرتب -->
<div class="modal fade" id="roleTemplatesModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">📋 قوالب الرتب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="templates-grid">
                    <div class="template-card" onclick="applyTemplate('admin')">
                        <div class="template-icon">👑</div>
                        <h6>مدير عام</h6>
                        <p>جميع الصلاحيات الإدارية</p>
                    </div>
                    <div class="template-card" onclick="applyTemplate('moderator')">
                        <div class="template-icon">🛡️</div>
                        <h6>مشرف</h6>
                        <p>صلاحيات الإشراف والحماية</p>
                    </div>
                    <div class="template-card" onclick="applyTemplate('helper')">
                        <div class="template-icon">🤝</div>
                        <h6>مساعد</h6>
                        <p>صلاحيات المساعدة الأساسية</p>
                    </div>
                    <div class="template-card" onclick="applyTemplate('vip')">
                        <div class="template-icon">⭐</div>
                        <h6>VIP</h6>
                        <p>صلاحيات مميزة للأعضاء المهمين</p>
                    </div>
                    <div class="template-card" onclick="applyTemplate('member')">
                        <div class="template-icon">👤</div>
                        <h6>عضو</h6>
                        <p>صلاحيات العضوية الأساسية</p>
                    </div>
                    <div class="template-card" onclick="applyTemplate('muted')">
                        <div class="template-icon">🔇</div>
                        <h6>مكتوم</h6>
                        <p>رتبة للأعضاء المكتومين</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.quick-actions {
    display: flex;
    gap: 15px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #667eea;
    margin-bottom: 10px;
}

.stat-label {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
}

.roles-container {
    min-height: 300px;
}

.loading-spinner {
    text-align: center;
    padding: 50px;
    color: rgba(255, 255, 255, 0.6);
}

.role-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    margin-bottom: 10px;
    border-left: 4px solid;
    transition: all 0.3s ease;
}

.role-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(5px);
}

.role-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.role-color {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.role-details h5 {
    margin: 0;
    color: white;
}

.role-details p {
    margin: 0;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

.role-actions {
    display: flex;
    gap: 10px;
}

.color-picker-container {
    position: relative;
}

.color-picker {
    width: 100%;
    height: 40px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
}

.color-presets {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

.color-preset {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    cursor: pointer;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.color-preset:hover {
    border-color: white;
    transform: scale(1.1);
}

.permissions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    max-height: 400px;
    overflow-y: auto;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
}

.permission-category h6 {
    color: #667eea;
    margin-bottom: 10px;
    font-weight: bold;
}

.permission-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.permission-item input[type="checkbox"] {
    width: 16px;
    height: 16px;
}

.permission-item label {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.9);
    cursor: pointer;
}

.additional-settings {
    background: rgba(255, 255, 255, 0.05);
    padding: 15px;
    border-radius: 10px;
}

.setting-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 10px;
}

.role-preview {
    background: rgba(255, 255, 255, 0.05);
    padding: 15px;
    border-radius: 10px;
    text-align: center;
}

.preview-role {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    padding: 8px 15px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
}

.role-color-indicator {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #667eea;
}

.templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.template-card {
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.template-card:hover {
    border-color: #667eea;
    transform: translateY(-5px);
    background: rgba(102, 126, 234, 0.1);
}

.template-icon {
    font-size: 2rem;
    margin-bottom: 10px;
}

.template-card h6 {
    color: white;
    margin-bottom: 10px;
}

.template-card p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    margin: 0;
}

@media (max-width: 768px) {
    .quick-actions {
        flex-direction: column;
    }
    
    .permissions-grid {
        grid-template-columns: 1fr;
    }
    
    .templates-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}
</style>

<script src="/static/js/roles.js"></script>

<%- include('../partials/footer') %>
