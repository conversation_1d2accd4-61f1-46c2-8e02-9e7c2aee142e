// ========================================
// 🎨 نموذج الأوامر المخصصة - CS Discord
// ========================================
// 📺 يوتيوب: https://www.youtube.com/@CS_Discord
// 💬 ديسكورد: https://discord.gg/yqTn2EwVsd
// ========================================

const mongoose = require('mongoose');

// نموذج البلوك
const blockSchema = new mongoose.Schema({
  id: {
    type: Number,
    required: true
  },
  
  type: {
    type: String,
    required: true,
    enum: ['trigger', 'action', 'condition', 'variable']
  },
  
  subtype: {
    type: String,
    required: true
  },
  
  text: {
    type: String,
    required: true
  },
  
  settings: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  },
  
  position: {
    x: Number,
    y: Number
  }
});

// نموذج الأمر المخصص
const customCommandSchema = new mongoose.Schema({
  // معرف الخادم
  guildId: {
    type: String,
    required: true,
    index: true
  },
  
  // معلومات الأمر
  name: {
    type: String,
    required: true,
    trim: true,
    lowercase: true
  },
  
  description: {
    type: String,
    required: true
  },
  
  aliases: [{
    type: String,
    trim: true,
    lowercase: true
  }],
  
  category: {
    type: String,
    default: 'custom'
  },
  
  // إعدادات الأمر
  settings: {
    cooldown: {
      type: Number,
      default: 3,
      min: 0,
      max: 300
    },
    
    permissions: [{
      type: String
    }],
    
    requiredRoles: [{
      type: String
    }],
    
    allowedChannels: [{
      type: String
    }],
    
    disabledChannels: [{
      type: String
    }],
    
    enabled: {
      type: Boolean,
      default: true
    }
  },
  
  // البلوكات
  blocks: [blockSchema],
  
  // الكود المُولد
  generatedCode: {
    type: String,
    required: true
  },
  
  // معلومات الإنشاء
  createdBy: {
    userId: {
      type: String,
      required: true
    },
    username: String,
    tag: String
  },
  
  // إحصائيات الاستخدام
  usage: {
    totalUses: {
      type: Number,
      default: 0
    },
    lastUsed: Date,
    usageHistory: [{
      userId: String,
      timestamp: {
        type: Date,
        default: Date.now
      }
    }]
  },
  
  // حالة الأمر
  status: {
    type: String,
    enum: ['active', 'disabled', 'error'],
    default: 'active'
  },
  
  // معلومات الأخطاء
  errorInfo: {
    lastError: String,
    errorCount: {
      type: Number,
      default: 0
    },
    lastErrorAt: Date
  },
  
  // الإصدار
  version: {
    type: String,
    default: '1.0.0'
  },
  
  // تاريخ آخر تحديث
  lastModified: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// فهارس للبحث السريع
customCommandSchema.index({ guildId: 1, name: 1 }, { unique: true });
customCommandSchema.index({ guildId: 1, status: 1 });
customCommandSchema.index({ 'createdBy.userId': 1 });

// دوال مساعدة للأمر المخصص
customCommandSchema.methods.execute = async function(message, args) {
  try {
    // تحديث إحصائيات الاستخدام
    this.usage.totalUses += 1;
    this.usage.lastUsed = new Date();
    this.usage.usageHistory.push({
      userId: message.author.id,
      timestamp: new Date()
    });
    
    // الاحتفاظ بآخر 100 استخدام فقط
    if (this.usage.usageHistory.length > 100) {
      this.usage.usageHistory = this.usage.usageHistory.slice(-100);
    }
    
    await this.save();
    
    // تنفيذ البلوكات
    await this.executeBlocks(message, args);
    
  } catch (error) {
    // تسجيل الخطأ
    this.errorInfo.lastError = error.message;
    this.errorInfo.errorCount += 1;
    this.errorInfo.lastErrorAt = new Date();
    
    // إيقاف الأمر إذا كان هناك أخطاء كثيرة
    if (this.errorInfo.errorCount >= 10) {
      this.status = 'error';
    }
    
    await this.save();
    throw error;
  }
};

customCommandSchema.methods.executeBlocks = async function(message, args) {
  const { EmbedBuilder } = require('discord.js');
  
  // متغيرات السياق
  const context = {
    message,
    args,
    user: message.author,
    member: message.member,
    channel: message.channel,
    guild: message.guild,
    variables: new Map()
  };
  
  // تنفيذ البلوكات بالترتيب
  for (const block of this.blocks) {
    await this.executeBlock(block, context);
  }
};

customCommandSchema.methods.executeBlock = async function(block, context) {
  const { EmbedBuilder } = require('discord.js');
  
  switch (block.subtype) {
    case 'send_message':
      await context.message.channel.send(this.processText(block.settings.message || 'مرحباً!', context));
      break;
      
    case 'send_embed':
      const embed = new EmbedBuilder()
        .setTitle(this.processText(block.settings.title || 'عنوان', context))
        .setDescription(this.processText(block.settings.description || 'وصف', context))
        .setColor(block.settings.color || '#0099ff');
      
      await context.message.channel.send({ embeds: [embed] });
      break;
      
    case 'reply':
      await context.message.reply(this.processText(block.settings.message || 'تم!', context));
      break;
      
    case 'add_role':
      if (block.settings.roleId && context.member) {
        const role = context.guild.roles.cache.get(block.settings.roleId);
        if (role) {
          await context.member.roles.add(role);
        }
      }
      break;
      
    case 'remove_role':
      if (block.settings.roleId && context.member) {
        const role = context.guild.roles.cache.get(block.settings.roleId);
        if (role) {
          await context.member.roles.remove(role);
        }
      }
      break;
      
    case 'wait':
      const seconds = parseInt(block.settings.seconds) || 5;
      await new Promise(resolve => setTimeout(resolve, seconds * 1000));
      break;
      
    case 'random':
      const min = parseInt(block.settings.min) || 1;
      const max = parseInt(block.settings.max) || 10;
      const randomNumber = Math.floor(Math.random() * (max - min + 1)) + min;
      context.variables.set('randomNumber', randomNumber);
      break;
  }
};

customCommandSchema.methods.processText = function(text, context) {
  if (!text) return '';
  
  // استبدال المتغيرات
  return text
    .replace(/\{user\}/g, context.user.toString())
    .replace(/\{user\.username\}/g, context.user.username)
    .replace(/\{user\.tag\}/g, context.user.tag)
    .replace(/\{channel\}/g, context.channel.toString())
    .replace(/\{channel\.name\}/g, context.channel.name)
    .replace(/\{guild\}/g, context.guild.name)
    .replace(/\{guild\.name\}/g, context.guild.name)
    .replace(/\{guild\.memberCount\}/g, context.guild.memberCount.toString())
    .replace(/\{randomNumber\}/g, context.variables.get('randomNumber') || '0')
    .replace(/\{args\}/g, context.args.join(' '))
    .replace(/\{args\.0\}/g, context.args[0] || '')
    .replace(/\{args\.1\}/g, context.args[1] || '')
    .replace(/\{args\.2\}/g, context.args[2] || '');
};

customCommandSchema.methods.canExecute = function(member) {
  // فحص الحالة
  if (this.status !== 'active') {
    return { canExecute: false, reason: 'الأمر معطل حالياً' };
  }
  
  // فحص الصلاحيات
  if (this.settings.permissions.length > 0) {
    const hasPermission = this.settings.permissions.some(perm => 
      member.permissions.has(perm)
    );
    
    if (!hasPermission) {
      return { canExecute: false, reason: 'ليس لديك الصلاحية المطلوبة' };
    }
  }
  
  // فحص الرتب المطلوبة
  if (this.settings.requiredRoles.length > 0) {
    const hasRole = this.settings.requiredRoles.some(roleId => 
      member.roles.cache.has(roleId)
    );
    
    if (!hasRole) {
      return { canExecute: false, reason: 'تحتاج رتبة معينة لاستخدام هذا الأمر' };
    }
  }
  
  return { canExecute: true };
};

// دالة للحصول على الأمر أو إنشاؤه
customCommandSchema.statics.getOrCreate = async function(guildId, name) {
  let command = await this.findOne({ guildId, name });
  
  if (!command) {
    command = new this({
      guildId,
      name,
      description: 'أمر مخصص',
      blocks: [],
      generatedCode: '// أمر فارغ',
      createdBy: {
        userId: 'system',
        username: 'System',
        tag: 'System#0000'
      }
    });
    
    await command.save();
  }
  
  return command;
};

// دالة للبحث عن الأوامر
customCommandSchema.statics.findByGuild = function(guildId, options = {}) {
  const query = { guildId };
  
  if (options.status) {
    query.status = options.status;
  }
  
  if (options.createdBy) {
    query['createdBy.userId'] = options.createdBy;
  }
  
  return this.find(query).sort({ createdAt: -1 });
};

const CustomCommand = mongoose.model('CustomCommand', customCommandSchema);

module.exports = CustomCommand;
