<%- include('../partials/header') %>

<div class="main-content">
    <div class="content-header">
        <h1 class="page-title">
            <i class="fas fa-robot"></i>
            إعدادات البوت المتقدمة
        </h1>
        <p class="page-description">
            تحكم كامل في إعدادات البوت والخادم
        </p>
    </div>

    <!-- حالة البوت -->
    <div class="bot-status-card">
        <div class="bot-avatar">
            <img src="/static/images/bot-logo.svg" alt="Bot Avatar" id="currentBotAvatar">
            <div class="status-indicator online" id="botStatusIndicator"></div>
        </div>
        <div class="bot-info">
            <h3 id="currentBotName">CS Discord Bot</h3>
            <p id="currentBotStatus">🟢 متصل - يخدم 1,234 خادم</p>
            <p id="currentBotActivity">🎮 يلعب مع 45,678 مستخدم</p>
        </div>
        <div class="bot-actions">
            <button class="btn btn-success" onclick="restartBot()">
                <i class="fas fa-redo"></i>
                إعادة تشغيل
            </button>
            <button class="btn btn-warning" onclick="updateBot()">
                <i class="fas fa-download"></i>
                تحديث
            </button>
        </div>
    </div>

    <!-- بطاقات الإعدادات -->
    <div class="settings-grid">
        <!-- إعدادات البوت الأساسية -->
        <div class="card">
            <div class="card-header">
                <div class="card-icon">
                    <i class="fas fa-cog"></i>
                </div>
                <div>
                    <h3 class="card-title">الإعدادات الأساسية</h3>
                </div>
            </div>
            <div class="card-body">
                <div class="setting-item">
                    <label>اسم البوت</label>
                    <input type="text" class="form-control" id="botName" value="CS Discord Bot">
                </div>
                
                <div class="setting-item">
                    <label>صورة البوت (URL)</label>
                    <input type="url" class="form-control" id="botAvatar" placeholder="https://example.com/avatar.png">
                </div>
                
                <div class="setting-item">
                    <label>حالة البوت</label>
                    <select class="form-control" id="botPresence">
                        <option value="online">🟢 متصل</option>
                        <option value="idle">🟡 خامل</option>
                        <option value="dnd">🔴 مشغول</option>
                        <option value="invisible">⚫ غير مرئي</option>
                    </select>
                </div>
                
                <div class="setting-item">
                    <label>نشاط البوت</label>
                    <div class="activity-settings">
                        <select class="form-control" id="activityType">
                            <option value="PLAYING">🎮 يلعب</option>
                            <option value="LISTENING">🎵 يستمع إلى</option>
                            <option value="WATCHING">👀 يشاهد</option>
                            <option value="STREAMING">📺 يبث</option>
                        </select>
                        <input type="text" class="form-control" id="activityText" placeholder="نص النشاط">
                    </div>
                </div>
                
                <button class="btn btn-primary" onclick="saveBasicSettings()">
                    <i class="fas fa-save"></i>
                    حفظ الإعدادات
                </button>
            </div>
        </div>

        <!-- إعدادات الأمان -->
        <div class="card">
            <div class="card-header">
                <div class="card-icon">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <div>
                    <h3 class="card-title">إعدادات الأمان</h3>
                </div>
            </div>
            <div class="card-body">
                <div class="setting-item">
                    <label>وضع المطور</label>
                    <div class="toggle-switch">
                        <input type="checkbox" id="developerMode">
                        <label for="developerMode"></label>
                    </div>
                </div>
                
                <div class="setting-item">
                    <label>تسجيل الأخطاء المتقدم</label>
                    <div class="toggle-switch">
                        <input type="checkbox" id="advancedLogging" checked>
                        <label for="advancedLogging"></label>
                    </div>
                </div>
                
                <div class="setting-item">
                    <label>حماية من DDoS</label>
                    <div class="toggle-switch">
                        <input type="checkbox" id="ddosProtection" checked>
                        <label for="ddosProtection"></label>
                    </div>
                </div>
                
                <div class="setting-item">
                    <label>تشفير قاعدة البيانات</label>
                    <div class="toggle-switch">
                        <input type="checkbox" id="dbEncryption" checked>
                        <label for="dbEncryption"></label>
                    </div>
                </div>
                
                <button class="btn btn-primary" onclick="saveSecuritySettings()">
                    <i class="fas fa-shield-alt"></i>
                    حفظ إعدادات الأمان
                </button>
            </div>
        </div>

        <!-- إحصائيات النظام -->
        <div class="card">
            <div class="card-header">
                <div class="card-icon">
                    <i class="fas fa-chart-bar"></i>
                </div>
                <div>
                    <h3 class="card-title">إحصائيات النظام</h3>
                </div>
            </div>
            <div class="card-body">
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value" id="serverCount">1,234</div>
                        <div class="stat-label">خادم</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="userCount">45,678</div>
                        <div class="stat-label">مستخدم</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="commandsToday">12,345</div>
                        <div class="stat-label">أمر اليوم</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="uptime">99.9%</div>
                        <div class="stat-label">وقت التشغيل</div>
                    </div>
                </div>
                
                <div class="system-health">
                    <h6>صحة النظام</h6>
                    <div class="health-item">
                        <span>استخدام المعالج</span>
                        <div class="progress">
                            <div class="progress-bar" style="width: 45%">45%</div>
                        </div>
                    </div>
                    <div class="health-item">
                        <span>استخدام الذاكرة</span>
                        <div class="progress">
                            <div class="progress-bar" style="width: 67%">67%</div>
                        </div>
                    </div>
                    <div class="health-item">
                        <span>مساحة القرص</span>
                        <div class="progress">
                            <div class="progress-bar" style="width: 23%">23%</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- إدارة قاعدة البيانات -->
        <div class="card">
            <div class="card-header">
                <div class="card-icon">
                    <i class="fas fa-database"></i>
                </div>
                <div>
                    <h3 class="card-title">إدارة قاعدة البيانات</h3>
                </div>
            </div>
            <div class="card-body">
                <div class="db-actions">
                    <button class="btn btn-info" onclick="createBackup()">
                        <i class="fas fa-download"></i>
                        إنشاء نسخة احتياطية
                    </button>
                    
                    <button class="btn btn-warning" onclick="optimizeDatabase()">
                        <i class="fas fa-tools"></i>
                        تحسين قاعدة البيانات
                    </button>
                    
                    <button class="btn btn-success" onclick="testConnection()">
                        <i class="fas fa-plug"></i>
                        اختبار الاتصال
                    </button>
                    
                    <button class="btn btn-danger" onclick="clearCache()">
                        <i class="fas fa-trash"></i>
                        مسح الذاكرة المؤقتة
                    </button>
                </div>
                
                <div class="db-stats">
                    <div class="db-stat">
                        <span>حجم قاعدة البيانات</span>
                        <strong>2.3 GB</strong>
                    </div>
                    <div class="db-stat">
                        <span>عدد المجموعات</span>
                        <strong>12</strong>
                    </div>
                    <div class="db-stat">
                        <span>إجمالي الوثائق</span>
                        <strong>1,234,567</strong>
                    </div>
                    <div class="db-stat">
                        <span>آخر نسخة احتياطية</span>
                        <strong>منذ ساعتين</strong>
                    </div>
                </div>
            </div>
        </div>

        <!-- إعدادات الإشعارات -->
        <div class="card">
            <div class="card-header">
                <div class="card-icon">
                    <i class="fas fa-bell"></i>
                </div>
                <div>
                    <h3 class="card-title">إعدادات الإشعارات</h3>
                </div>
            </div>
            <div class="card-body">
                <div class="setting-item">
                    <label>إشعارات الأخطاء</label>
                    <div class="toggle-switch">
                        <input type="checkbox" id="errorNotifications" checked>
                        <label for="errorNotifications"></label>
                    </div>
                </div>
                
                <div class="setting-item">
                    <label>إشعارات الخوادم الجديدة</label>
                    <div class="toggle-switch">
                        <input type="checkbox" id="newServerNotifications" checked>
                        <label for="newServerNotifications"></label>
                    </div>
                </div>
                
                <div class="setting-item">
                    <label>تقارير يومية</label>
                    <div class="toggle-switch">
                        <input type="checkbox" id="dailyReports" checked>
                        <label for="dailyReports"></label>
                    </div>
                </div>
                
                <div class="setting-item">
                    <label>قناة الإشعارات</label>
                    <input type="text" class="form-control" id="notificationChannel" placeholder="معرف القناة">
                </div>
                
                <button class="btn btn-primary" onclick="saveNotificationSettings()">
                    <i class="fas fa-bell"></i>
                    حفظ إعدادات الإشعارات
                </button>
            </div>
        </div>

        <!-- أدوات المطور -->
        <div class="card">
            <div class="card-header">
                <div class="card-icon">
                    <i class="fas fa-code"></i>
                </div>
                <div>
                    <h3 class="card-title">أدوات المطور</h3>
                </div>
            </div>
            <div class="card-body">
                <div class="dev-tools">
                    <button class="btn btn-info" onclick="viewLogs()">
                        <i class="fas fa-file-alt"></i>
                        عرض السجلات
                    </button>
                    
                    <button class="btn btn-warning" onclick="runDiagnostics()">
                        <i class="fas fa-stethoscope"></i>
                        تشخيص النظام
                    </button>
                    
                    <button class="btn btn-success" onclick="testCommands()">
                        <i class="fas fa-terminal"></i>
                        اختبار الأوامر
                    </button>
                    
                    <button class="btn btn-secondary" onclick="exportConfig()">
                        <i class="fas fa-file-export"></i>
                        تصدير الإعدادات
                    </button>
                </div>
                
                <div class="console-output">
                    <h6>وحدة التحكم</h6>
                    <div class="console" id="console">
                        <div class="console-line">[INFO] البوت يعمل بشكل طبيعي</div>
                        <div class="console-line">[SUCCESS] تم تحديث الإعدادات</div>
                        <div class="console-line">[INFO] متصل بـ 1,234 خادم</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.bot-status-card {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    display: flex;
    align-items: center;
    gap: 20px;
    color: white;
}

.bot-avatar {
    position: relative;
}

.bot-avatar img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    border: 4px solid rgba(255, 255, 255, 0.3);
}

.status-indicator {
    position: absolute;
    bottom: 5px;
    right: 5px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 3px solid white;
}

.status-indicator.online {
    background: #43b581;
}

.bot-info {
    flex: 1;
}

.bot-info h3 {
    margin: 0 0 10px 0;
    font-size: 1.5rem;
}

.bot-info p {
    margin: 5px 0;
    opacity: 0.9;
}

.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
}

.setting-item {
    margin-bottom: 20px;
}

.setting-item label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: rgba(255, 255, 255, 0.9);
}

.activity-settings {
    display: flex;
    gap: 10px;
}

.activity-settings select {
    flex: 0 0 150px;
}

.activity-settings input {
    flex: 1;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 30px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-switch label {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 30px;
}

.toggle-switch label:before {
    position: absolute;
    content: "";
    height: 22px;
    width: 22px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

.toggle-switch input:checked + label {
    background-color: #667eea;
}

.toggle-switch input:checked + label:before {
    transform: translateX(30px);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin-bottom: 20px;
}

.stat-item {
    text-align: center;
    padding: 15px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: #667eea;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-top: 5px;
}

.system-health {
    margin-top: 20px;
}

.health-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.health-item span {
    flex: 0 0 150px;
}

.progress {
    flex: 1;
    height: 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    overflow: hidden;
    margin-left: 15px;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(45deg, #667eea, #764ba2);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.8rem;
    font-weight: bold;
}

.db-actions {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    margin-bottom: 20px;
}

.db-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.db-stat {
    display: flex;
    justify-content: space-between;
    padding: 10px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
}

.dev-tools {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    margin-bottom: 20px;
}

.console {
    background: #1a1a1a;
    border-radius: 8px;
    padding: 15px;
    height: 150px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
}

.console-line {
    color: #00ff00;
    margin-bottom: 5px;
}

@media (max-width: 768px) {
    .bot-status-card {
        flex-direction: column;
        text-align: center;
    }
    
    .settings-grid {
        grid-template-columns: 1fr;
    }
    
    .activity-settings {
        flex-direction: column;
    }
    
    .db-actions,
    .dev-tools {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<script src="/static/js/bot-settings.js"></script>

<%- include('../partials/footer') %>
