{"parent": "caee767b-c4ce-4448-9074-a8212c1e2643", "pid": 7507, "argv": ["/Users/<USER>/.nvm/versions/node/v18.16.0/bin/node", "/Users/<USER>/jdesboeufs/connect-mongo/node_modules/ava/lib/worker/subprocess.js"], "execArgv": [], "cwd": "/Users/<USER>/jdesboeufs/connect-mongo", "time": 1697308499298, "ppid": 7505, "coverageFilename": "/Users/<USER>/jdesboeufs/connect-mongo/.nyc_output/a2fabf40-3b97-45f1-94a2-9ed31c01ffd2.json", "externalId": "", "uuid": "a2fabf40-3b97-45f1-94a2-9ed31c01ffd2", "files": ["/Users/<USER>/jdesboeufs/connect-mongo/build/main/test/integration.spec.js", "/Users/<USER>/jdesboeufs/connect-mongo/build/main/index.js", "/Users/<USER>/jdesboeufs/connect-mongo/build/main/lib/MongoStore.js"]}