// ========================================
// 🛒 نموذج المتجر - CS Discord
// ========================================
// 📺 يوتيوب: https://www.youtube.com/@CS_Discord
// 💬 ديسكورد: https://discord.gg/yqTn2EwVsd
// ========================================

const mongoose = require('mongoose');

// نموذج عناصر المتجر
const shopItemSchema = new mongoose.Schema({
  // معرف الخادم
  guildId: {
    type: String,
    required: true,
    index: true
  },
  
  // معلومات العنصر
  name: {
    type: String,
    required: true,
    trim: true
  },
  
  description: {
    type: String,
    required: true
  },
  
  price: {
    type: Number,
    required: true,
    min: 1
  },
  
  // نوع العنصر
  type: {
    type: String,
    enum: ['role', 'item', 'boost', 'cosmetic', 'special'],
    required: true
  },
  
  // إعدادات العنصر
  settings: {
    // للرتب
    roleId: String,
    roleDuration: Number, // بالأيام، null = دائم
    
    // للعناصر
    emoji: String,
    rarity: {
      type: String,
      enum: ['common', 'rare', 'epic', 'legendary'],
      default: 'common'
    },
    
    // للتعزيزات
    boostType: {
      type: String,
      enum: ['xp', 'currency', 'luck']
    },
    boostAmount: Number,
    boostDuration: Number, // بالساعات
    
    // إعدادات عامة
    maxUses: Number, // عدد مرات الاستخدام، null = لا نهائي
    cooldown: Number, // فترة الانتظار بالساعات
    requiredLevel: Number,
    requiredRole: String
  },
  
  // حالة العنصر
  isActive: {
    type: Boolean,
    default: true
  },
  
  stock: {
    type: Number,
    default: -1 // -1 = لا نهائي
  },
  
  // إحصائيات
  stats: {
    totalSold: {
      type: Number,
      default: 0
    },
    totalRevenue: {
      type: Number,
      default: 0
    }
  }
}, {
  timestamps: true
});

// نموذج مشتريات المستخدمين
const userInventorySchema = new mongoose.Schema({
  userId: {
    type: String,
    required: true,
    index: true
  },
  
  guildId: {
    type: String,
    required: true,
    index: true
  },
  
  // العناصر المملوكة
  items: [{
    itemId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'ShopItem',
      required: true
    },
    
    quantity: {
      type: Number,
      default: 1
    },
    
    purchasedAt: {
      type: Date,
      default: Date.now
    },
    
    // للعناصر المؤقتة
    expiresAt: Date,
    
    // للعناصر القابلة للاستخدام
    usesLeft: Number,
    lastUsed: Date,
    
    // حالة العنصر
    isActive: {
      type: Boolean,
      default: true
    }
  }],
  
  // إحصائيات الشراء
  stats: {
    totalSpent: {
      type: Number,
      default: 0
    },
    totalItems: {
      type: Number,
      default: 0
    },
    favoriteCategory: String
  }
}, {
  timestamps: true
});

// فهارس للبحث السريع
shopItemSchema.index({ guildId: 1, type: 1 });
shopItemSchema.index({ guildId: 1, isActive: 1 });
userInventorySchema.index({ userId: 1, guildId: 1 });

// دوال مساعدة للمتجر
shopItemSchema.methods.canPurchase = function(user, userCurrency) {
  // فحص الرصيد
  if (userCurrency.balance < this.price) {
    return { canPurchase: false, reason: 'الرصيد غير كافي' };
  }
  
  // فحص المخزون
  if (this.stock !== -1 && this.stock <= 0) {
    return { canPurchase: false, reason: 'العنصر غير متوفر' };
  }
  
  // فحص المستوى المطلوب
  if (this.settings.requiredLevel && user.level < this.settings.requiredLevel) {
    return { canPurchase: false, reason: `يتطلب مستوى ${this.settings.requiredLevel}` };
  }
  
  // فحص الرتبة المطلوبة
  if (this.settings.requiredRole && !user.roles.includes(this.settings.requiredRole)) {
    return { canPurchase: false, reason: 'تحتاج رتبة معينة' };
  }
  
  return { canPurchase: true };
};

shopItemSchema.methods.purchase = async function(userId, guildId) {
  const Currency = mongoose.model('Currency');
  const UserInventory = mongoose.model('UserInventory');
  
  // الحصول على بيانات المستخدم
  const userCurrency = await Currency.getOrCreate(userId, guildId);
  let userInventory = await UserInventory.findOne({ userId, guildId });
  
  if (!userInventory) {
    userInventory = new UserInventory({ userId, guildId });
  }
  
  // فحص إمكانية الشراء
  const canPurchase = this.canPurchase({}, userCurrency);
  if (!canPurchase.canPurchase) {
    throw new Error(canPurchase.reason);
  }
  
  // خصم المبلغ
  await userCurrency.removeBalance(this.price, 'shop', `شراء ${this.name}`);
  
  // إضافة العنصر للمخزون
  const newItem = {
    itemId: this._id,
    quantity: 1,
    purchasedAt: new Date()
  };
  
  // إعداد انتهاء الصلاحية للعناصر المؤقتة
  if (this.settings.roleDuration) {
    newItem.expiresAt = new Date(Date.now() + this.settings.roleDuration * 24 * 60 * 60 * 1000);
  }
  
  // إعداد عدد الاستخدامات
  if (this.settings.maxUses) {
    newItem.usesLeft = this.settings.maxUses;
  }
  
  userInventory.items.push(newItem);
  
  // تحديث الإحصائيات
  userInventory.stats.totalSpent += this.price;
  userInventory.stats.totalItems += 1;
  
  this.stats.totalSold += 1;
  this.stats.totalRevenue += this.price;
  
  // تقليل المخزون
  if (this.stock !== -1) {
    this.stock -= 1;
  }
  
  await Promise.all([
    userInventory.save(),
    this.save()
  ]);
  
  return newItem;
};

// دوال مساعدة للمخزون
userInventorySchema.methods.hasItem = function(itemId) {
  return this.items.some(item => 
    item.itemId.toString() === itemId.toString() && 
    item.isActive &&
    (!item.expiresAt || item.expiresAt > new Date())
  );
};

userInventorySchema.methods.useItem = function(itemId) {
  const item = this.items.find(item => 
    item.itemId.toString() === itemId.toString() && 
    item.isActive &&
    (!item.expiresAt || item.expiresAt > new Date())
  );
  
  if (!item) {
    throw new Error('العنصر غير موجود أو منتهي الصلاحية');
  }
  
  if (item.usesLeft !== undefined) {
    if (item.usesLeft <= 0) {
      throw new Error('لا توجد استخدامات متبقية');
    }
    item.usesLeft -= 1;
    
    if (item.usesLeft === 0) {
      item.isActive = false;
    }
  }
  
  item.lastUsed = new Date();
  return this.save();
};

// دالة للحصول على العناصر النشطة
userInventorySchema.methods.getActiveItems = function() {
  return this.items.filter(item => 
    item.isActive &&
    (!item.expiresAt || item.expiresAt > new Date())
  );
};

// تنظيف العناصر المنتهية الصلاحية
userInventorySchema.methods.cleanExpiredItems = function() {
  const now = new Date();
  this.items.forEach(item => {
    if (item.expiresAt && item.expiresAt <= now) {
      item.isActive = false;
    }
  });
  return this.save();
};

const ShopItem = mongoose.model('ShopItem', shopItemSchema);
const UserInventory = mongoose.model('UserInventory', userInventorySchema);

module.exports = { ShopItem, UserInventory };
