#!/bin/bash

# تعيين الترميز
export LANG=en_US.UTF-8

# ألوان للنصوص
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo ""
echo "========================================"
echo "    🤖 لوحة تحكم بوت ديسكورد"
echo "    📺 CS Discord - يوتيوب"
echo "    💬 https://discord.gg/yqTn2EwVsd"
echo "========================================"
echo ""

# التحقق من وجود Node.js
if ! command -v node &> /dev/null; then
    echo -e "${RED}❌ خطأ: Node.js غير مثبت على النظام${NC}"
    echo "يرجى تحميل وتثبيت Node.js من: https://nodejs.org"
    exit 1
fi

# التحقق من وجود npm
if ! command -v npm &> /dev/null; then
    echo -e "${RED}❌ خطأ: npm غير مثبت على النظام${NC}"
    echo "يرجى تثبيت npm"
    exit 1
fi

# التحقق من وجود ملف package.json
if [ ! -f "package.json" ]; then
    echo -e "${RED}❌ خطأ: ملف package.json غير موجود${NC}"
    echo "تأكد من أنك في المجلد الصحيح للمشروع"
    exit 1
fi

# التحقق من وجود ملف config.js
if [ ! -f "config.js" ]; then
    echo -e "${RED}❌ خطأ: ملف config.js غير موجود${NC}"
    echo "تأكد من وجود ملف config.js في المجلد"
    exit 1
fi

echo -e "${BLUE}ℹ️  ملاحظة: جميع الإعدادات موجودة في ملف config.js${NC}"
echo -e "${YELLOW}🔧 لتعديل الإعدادات، افتح ملف config.js${NC}"
echo -e "${GREEN}📖 راجع ملف QUICK_START.md للتفاصيل${NC}"

# التحقق من وجود مجلد node_modules
if [ ! -d "node_modules" ]; then
    echo -e "${BLUE}📦 تثبيت المكتبات المطلوبة...${NC}"
    npm install
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ خطأ في تثبيت المكتبات${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ تم تثبيت المكتبات بنجاح${NC}"
    echo ""
fi

# عرض معلومات المشروع
echo -e "${BLUE}📋 معلومات المشروع:${NC}"
echo "   - الاسم: لوحة تحكم بوت ديسكورد"
echo "   - الإصدار: 1.0.0"
echo "   - المنفذ: 3000"
echo "   - الرابط: http://localhost:3000"
echo ""

# بدء التشغيل
echo -e "${GREEN}🚀 بدء تشغيل الخادم...${NC}"
echo -e "${BLUE}🛡️ حماية CS Discord مفعلة${NC}"
echo -e "${YELLOW}⚠️  يرجى عدم تعديل حقوق المطور${NC}"
echo ""
echo "للإيقاف اضغط Ctrl+C"
echo ""

# تشغيل التطبيق
npm start

# في حالة الخروج
echo ""
echo "تم إيقاف الخادم"
