// ========================================
// 🤖 لوحة تحكم بوت ديسكورد - CS Discord
// ========================================
// 📺 يوتيوب: https://www.youtube.com/@CS_Discord
// 💬 ديسكورد: https://discord.gg/yqTn2EwVsd
// ========================================

// استيراد الإعدادات المحمية
const { getSecureCredentials } = require('./core/security/credentials');

// 🛡️ حماية حقوق CS Discord (لا تحذف أو تعدل)
const CS_CREDITS = {
  youtube: 'https://www.youtube.com/@CS_Discord',
  discord: 'https://discord.gg/yqTn2EwVsd',
  author: 'CS Discord'
};

function validateCSCredits() {
  if (CS_CREDITS.youtube !== 'https://www.youtube.com/@CS_Discord' ||
      CS_CREDITS.discord !== 'https://discord.gg/yqTn2EwVsd') {
    console.error('❌ خطأ: تم تعديل حقوق CS Discord');
    console.error('📺 يوتيوب: https://www.youtube.com/@CS_Discord');
    console.error('💬 ديسكورد: https://discord.gg/yqTn2EwVsd');
    process.exit(1);
  }
}
validateCSCredits();

// الحصول على الإعدادات المحمية
const secureCredentials = getSecureCredentials();

module.exports = {
  // معلومات المطور (لا تحذف أو تعدل)
  credits: CS_CREDITS,

  // إعدادات البوت الأساسية (محمية)
  bot: {
    token: secureCredentials.botToken,
    clientId: secureCredentials.clientId,
    ownerId: secureCredentials.ownerId,
    prefix: '!'
  },

  // إعدادات OAuth2 (محمية)
  oauth: {
    clientId: secureCredentials.clientId,
    clientSecret: secureCredentials.clientSecret,
    redirectUri: 'http://localhost:3000/auth/discord/callback',
    scopes: ['identify', 'email', 'guilds']
  },

  // إعدادات قاعدة البيانات (محمية)
  database: {
    mongoUri: secureCredentials.mongoUri,
    options: {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000
    }
  },

  // إعدادات الخادم (محمية)
  server: {
    port: 3000,
    host: 'localhost',
    baseUrl: 'http://localhost:3000',
    sessionSecret: secureCredentials.sessionSecret
  },

  // إعدادات الأمان (محمية)
  security: {
    jwtSecret: secureCredentials.jwtSecret,
    rateLimitWindowMs: 15 * 60 * 1000,
    rateLimitMax: 100
  },
  
  // إعدادات اللوحة
  dashboard: {
    title: 'لوحة تحكم البوت',
    description: 'لوحة تحكم شاملة لإدارة بوت ديسكورد'
  },
  
  // رسائل النظام
  messages: {
    success: {
      settingsSaved: 'تم حفظ الإعدادات بنجاح! ✅'
    },
    errors: {
      serverError: 'حدث خطأ في الخادم! ❌',
      noPermission: 'ليس لديك صلاحية! 🚫'
    }
  },
  
  // إعدادات التطوير
  development: {
    debug: true,
    logLevel: 'info'
  }
};
