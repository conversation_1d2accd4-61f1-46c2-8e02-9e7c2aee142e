// ========================================
// 🤖 إعدادات لوحة تحكم بوت ديسكورد
// ========================================
//
// 📺 يوتيوب: https://www.youtube.com/@CS_Discord
// 💬 ديسكورد: https://discord.gg/yqTn2EwVsd
//
// ⚠️  تحذير: لا تشارك هذا الملف مع أي شخص!
// يحتوي على معلومات حساسة وسرية
// ========================================

// ========================================
// 🛡️ نظام حماية حقوق CS Discord
// ========================================
const CS_CREDITS = {
  youtube: 'https://www.youtube.com/@CS_Discord',
  discord: 'https://discord.gg/yqTn2EwVsd',
  author: 'CS Discord',
  version: '1.0.0',
  protected: true,
  hash: 'cs-discord-2024-protection'
};

// التحقق من حقوق CS (لا تحذف أو تعدل هذا الجزء)
function validateCSCredits() {
  const requiredCredits = {
    youtube: 'https://www.youtube.com/@CS_Discord',
    discord: 'https://discord.gg/yqTn2EwVsd',
    author: 'CS Discord'
  };

  if (CS_CREDITS.youtube !== requiredCredits.youtube ||
      CS_CREDITS.discord !== requiredCredits.discord ||
      CS_CREDITS.author !== requiredCredits.author) {
    console.error('❌ خطأ: تم تعديل حقوق CS Discord');
    console.error('📺 يوتيوب: https://www.youtube.com/@CS_Discord');
    console.error('💬 ديسكورد: https://discord.gg/yqTn2EwVsd');
    console.error('⚠️  يرجى عدم حذف أو تعديل حقوق المطور');
    process.exit(1);
  }
}

// تشغيل التحقق
validateCSCredits();

// ========================================
// 🤖 إعدادات البوت الأساسية
// ========================================
const BOT_CONFIG = {
  // توكن البوت من Discord Developer Portal
  token: 'MTM4MTk1MjIzNTg1MTc0NzM0OA.GvAHbj.HwjrInpTqp_WBPPA8-MhEcwHTEbI_YKdH134U4',

  // معرف التطبيق (Client ID)
  clientId: '1381952235851747348',

  // سر التطبيق (Client Secret)
  clientSecret: '3bqg_KevS_EYZKMo-6om3tMnBIEqR-ws',

  // البريفكس الافتراضي للأوامر
  prefix: '!',

  // معرف مالك البوت
  ownerId: '761218404833034251',

  // معرف خادم الدعم
  supportServerId: '1354795453769711787'
};

// ========================================
// 🗄️ إعدادات قاعدة البيانات
// ========================================
const DATABASE_CONFIG = {
  // رابط الاتصال بقاعدة بيانات MongoDB
  mongoUri: 'mongodb://Abdullah:<EMAIL>:27017/db_Abdullah?authSource=admin',

  // خيارات الاتصال
  options: {
    useNewUrlParser: true,
    useUnifiedTopology: true,
    maxPoolSize: 10,
    serverSelectionTimeoutMS: 5000,
    socketTimeoutMS: 45000
  }
};

// ========================================
// 🌐 إعدادات الخادم
// ========================================
const SERVER_CONFIG = {
  // المنفذ الذي سيعمل عليه الخادم
  port: 3000,

  // عنوان الخادم
  host: 'localhost',

  // الرابط الأساسي للتطبيق
  baseUrl: 'http://localhost:3000',

  // بيئة التشغيل
  nodeEnv: 'development'
};

// ========================================
// 🔒 إعدادات الأمان
// ========================================
const SECURITY_CONFIG = {
  // مفتاح سري للجلسات
  sessionSecret: 'dhsdgopfmkhklsfoghksoprthp',

  // مفتاح سري لـ JWT
  jwtSecret: 'hsflghpsofpgohsfghikospfghos',

  // إعدادات حد الطلبات
  rateLimitWindowMs: 15 * 60 * 1000, // 15 دقيقة
  rateLimitMax: 100 // 100 طلب لكل نافذة زمنية
};

// ========================================
// 🔐 إعدادات OAuth2
// ========================================
const OAUTH_CONFIG = {
  // رابط إعادة التوجيه بعد تسجيل الدخول
  redirectUri: 'http://localhost:3000/auth/discord/callback',

  // نطاقات الصلاحيات المطلوبة
  scopes: ['identify', 'email', 'guilds']
};

// ========================================
// 🛠️ إعدادات التطوير
// ========================================
const DEVELOPMENT_CONFIG = {
  // مستوى تسجيل الأحداث
  logLevel: 'info',

  // تفعيل وضع التطوير
  debug: true,

  // تفعيل البيانات التجريبية
  enableMockData: false
};

module.exports = {
  // معلومات المطور (لا تحذف أو تعدل)
  credits: CS_CREDITS,

  // إعدادات البوت
  bot: {
    token: BOT_CONFIG.token,
    clientId: BOT_CONFIG.clientId,
    ownerId: BOT_CONFIG.ownerId,
    supportServerId: BOT_CONFIG.supportServerId,
    prefix: BOT_CONFIG.prefix
  },

  // إعدادات OAuth2
  oauth: {
    clientId: BOT_CONFIG.clientId,
    clientSecret: BOT_CONFIG.clientSecret,
    redirectUri: OAUTH_CONFIG.redirectUri,
    scopes: OAUTH_CONFIG.scopes
  },

  // إعدادات قاعدة البيانات
  database: {
    mongoUri: DATABASE_CONFIG.mongoUri,
    options: DATABASE_CONFIG.options
  },

  // إعدادات الخادم
  server: {
    port: SERVER_CONFIG.port,
    host: SERVER_CONFIG.host,
    baseUrl: SERVER_CONFIG.baseUrl,
    sessionSecret: SECURITY_CONFIG.sessionSecret
  },

  // إعدادات الأمان
  security: {
    jwtSecret: SECURITY_CONFIG.jwtSecret,
    rateLimitWindowMs: SECURITY_CONFIG.rateLimitWindowMs,
    rateLimitMax: SECURITY_CONFIG.rateLimitMax
  },

  // إعدادات التطوير
  development: {
    debug: DEVELOPMENT_CONFIG.debug,
    logLevel: DEVELOPMENT_CONFIG.logLevel,
    enableMockData: DEVELOPMENT_CONFIG.enableMockData
  },

  // إعدادات اللوحة
  dashboard: {
    title: 'لوحة تحكم البوت',
    description: 'لوحة تحكم شاملة لإدارة بوت ديسكورد',
    supportedLanguages: ['ar', 'en'],
    defaultLanguage: 'ar',
    theme: {
      default: 'dark',
      options: ['light', 'dark']
    },
    itemsPerPage: 10,
    maxFileSize: 5 * 1024 * 1024, // 5MB
    allowedFileTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
  },

  // إعدادات الميزات
  features: {
    // نظام الإدارة والمودريشن
    moderation: {
      enabled: true,
      defaultMuteDuration: 10 * 60 * 1000, // 10 دقائق
      maxWarnings: 3,
      autoMuteAfterWarnings: true,
      logActions: true
    },

    // نظام التذاكر
    tickets: {
      enabled: true,
      maxTicketsPerUser: 3,
      autoCloseAfterHours: 24,
      saveTranscript: true,
      categoryPrefix: 'ticket-',
      supportRoles: []
    },

    // نظام الترحيب والمغادرة
    welcome: {
      enabled: true,
      defaultMessage: 'مرحباً {user} في {server}! 🎉',
      defaultLeaveMessage: 'وداعاً {user} 👋',
      autoRole: null,
      autoRoleDelay: 0
    },

    // الردود التلقائية
    autoResponses: {
      enabled: true,
      maxResponsesPerGuild: 50,
      cooldownMs: 5000,
      allowEveryone: false
    },

    // نظام الفلاتر والحماية
    filters: {
      enabled: true,
      antiSpam: true,
      antiInvite: true,
      antiLink: false,
      badWords: [],
      exemptRoles: [],
      actions: {
        warn: true,
        delete: true,
        mute: false,
        kick: false,
        ban: false
      }
    },

    // نظام اللوجات
    logging: {
      enabled: true,
      events: {
        memberJoin: true,
        memberLeave: true,
        messageEdit: true,
        messageDelete: true,
        moderation: true,
        tickets: true,
        roleChanges: true
      },
      archiveAfterDays: 60
    },

    // الرتب التلقائية
    autoRoles: {
      enabled: true,
      onJoin: null,
      delayMinutes: 0,
      basedOnActivity: false,
      basedOnLevel: false
    },

    // نظام المستويات والـ XP
    leveling: {
      enabled: true,
      xpPerMessage: 1,
      cooldownSeconds: 60,
      levelUpMessage: 'تهانينا {user}! وصلت للمستوى {level}! 🎉',
      rewards: [],
      ignoredChannels: [],
      ignoredRoles: []
    },

    // الإشعارات والبث المباشر
    notifications: {
      enabled: true,
      twitch: {
        enabled: false,
        clientId: process.env.TWITCH_CLIENT_ID || '',
        clientSecret: process.env.TWITCH_CLIENT_SECRET || ''
      },
      youtube: {
        enabled: false,
        apiKey: process.env.YOUTUBE_API_KEY || ''
      }
    },

    // النسخ الاحتياطي
    backup: {
      enabled: true,
      autoBackup: true,
      backupIntervalHours: 24,
      maxBackups: 7,
      includeMessages: false
    }
  },

  // رسائل النظام
  messages: {
    errors: {
      noPermission: 'ليس لديك صلاحية للوصول لهذه الصفحة',
      notFound: 'الصفحة غير موجودة',
      serverError: 'حدث خطأ في الخادم',
      invalidData: 'البيانات المدخلة غير صحيحة',
      rateLimited: 'تم تجاوز الحد المسموح من الطلبات'
    },
    success: {
      settingsSaved: 'تم حفظ الإعدادات بنجاح',
      actionCompleted: 'تم تنفيذ العملية بنجاح',
      backupCreated: 'تم إنشاء النسخة الاحتياطية بنجاح',
      dataImported: 'تم استيراد البيانات بنجاح'
    }
  },

  // ========================================
  // 🔧 إعدادات إضافية مهمة
  // ========================================

  // إعدادات النسخ الاحتياطي
  backup: {
    enabled: true,
    autoBackup: false,
    backupInterval: 24 * 60 * 60 * 1000, // 24 ساعة
    maxBackups: 10,
    includeUserData: false
  },

  // إعدادات الإشعارات
  notifications: {
    enabled: true,
    discordWebhook: null,
    emailNotifications: false,
    logImportantEvents: true
  },

  // إعدادات الأداء
  performance: {
    cacheEnabled: true,
    cacheTTL: 5 * 60 * 1000, // 5 دقائق
    maxConcurrentRequests: 100,
    requestTimeout: 30000 // 30 ثانية
  },

  // إعدادات الصيانة
  maintenance: {
    enabled: false,
    message: 'الموقع تحت الصيانة، يرجى المحاولة لاحقاً',
    allowedIPs: [],
    estimatedTime: '30 دقيقة'
  },

  // ========================================
  // 🛡️ إعدادات حماية CS Discord الإضافية
  // ========================================
  csProtection: {
    enabled: true,
    strictMode: true,
    checkInterval: 5 * 60 * 1000, // 5 دقائق
    maxViolations: 3,
    autoDisable: true,
    logViolations: true,
    requiredCredits: {
      youtube: 'https://www.youtube.com/@CS_Discord',
      discord: 'https://discord.gg/yqTn2EwVsd',
      author: 'CS Discord'
    }
  }
};
