require('dotenv').config();

// ========================================
// 🛡️ نظام حماية حقوق CS
// ========================================
const CS_CREDITS = {
  youtube: 'https://www.youtube.com/@CS_Discord',
  discord: 'https://discord.gg/yqTn2EwVsd',
  author: 'CS Discord',
  version: '1.0.0'
};

// التحقق من حقوق CS (لا تحذف أو تعدل هذا الجزء)
function validateCSCredits() {
  const requiredCredits = {
    youtube: 'https://www.youtube.com/@CS_Discord',
    discord: 'https://discord.gg/yqTn2EwVsd'
  };

  if (CS_CREDITS.youtube !== requiredCredits.youtube ||
      CS_CREDITS.discord !== requiredCredits.discord) {
    console.error('❌ خطأ: تم تعديل حقوق CS Discord');
    console.error('📺 يوتيوب: https://www.youtube.com/@CS_Discord');
    console.error('💬 ديسكورد: https://discord.gg/yqTn2EwVsd');
    console.error('⚠️  يرجى عدم حذف أو تعديل حقوق المطور');
    process.exit(1);
  }
}

// تشغيل التحقق
validateCSCredits();

module.exports = {
  // معلومات المطور (لا تحذف أو تعدل)
  credits: CS_CREDITS,
  // إعدادات البوت الأساسية
  bot: {
    token: process.env.BOT_TOKEN || '',
    clientId: process.env.CLIENT_ID || '',
    clientSecret: process.env.CLIENT_SECRET || '',
    prefix: process.env.PREFIX || '!',
    ownerId: process.env.OWNER_ID || '',
    supportServerId: process.env.SUPPORT_SERVER_ID || ''
  },

  // إعدادات قاعدة البيانات
  database: {
    mongoUri: process.env.MONGO_URI || 'mongodb://localhost:27017/discord-bot-dashboard',
    options: {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000
    }
  },

  // إعدادات الخادم
  server: {
    port: process.env.PORT || 3000,
    host: process.env.HOST || 'localhost',
    baseUrl: process.env.BASE_URL || 'http://localhost:3000',
    sessionSecret: process.env.SESSION_SECRET || 'your-super-secret-session-key-here'
  },

  // إعدادات OAuth2 للديسكورد
  oauth: {
    clientId: process.env.CLIENT_ID || '',
    clientSecret: process.env.CLIENT_SECRET || '',
    redirectUri: process.env.REDIRECT_URI || 'http://localhost:3000/auth/discord/callback',
    scopes: ['identify', 'guilds', 'guilds.join']
  },

  // إعدادات الأمان
  security: {
    rateLimitWindowMs: 15 * 60 * 1000, // 15 دقيقة
    rateLimitMax: 100, // حد أقصى 100 طلب لكل نافذة زمنية
    jwtSecret: process.env.JWT_SECRET || 'your-jwt-secret-key',
    jwtExpiration: '7d',
    bcryptRounds: 12
  },

  // إعدادات اللوحة
  dashboard: {
    title: 'لوحة تحكم البوت',
    description: 'لوحة تحكم شاملة لإدارة بوت ديسكورد',
    supportedLanguages: ['ar', 'en'],
    defaultLanguage: 'ar',
    theme: {
      default: 'dark',
      options: ['light', 'dark']
    },
    itemsPerPage: 10,
    maxFileSize: 5 * 1024 * 1024, // 5MB
    allowedFileTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
  },

  // إعدادات الميزات
  features: {
    // نظام الإدارة والمودريشن
    moderation: {
      enabled: true,
      defaultMuteDuration: 10 * 60 * 1000, // 10 دقائق
      maxWarnings: 3,
      autoMuteAfterWarnings: true,
      logActions: true
    },

    // نظام التذاكر
    tickets: {
      enabled: true,
      maxTicketsPerUser: 3,
      autoCloseAfterHours: 24,
      saveTranscript: true,
      categoryPrefix: 'ticket-',
      supportRoles: []
    },

    // نظام الترحيب والمغادرة
    welcome: {
      enabled: true,
      defaultMessage: 'مرحباً {user} في {server}! 🎉',
      defaultLeaveMessage: 'وداعاً {user} 👋',
      autoRole: null,
      autoRoleDelay: 0
    },

    // الردود التلقائية
    autoResponses: {
      enabled: true,
      maxResponsesPerGuild: 50,
      cooldownMs: 5000,
      allowEveryone: false
    },

    // نظام الفلاتر والحماية
    filters: {
      enabled: true,
      antiSpam: true,
      antiInvite: true,
      antiLink: false,
      badWords: [],
      exemptRoles: [],
      actions: {
        warn: true,
        delete: true,
        mute: false,
        kick: false,
        ban: false
      }
    },

    // نظام اللوجات
    logging: {
      enabled: true,
      events: {
        memberJoin: true,
        memberLeave: true,
        messageEdit: true,
        messageDelete: true,
        moderation: true,
        tickets: true,
        roleChanges: true
      },
      archiveAfterDays: 60
    },

    // الرتب التلقائية
    autoRoles: {
      enabled: true,
      onJoin: null,
      delayMinutes: 0,
      basedOnActivity: false,
      basedOnLevel: false
    },

    // نظام المستويات والـ XP
    leveling: {
      enabled: true,
      xpPerMessage: 1,
      cooldownSeconds: 60,
      levelUpMessage: 'تهانينا {user}! وصلت للمستوى {level}! 🎉',
      rewards: [],
      ignoredChannels: [],
      ignoredRoles: []
    },

    // الإشعارات والبث المباشر
    notifications: {
      enabled: true,
      twitch: {
        enabled: false,
        clientId: process.env.TWITCH_CLIENT_ID || '',
        clientSecret: process.env.TWITCH_CLIENT_SECRET || ''
      },
      youtube: {
        enabled: false,
        apiKey: process.env.YOUTUBE_API_KEY || ''
      }
    },

    // النسخ الاحتياطي
    backup: {
      enabled: true,
      autoBackup: true,
      backupIntervalHours: 24,
      maxBackups: 7,
      includeMessages: false
    }
  },

  // رسائل النظام
  messages: {
    errors: {
      noPermission: 'ليس لديك صلاحية للوصول لهذه الصفحة',
      notFound: 'الصفحة غير موجودة',
      serverError: 'حدث خطأ في الخادم',
      invalidData: 'البيانات المدخلة غير صحيحة',
      rateLimited: 'تم تجاوز الحد المسموح من الطلبات'
    },
    success: {
      settingsSaved: 'تم حفظ الإعدادات بنجاح',
      actionCompleted: 'تم تنفيذ العملية بنجاح',
      backupCreated: 'تم إنشاء النسخة الاحتياطية بنجاح',
      dataImported: 'تم استيراد البيانات بنجاح'
    }
  },

  // إعدادات التطوير
  development: {
    debug: process.env.NODE_ENV !== 'production',
    logLevel: process.env.LOG_LEVEL || 'info',
    enableMockData: process.env.ENABLE_MOCK_DATA === 'true'
  }
};
