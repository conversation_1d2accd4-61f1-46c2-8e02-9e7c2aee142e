// ========================================
// 🤖 لوحة تحكم بوت ديسكورد - CS Discord
// ========================================
// 📺 يوتيوب: https://www.youtube.com/@CS_Discord
// 💬 ديسكورد: https://discord.gg/yqTn2EwVsd
// ========================================

// 🛡️ حماية حقوق CS Discord (لا تحذف أو تعدل)
const CS_CREDITS = {
  youtube: 'https://www.youtube.com/@CS_Discord',
  discord: 'https://discord.gg/yqTn2EwVsd',
  author: 'CS Discord'
};

function validateCSCredits() {
  if (CS_CREDITS.youtube !== 'https://www.youtube.com/@CS_Discord' || 
      CS_CREDITS.discord !== 'https://discord.gg/yqTn2EwVsd') {
    console.error('❌ خطأ: تم تعديل حقوق CS Discord');
    console.error('📺 يوتيوب: https://www.youtube.com/@CS_Discord');
    console.error('💬 ديسكورد: https://discord.gg/yqTn2EwVsd');
    process.exit(1);
  }
}
validateCSCredits();

module.exports = {
  // معلومات المطور (لا تحذف أو تعدل)
  credits: CS_CREDITS,
  
  // إعدادات البوت الأساسية
  bot: {
    token: 'MTM4MTk1MjIzNTg1MTc0NzM0OA.GvAHbj.HwjrInpTqp_WBPPA8-MhEcwHTEbI_YKdH134U4',
    clientId: '1381952235851747348',
    ownerId: '761218404833034251',
    prefix: '!'
  },
  
  // إعدادات OAuth2
  oauth: {
    clientId: '1381952235851747348',
    clientSecret: '3bqg_KevS_EYZKMo-6om3tMnBIEqR-ws',
    redirectUri: 'http://localhost:3000/auth/discord/callback',
    scopes: ['identify', 'email', 'guilds']
  },
  
  // إعدادات قاعدة البيانات
  database: {
    mongoUri: 'mongodb+srv://wick-studio5:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0',
    options: {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000
    }
  },
  
  // إعدادات الخادم
  server: {
    port: 3000,
    host: 'localhost',
    baseUrl: 'http://localhost:3000',
    sessionSecret: 'dhsdgopfmkhklsfoghksoprthp'
  },
  
  // إعدادات الأمان
  security: {
    jwtSecret: 'hsflghpsofpgohsfghikospfghos',
    rateLimitWindowMs: 15 * 60 * 1000,
    rateLimitMax: 100
  },
  
  // إعدادات اللوحة
  dashboard: {
    title: 'لوحة تحكم البوت',
    description: 'لوحة تحكم شاملة لإدارة بوت ديسكورد'
  },
  
  // رسائل النظام
  messages: {
    success: {
      settingsSaved: 'تم حفظ الإعدادات بنجاح! ✅'
    },
    errors: {
      serverError: 'حدث خطأ في الخادم! ❌',
      noPermission: 'ليس لديك صلاحية! 🚫'
    }
  },
  
  // إعدادات التطوير
  development: {
    debug: true,
    logLevel: 'info'
  }
};
