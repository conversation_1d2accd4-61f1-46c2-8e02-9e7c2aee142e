// ========================================
// 👑 نموذج Premium - CS Discord
// ========================================
// 📺 يوتيوب: https://www.youtube.com/@CS_Discord
// 💬 ديسكورد: https://discord.gg/yqTn2EwVsd
// ========================================

const mongoose = require('mongoose');

// نموذج Premium للمستخدمين
const premiumSchema = new mongoose.Schema({
  // معرف المستخدم
  userId: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  
  // معرف الخادم
  guildId: {
    type: String,
    required: true,
    index: true
  },
  
  // معلومات المستخدم
  userInfo: {
    username: String,
    discriminator: String,
    avatar: String,
    tag: String
  },
  
  // نوع Premium
  premiumType: {
    type: String,
    enum: ['basic', 'premium', 'vip', 'ultimate'],
    default: 'basic'
  },
  
  // حالة Premium
  isActive: {
    type: Boolean,
    default: true
  },
  
  // تاريخ البداية
  startDate: {
    type: Date,
    default: Date.now
  },
  
  // تاريخ الانتهاء
  endDate: {
    type: Date,
    required: true
  },
  
  // المدة بالأيام
  durationDays: {
    type: Number,
    required: true
  },
  
  // الميزات المتاحة
  features: {
    // ميزات عامة
    customCommands: {
      type: Boolean,
      default: false
    },
    
    advancedModeration: {
      type: Boolean,
      default: false
    },
    
    customEmbeds: {
      type: Boolean,
      default: false
    },
    
    autoRoles: {
      type: Boolean,
      default: false
    },
    
    welcomeMessages: {
      type: Boolean,
      default: false
    },
    
    // ميزات الاقتصاد
    economyBoost: {
      multiplier: {
        type: Number,
        default: 1
      },
      enabled: {
        type: Boolean,
        default: false
      }
    },
    
    // ميزات خاصة
    customBot: {
      name: String,
      avatar: String,
      status: String,
      enabled: {
        type: Boolean,
        default: false
      }
    },
    
    // حدود مرفوعة
    limits: {
      customCommands: {
        type: Number,
        default: 10
      },
      autoResponses: {
        type: Number,
        default: 20
      },
      tickets: {
        type: Number,
        default: 50
      }
    }
  },
  
  // معلومات الدفع
  payment: {
    method: String,
    amount: Number,
    currency: String,
    transactionId: String,
    paidBy: {
      userId: String,
      username: String
    }
  },
  
  // تاريخ التجديد
  renewalHistory: [{
    date: {
      type: Date,
      default: Date.now
    },
    duration: Number,
    renewedBy: {
      userId: String,
      username: String
    },
    reason: String
  }],
  
  // إحصائيات الاستخدام
  usage: {
    commandsUsed: {
      type: Number,
      default: 0
    },
    
    featuresUsed: [{
      feature: String,
      count: {
        type: Number,
        default: 0
      },
      lastUsed: Date
    }],
    
    lastActivity: {
      type: Date,
      default: Date.now
    }
  },
  
  // ملاحظات المشرف
  adminNotes: [{
    note: String,
    addedBy: {
      userId: String,
      username: String
    },
    addedAt: {
      type: Date,
      default: Date.now
    }
  }],
  
  // حالة التنبيهات
  notifications: {
    expiryWarning: {
      sent: {
        type: Boolean,
        default: false
      },
      sentAt: Date
    },
    
    expired: {
      sent: {
        type: Boolean,
        default: false
      },
      sentAt: Date
    }
  }
}, {
  timestamps: true
});

// فهارس للبحث السريع
premiumSchema.index({ userId: 1, guildId: 1 });
premiumSchema.index({ endDate: 1 });
premiumSchema.index({ isActive: 1 });
premiumSchema.index({ premiumType: 1 });

// دوال مساعدة
premiumSchema.methods.isExpired = function() {
  return new Date() > this.endDate;
};

premiumSchema.methods.getDaysRemaining = function() {
  const now = new Date();
  const diff = this.endDate - now;
  return Math.max(0, Math.ceil(diff / (1000 * 60 * 60 * 24)));
};

premiumSchema.methods.getHoursRemaining = function() {
  const now = new Date();
  const diff = this.endDate - now;
  return Math.max(0, Math.ceil(diff / (1000 * 60 * 60)));
};

premiumSchema.methods.extend = function(days, extendedBy) {
  this.endDate = new Date(this.endDate.getTime() + (days * 24 * 60 * 60 * 1000));
  this.durationDays += days;
  
  this.renewalHistory.push({
    date: new Date(),
    duration: days,
    renewedBy: extendedBy,
    reason: 'Extension'
  });
  
  return this;
};

premiumSchema.methods.activate = function() {
  this.isActive = true;
  this.usage.lastActivity = new Date();
  return this;
};

premiumSchema.methods.deactivate = function() {
  this.isActive = false;
  return this;
};

premiumSchema.methods.hasFeature = function(featureName) {
  if (!this.isActive || this.isExpired()) {
    return false;
  }
  
  const featurePath = featureName.split('.');
  let feature = this.features;
  
  for (const path of featurePath) {
    if (feature[path] === undefined) {
      return false;
    }
    feature = feature[path];
  }
  
  return feature === true;
};

premiumSchema.methods.incrementUsage = function(featureName) {
  this.usage.commandsUsed += 1;
  this.usage.lastActivity = new Date();
  
  const existingFeature = this.usage.featuresUsed.find(f => f.feature === featureName);
  
  if (existingFeature) {
    existingFeature.count += 1;
    existingFeature.lastUsed = new Date();
  } else {
    this.usage.featuresUsed.push({
      feature: featureName,
      count: 1,
      lastUsed: new Date()
    });
  }
  
  return this;
};

// دوال ثابتة
premiumSchema.statics.findActiveByUser = function(userId, guildId) {
  return this.findOne({
    userId,
    guildId,
    isActive: true,
    endDate: { $gt: new Date() }
  });
};

premiumSchema.statics.findExpiringSoon = function(days = 3) {
  const futureDate = new Date();
  futureDate.setDate(futureDate.getDate() + days);
  
  return this.find({
    isActive: true,
    endDate: { $lte: futureDate, $gt: new Date() },
    'notifications.expiryWarning.sent': false
  });
};

premiumSchema.statics.findExpired = function() {
  return this.find({
    isActive: true,
    endDate: { $lt: new Date() }
  });
};

premiumSchema.statics.createPremium = function(userId, guildId, type, days, grantedBy) {
  const endDate = new Date();
  endDate.setDate(endDate.getDate() + days);
  
  const features = this.getFeaturesByType(type);
  
  return new this({
    userId,
    guildId,
    premiumType: type,
    endDate,
    durationDays: days,
    features,
    renewalHistory: [{
      date: new Date(),
      duration: days,
      renewedBy: grantedBy,
      reason: 'Initial grant'
    }]
  });
};

premiumSchema.statics.getFeaturesByType = function(type) {
  const baseFeatures = {
    customCommands: false,
    advancedModeration: false,
    customEmbeds: false,
    autoRoles: false,
    welcomeMessages: false,
    economyBoost: {
      multiplier: 1,
      enabled: false
    },
    customBot: {
      enabled: false
    },
    limits: {
      customCommands: 5,
      autoResponses: 10,
      tickets: 20
    }
  };
  
  switch (type) {
    case 'basic':
      return {
        ...baseFeatures,
        customCommands: true,
        limits: {
          customCommands: 10,
          autoResponses: 15,
          tickets: 30
        }
      };
      
    case 'premium':
      return {
        ...baseFeatures,
        customCommands: true,
        advancedModeration: true,
        customEmbeds: true,
        economyBoost: {
          multiplier: 1.5,
          enabled: true
        },
        limits: {
          customCommands: 25,
          autoResponses: 30,
          tickets: 50
        }
      };
      
    case 'vip':
      return {
        ...baseFeatures,
        customCommands: true,
        advancedModeration: true,
        customEmbeds: true,
        autoRoles: true,
        welcomeMessages: true,
        economyBoost: {
          multiplier: 2,
          enabled: true
        },
        limits: {
          customCommands: 50,
          autoResponses: 50,
          tickets: 100
        }
      };
      
    case 'ultimate':
      return {
        ...baseFeatures,
        customCommands: true,
        advancedModeration: true,
        customEmbeds: true,
        autoRoles: true,
        welcomeMessages: true,
        economyBoost: {
          multiplier: 3,
          enabled: true
        },
        customBot: {
          enabled: true
        },
        limits: {
          customCommands: 100,
          autoResponses: 100,
          tickets: 200
        }
      };
      
    default:
      return baseFeatures;
  }
};

const Premium = mongoose.model('Premium', premiumSchema);

module.exports = Premium;
