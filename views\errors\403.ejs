<%- include('../partials/header') %>

<div class="error-container">
    <div class="error-content">
        <div class="error-animation">
            <div class="floating-bot">
                <img src="/static/images/bot-logo.svg" alt="CS Discord Bot" class="bot-image">
                <div class="speech-bubble">
                    <p>عذراً! لا تملك الصلاحية 🔒</p>
                </div>
            </div>
        </div>
        
        <div class="error-info">
            <h1 class="error-code">403</h1>
            <h2 class="error-title">الوصول مرفوض</h2>
            <p class="error-description">
                هذه الصفحة متاحة للمالك فقط! 👑
            </p>
            
            <div class="error-actions">
                <a href="/dashboard" class="btn btn-primary">
                    <i class="fas fa-home"></i>
                    العودة للرئيسية
                </a>
                <a href="javascript:history.back()" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    الصفحة السابقة
                </a>
            </div>
        </div>
    </div>
</div>

<style>
.error-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

.error-content {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 40px;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.2);
    max-width: 600px;
    width: 100%;
}

.floating-bot {
    animation: float 3s ease-in-out infinite;
}

.bot-image {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    border: 4px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.speech-bubble {
    position: absolute;
    top: -20px;
    right: -50px;
    background: white;
    color: #333;
    padding: 10px 15px;
    border-radius: 15px;
    font-size: 14px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.error-code {
    font-size: 6rem;
    font-weight: bold;
    color: white;
    text-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    margin: 0;
}

.error-title {
    font-size: 2rem;
    color: white;
    margin: 10px 0;
}

.error-description {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 30px;
}

.error-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn {
    padding: 12px 24px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: bold;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}
</style>

<%- include('../partials/footer') %>
