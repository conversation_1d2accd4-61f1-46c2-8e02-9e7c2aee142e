// ========================================
// 👑 أمر إدارة Premium - CS Discord
// ========================================
// 📺 يوتيوب: https://www.youtube.com/@CS_Discord
// 💬 ديسكورد: https://discord.gg/yqTn2EwVsd
// ========================================

const { EmbedBuilder, PermissionFlagsBits } = require('discord.js');
const { Premium } = require('../../models');

module.exports = {
  name: 'premium',
  aliases: ['vip', 'مميز'],
  description: 'إدارة اشتراكات Premium (للمالك فقط)',
  usage: '<grant/revoke/check/list> [المستخدم] [النوع] [المدة]',
  category: 'owner',
  ownerOnly: true,
  cooldown: 0,
  
  async execute(message, args) {
    try {
      const config = require('../../config');

      // التحقق من أن المستخدم هو المالك
      if (message.author.id !== config.bot.ownerId) {
        return await message.reply('❌ هذا الأمر متاح للمالك فقط!');
      }
      
      if (!args.length) {
        return await showPremiumHelp(message);
      }
      
      const action = args[0].toLowerCase();
      
      switch (action) {
        case 'grant':
        case 'منح':
          await grantPremium(message, args.slice(1));
          break;
          
        case 'revoke':
        case 'إلغاء':
          await revokePremium(message, args.slice(1));
          break;
          
        case 'check':
        case 'فحص':
          await checkPremium(message, args.slice(1));
          break;
          
        case 'list':
        case 'قائمة':
          await listPremium(message, args.slice(1));
          break;
          
        case 'extend':
        case 'تمديد':
          await extendPremium(message, args.slice(1));
          break;
          
        case 'stats':
        case 'إحصائيات':
          await showPremiumStats(message);
          break;
          
        default:
          await showPremiumHelp(message);
      }
      
    } catch (error) {
      console.error('خطأ في أمر Premium:', error);
      await message.reply('❌ حدث خطأ أثناء تنفيذ الأمر!');
    }
  }
};

// عرض مساعدة Premium
async function showPremiumHelp(message) {
  const embed = new EmbedBuilder()
    .setColor('#ffd700')
    .setTitle('👑 أوامر إدارة Premium')
    .setDescription('إدارة اشتراكات Premium للأعضاء')
    .addFields(
      {
        name: '📝 الأوامر المتاحة',
        value: `
        \`premium grant <@المستخدم> <النوع> <المدة>\` - منح Premium
        \`premium revoke <@المستخدم>\` - إلغاء Premium
        \`premium check <@المستخدم>\` - فحص حالة Premium
        \`premium extend <@المستخدم> <المدة>\` - تمديد Premium
        \`premium list [النوع]\` - عرض قائمة المستخدمين
        \`premium stats\` - عرض الإحصائيات
        `,
        inline: false
      },
      {
        name: '🎭 أنواع Premium',
        value: `
        \`basic\` 🥉 - ميزات أساسية
        \`premium\` 🥈 - ميزات متقدمة
        \`vip\` 🥇 - ميزات VIP
        \`ultimate\` 💎 - جميع الميزات
        `,
        inline: true
      },
      {
        name: '⏰ أمثلة المدة',
        value: `
        \`7d\` - 7 أيام
        \`1m\` - شهر واحد
        \`3m\` - 3 أشهر
        \`1y\` - سنة واحدة
        `,
        inline: true
      }
    )
    .setFooter({
      text: 'CS Discord Premium System',
      iconURL: message.client.user.displayAvatarURL()
    })
    .setTimestamp();
  
  await message.reply({ embeds: [embed] });
}

// منح Premium
async function grantPremium(message, args) {
  if (args.length < 3) {
    return await message.reply('❌ استخدام: `premium grant <@المستخدم> <النوع> <المدة>`');
  }
  
  const targetUser = message.mentions.users.first();
  if (!targetUser) {
    return await message.reply('❌ يجب ذكر المستخدم!');
  }
  
  const type = args[1].toLowerCase();
  const validTypes = ['basic', 'premium', 'vip', 'ultimate'];
  if (!validTypes.includes(type)) {
    return await message.reply('❌ نوع Premium غير صحيح! الأنواع المتاحة: basic, premium, vip, ultimate');
  }
  
  const duration = parseDuration(args[2]);
  if (!duration) {
    return await message.reply('❌ مدة غير صحيحة! مثال: 7d, 1m, 3m, 1y');
  }
  
  try {
    // فحص إذا كان لديه Premium بالفعل
    let existingPremium = await Premium.findActiveByUser(targetUser.id, message.guild.id);
    
    if (existingPremium) {
      return await message.reply(`❌ ${targetUser.username} لديه Premium بالفعل! استخدم \`extend\` للتمديد.`);
    }
    
    // إنشاء Premium جديد
    const newPremium = Premium.createPremium(
      targetUser.id,
      message.guild.id,
      type,
      duration,
      {
        userId: message.author.id,
        username: message.author.username
      }
    );
    
    // إضافة معلومات المستخدم
    newPremium.userInfo = {
      username: targetUser.username,
      discriminator: targetUser.discriminator,
      avatar: targetUser.displayAvatarURL(),
      tag: targetUser.tag
    };
    
    await newPremium.save();
    
    // إنشاء embed النجاح
    const successEmbed = new EmbedBuilder()
      .setColor('#00ff00')
      .setTitle('✅ تم منح Premium بنجاح!')
      .setThumbnail(targetUser.displayAvatarURL())
      .addFields(
        {
          name: '👤 المستخدم',
          value: `${targetUser} (${targetUser.tag})`,
          inline: true
        },
        {
          name: '🎭 النوع',
          value: getPremiumTypeDisplay(type),
          inline: true
        },
        {
          name: '⏰ المدة',
          value: `${duration} يوم`,
          inline: true
        },
        {
          name: '📅 تاريخ الانتهاء',
          value: `<t:${Math.floor(newPremium.endDate.getTime() / 1000)}:F>`,
          inline: false
        },
        {
          name: '🎁 الميزات المتضمنة',
          value: getPremiumFeatures(type),
          inline: false
        }
      )
      .setFooter({
        text: `منح بواسطة ${message.author.tag}`,
        iconURL: message.author.displayAvatarURL()
      })
      .setTimestamp();
    
    await message.reply({ embeds: [successEmbed] });
    
    // إرسال رسالة خاصة للمستخدم
    try {
      const dmEmbed = new EmbedBuilder()
        .setColor('#ffd700')
        .setTitle('🎉 تهانينا! حصلت على Premium!')
        .setDescription(`تم منحك اشتراك **${getPremiumTypeDisplay(type)}** في خادم **${message.guild.name}**`)
        .addFields(
          {
            name: '⏰ صالح حتى',
            value: `<t:${Math.floor(newPremium.endDate.getTime() / 1000)}:F>`,
            inline: false
          },
          {
            name: '🎁 الميزات الجديدة',
            value: getPremiumFeatures(type),
            inline: false
          }
        )
        .setFooter({
          text: 'CS Discord Premium',
          iconURL: message.client.user.displayAvatarURL()
        })
        .setTimestamp();
      
      await targetUser.send({ embeds: [dmEmbed] });
    } catch (error) {
      // تجاهل خطأ الرسائل الخاصة
    }
    
  } catch (error) {
    console.error('خطأ في منح Premium:', error);
    await message.reply('❌ حدث خطأ أثناء منح Premium!');
  }
}

// إلغاء Premium
async function revokePremium(message, args) {
  if (!args.length) {
    return await message.reply('❌ استخدام: `premium revoke <@المستخدم>`');
  }
  
  const targetUser = message.mentions.users.first();
  if (!targetUser) {
    return await message.reply('❌ يجب ذكر المستخدم!');
  }
  
  try {
    const premium = await Premium.findActiveByUser(targetUser.id, message.guild.id);
    
    if (!premium) {
      return await message.reply(`❌ ${targetUser.username} ليس لديه Premium نشط!`);
    }
    
    // إلغاء Premium
    premium.deactivate();
    await premium.save();
    
    const embed = new EmbedBuilder()
      .setColor('#ff0000')
      .setTitle('❌ تم إلغاء Premium')
      .setDescription(`تم إلغاء Premium لـ ${targetUser}`)
      .addFields(
        {
          name: 'النوع المُلغى',
          value: getPremiumTypeDisplay(premium.premiumType),
          inline: true
        },
        {
          name: 'كان ينتهي في',
          value: `<t:${Math.floor(premium.endDate.getTime() / 1000)}:F>`,
          inline: true
        }
      )
      .setFooter({
        text: `ألغى بواسطة ${message.author.tag}`,
        iconURL: message.author.displayAvatarURL()
      })
      .setTimestamp();
    
    await message.reply({ embeds: [embed] });
    
  } catch (error) {
    console.error('خطأ في إلغاء Premium:', error);
    await message.reply('❌ حدث خطأ أثناء إلغاء Premium!');
  }
}

// فحص Premium
async function checkPremium(message, args) {
  const targetUser = message.mentions.users.first() || message.author;
  
  try {
    const premium = await Premium.findActiveByUser(targetUser.id, message.guild.id);
    
    if (!premium) {
      const embed = new EmbedBuilder()
        .setColor('#ff6b6b')
        .setTitle('❌ لا يوجد Premium')
        .setDescription(`${targetUser.username} ليس لديه Premium نشط`)
        .setThumbnail(targetUser.displayAvatarURL())
        .setTimestamp();
      
      return await message.reply({ embeds: [embed] });
    }
    
    const daysRemaining = premium.getDaysRemaining();
    const hoursRemaining = premium.getHoursRemaining();
    
    const embed = new EmbedBuilder()
      .setColor('#00ff00')
      .setTitle('✅ Premium نشط')
      .setThumbnail(targetUser.displayAvatarURL())
      .addFields(
        {
          name: '👤 المستخدم',
          value: `${targetUser} (${targetUser.tag})`,
          inline: true
        },
        {
          name: '🎭 النوع',
          value: getPremiumTypeDisplay(premium.premiumType),
          inline: true
        },
        {
          name: '⏰ المدة المتبقية',
          value: `${daysRemaining} يوم (${hoursRemaining} ساعة)`,
          inline: true
        },
        {
          name: '📅 تاريخ البداية',
          value: `<t:${Math.floor(premium.startDate.getTime() / 1000)}:F>`,
          inline: true
        },
        {
          name: '📅 تاريخ الانتهاء',
          value: `<t:${Math.floor(premium.endDate.getTime() / 1000)}:F>`,
          inline: true
        },
        {
          name: '📊 الاستخدام',
          value: `${premium.usage.commandsUsed} أمر مستخدم`,
          inline: true
        }
      )
      .setFooter({
        text: 'CS Discord Premium',
        iconURL: message.client.user.displayAvatarURL()
      })
      .setTimestamp();
    
    await message.reply({ embeds: [embed] });
    
  } catch (error) {
    console.error('خطأ في فحص Premium:', error);
    await message.reply('❌ حدث خطأ أثناء فحص Premium!');
  }
}

// عرض إحصائيات Premium
async function showPremiumStats(message) {
  try {
    const totalPremium = await Premium.countDocuments({ guildId: message.guild.id });
    const activePremium = await Premium.countDocuments({ 
      guildId: message.guild.id, 
      isActive: true,
      endDate: { $gt: new Date() }
    });
    
    const expiringSoon = await Premium.findExpiringSoon(7);
    const expired = await Premium.findExpired();
    
    // إحصائيات حسب النوع
    const typeStats = await Premium.aggregate([
      { $match: { guildId: message.guild.id, isActive: true } },
      { $group: { _id: '$premiumType', count: { $sum: 1 } } }
    ]);
    
    const embed = new EmbedBuilder()
      .setColor('#ffd700')
      .setTitle('📊 إحصائيات Premium')
      .addFields(
        {
          name: '📈 إحصائيات عامة',
          value: `
          👥 إجمالي المستخدمين: **${totalPremium}**
          ✅ نشط حالياً: **${activePremium}**
          ⚠️ ينتهي خلال 7 أيام: **${expiringSoon.length}**
          ❌ منتهي: **${expired.length}**
          `,
          inline: false
        }
      )
      .setFooter({
        text: 'CS Discord Premium Statistics',
        iconURL: message.client.user.displayAvatarURL()
      })
      .setTimestamp();
    
    // إضافة إحصائيات الأنواع
    if (typeStats.length > 0) {
      const typeStatsText = typeStats.map(stat => 
        `${getPremiumTypeDisplay(stat._id)}: **${stat.count}**`
      ).join('\n');
      
      embed.addFields({
        name: '🎭 حسب النوع',
        value: typeStatsText,
        inline: true
      });
    }
    
    await message.reply({ embeds: [embed] });
    
  } catch (error) {
    console.error('خطأ في إحصائيات Premium:', error);
    await message.reply('❌ حدث خطأ أثناء جلب الإحصائيات!');
  }
}

// دوال مساعدة
function parseDuration(duration) {
  const match = duration.match(/^(\d+)([dmyDMY])$/);
  if (!match) return null;
  
  const value = parseInt(match[1]);
  const unit = match[2].toLowerCase();
  
  switch (unit) {
    case 'd': return value;
    case 'm': return value * 30;
    case 'y': return value * 365;
    default: return null;
  }
}

function getPremiumTypeDisplay(type) {
  const displays = {
    basic: '🥉 Basic',
    premium: '🥈 Premium',
    vip: '🥇 VIP',
    ultimate: '💎 Ultimate'
  };
  
  return displays[type] || type;
}

function getPremiumFeatures(type) {
  const features = {
    basic: '• أوامر مخصصة (10)\n• ردود تلقائية (15)\n• تذاكر (30)',
    premium: '• أوامر مخصصة (25)\n• ردود تلقائية (30)\n• تذاكر (50)\n• إشراف متقدم\n• تعزيز اقتصاد (1.5x)',
    vip: '• أوامر مخصصة (50)\n• ردود تلقائية (50)\n• تذاكر (100)\n• إشراف متقدم\n• رتب تلقائية\n• رسائل ترحيب\n• تعزيز اقتصاد (2x)',
    ultimate: '• أوامر مخصصة (100)\n• ردود تلقائية (100)\n• تذاكر (200)\n• جميع ميزات الإشراف\n• رتب تلقائية\n• رسائل ترحيب\n• تعزيز اقتصاد (3x)\n• بوت مخصص\n• دعم أولوية'
  };
  
  return features[type] || 'ميزات أساسية';
}
