// ========================================
// 🔄 أمر عكس النص - CS Discord
// ========================================
// 📺 يوتيوب: https://www.youtube.com/@CS_Discord
// 💬 ديسكورد: https://discord.gg/yqTn2EwVsd
// ========================================

const { EmbedBuilder } = require('discord.js');

module.exports = {
  name: 'reverse',
  aliases: ['عكس', 'flip', 'backward'],
  description: 'عكس النص المكتوب',
  usage: '<النص>',
  category: 'fun',
  cooldown: 2,
  
  async execute(message, args) {
    try {
      // التحقق من وجود النص
      if (!args.length) {
        return await message.reply('❌ يجب كتابة النص المراد عكسه!\nمثال: `reverse مرحبا بكم`');
      }
      
      // دمج النص
      const originalText = args.join(' ');
      
      // التحقق من طول النص
      if (originalText.length > 1000) {
        return await message.reply('❌ النص طويل جداً! الحد الأقصى 1000 حرف.');
      }
      
      // عكس النص
      const reversedText = reverseText(originalText);
      
      // عكس الكلمات (اختياري)
      const reversedWords = originalText.split(' ').reverse().join(' ');
      
      // عكس كل كلمة على حدة
      const reversedEachWord = originalText.split(' ').map(word => word.split('').reverse().join('')).join(' ');
      
      // إنشاء الـ embed
      const embed = new EmbedBuilder()
        .setColor('#9932cc')
        .setTitle('🔄 عكس النص')
        .addFields(
          {
            name: '📝 النص الأصلي',
            value: `\`\`\`${originalText}\`\`\``,
            inline: false
          },
          {
            name: '🔄 النص المعكوس',
            value: `\`\`\`${reversedText}\`\`\``,
            inline: false
          },
          {
            name: '🔀 عكس ترتيب الكلمات',
            value: `\`\`\`${reversedWords}\`\`\``,
            inline: false
          },
          {
            name: '🌀 عكس كل كلمة',
            value: `\`\`\`${reversedEachWord}\`\`\``,
            inline: false
          }
        )
        .addFields(
          {
            name: '📊 إحصائيات',
            value: `📏 الطول: **${originalText.length}** حرف\n📝 الكلمات: **${originalText.split(' ').length}** كلمة`,
            inline: true
          },
          {
            name: '🔤 تحليل الأحرف',
            value: `🔤 أحرف: **${originalText.replace(/[^a-zA-Zأ-ي]/g, '').length}**\n🔢 أرقام: **${originalText.replace(/[^0-9]/g, '').length}**\n⭐ رموز: **${originalText.replace(/[a-zA-Zأ-ي0-9\s]/g, '').length}**`,
            inline: true
          }
        )
        .setFooter({
          text: 'CS Discord Fun Commands',
          iconURL: message.client.user.displayAvatarURL()
        })
        .setTimestamp();
      
      // إضافة ميزات إضافية للنصوص الطويلة
      if (originalText.length > 50) {
        embed.addFields({
          name: '🎯 نصائح',
          value: '💡 يمكنك نسخ النص المعكوس واستخدامه!\n💡 جرب عكس النص المعكوس للحصول على النص الأصلي!',
          inline: false
        });
      }
      
      await message.reply({ embeds: [embed] });
      
      // إضافة ردود فعل
      const reactions = ['🔄', '🔀', '🌀'];
      for (const reaction of reactions) {
        try {
          await message.react(reaction);
          await new Promise(resolve => setTimeout(resolve, 300));
        } catch (error) {
          // تجاهل خطأ الردود
        }
      }
      
    } catch (error) {
      console.error('خطأ في أمر عكس النص:', error);
      await message.reply('❌ حدث خطأ أثناء عكس النص!');
    }
  }
};

// دالة عكس النص مع دعم الأحرف العربية
function reverseText(text) {
  return text.split('').reverse().join('');
}
