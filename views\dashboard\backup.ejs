<%- include('../partials/header') %>

<div class="main-content">
    <div class="content-header">
        <h1 class="page-title">
            <i class="fas fa-cloud-download-alt"></i>
            النسخ الاحتياطي
        </h1>
        <p class="page-description">
            إنشاء واستعادة النسخ الاحتياطية لإعدادات الخادم والبوت
        </p>
    </div>

    <!-- إحصائيات النسخ الاحتياطي -->
    <div class="stats-grid">
        <div class="stat-card total">
            <div class="stat-number" id="total-backups">15</div>
            <div class="stat-label">إجمالي النسخ</div>
        </div>
        <div class="stat-card recent">
            <div class="stat-number" id="recent-backups">3</div>
            <div class="stat-label">نسخ هذا الأسبوع</div>
        </div>
        <div class="stat-card size">
            <div class="stat-number" id="total-size">2.4 GB</div>
            <div class="stat-label">الحجم الإجمالي</div>
        </div>
        <div class="stat-card auto">
            <div class="stat-number" id="auto-backups">نشط</div>
            <div class="stat-label">النسخ التلقائي</div>
        </div>
    </div>

    <!-- أدوات النسخ الاحتياطي -->
    <div class="backup-tools">
        <div class="card">
            <div class="card-header">
                <div class="card-icon">
                    <i class="fas fa-plus"></i>
                </div>
                <div>
                    <h3 class="card-title">إنشاء نسخة احتياطية جديدة</h3>
                </div>
            </div>
            <div class="card-body">
                <form id="createBackupForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">اسم النسخة الاحتياطية</label>
                                <input type="text" class="form-control" id="backupName" placeholder="مثال: نسخة احتياطية يومية" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">نوع النسخة</label>
                                <select class="form-control" id="backupType" required>
                                    <option value="full">نسخة كاملة</option>
                                    <option value="settings">الإعدادات فقط</option>
                                    <option value="data">البيانات فقط</option>
                                    <option value="custom">مخصص</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">وصف النسخة (اختياري)</label>
                        <textarea class="form-control" id="backupDescription" rows="3" placeholder="وصف مختصر للنسخة الاحتياطية..."></textarea>
                    </div>
                    
                    <div class="backup-options">
                        <label class="form-label">العناصر المراد نسخها:</label>
                        <div class="options-grid">
                            <div class="option-item">
                                <input type="checkbox" id="backup_settings" checked>
                                <label for="backup_settings">إعدادات الخادم</label>
                            </div>
                            <div class="option-item">
                                <input type="checkbox" id="backup_roles" checked>
                                <label for="backup_roles">الرتب والصلاحيات</label>
                            </div>
                            <div class="option-item">
                                <input type="checkbox" id="backup_channels" checked>
                                <label for="backup_channels">القنوات</label>
                            </div>
                            <div class="option-item">
                                <input type="checkbox" id="backup_automod" checked>
                                <label for="backup_automod">إعدادات الحماية</label>
                            </div>
                            <div class="option-item">
                                <input type="checkbox" id="backup_economy" checked>
                                <label for="backup_economy">النظام الاقتصادي</label>
                            </div>
                            <div class="option-item">
                                <input type="checkbox" id="backup_levels" checked>
                                <label for="backup_levels">نظام المستويات</label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="backup-schedule">
                        <label class="form-label">جدولة النسخ:</label>
                        <div class="schedule-options">
                            <div class="schedule-item">
                                <input type="radio" name="schedule" id="schedule_manual" value="manual" checked>
                                <label for="schedule_manual">يدوي</label>
                            </div>
                            <div class="schedule-item">
                                <input type="radio" name="schedule" id="schedule_daily" value="daily">
                                <label for="schedule_daily">يومي</label>
                            </div>
                            <div class="schedule-item">
                                <input type="radio" name="schedule" id="schedule_weekly" value="weekly">
                                <label for="schedule_weekly">أسبوعي</label>
                            </div>
                            <div class="schedule-item">
                                <input type="radio" name="schedule" id="schedule_monthly" value="monthly">
                                <label for="schedule_monthly">شهري</label>
                            </div>
                        </div>
                    </div>
                    
                    <button type="button" class="btn btn-success" onclick="createBackup()">
                        <i class="fas fa-save"></i>
                        إنشاء النسخة الاحتياطية
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- قائمة النسخ الاحتياطية -->
    <div class="card">
        <div class="card-header">
            <div class="card-icon">
                <i class="fas fa-history"></i>
            </div>
            <div>
                <h3 class="card-title">النسخ الاحتياطية المحفوظة</h3>
            </div>
            <div class="card-actions">
                <select class="form-control form-control-sm" id="backupFilter" onchange="filterBackups()">
                    <option value="all">جميع النسخ</option>
                    <option value="full">نسخ كاملة</option>
                    <option value="settings">إعدادات فقط</option>
                    <option value="data">بيانات فقط</option>
                </select>
                <button class="btn btn-sm btn-primary" onclick="refreshBackups()">
                    <i class="fas fa-sync"></i>
                    تحديث
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="backups-container" id="backups-container">
                <div class="loading-spinner">
                    <i class="fas fa-spinner fa-spin"></i>
                    جاري تحميل النسخ الاحتياطية...
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.stat-card.total {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.stat-card.recent {
    background: linear-gradient(135deg, #00ff88, #00d4aa);
    color: white;
}

.stat-card.size {
    background: linear-gradient(135deg, #feca57, #ff9ff3);
    color: white;
}

.stat-card.auto {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
}

.backup-tools {
    margin-bottom: 30px;
}

.options-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    background: rgba(255, 255, 255, 0.05);
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.option-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.option-item input[type="checkbox"] {
    width: 16px;
    height: 16px;
}

.option-item label {
    color: rgba(255, 255, 255, 0.9);
    cursor: pointer;
    font-size: 0.9rem;
}

.schedule-options {
    display: flex;
    gap: 20px;
    background: rgba(255, 255, 255, 0.05);
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.schedule-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.schedule-item input[type="radio"] {
    width: 16px;
    height: 16px;
}

.schedule-item label {
    color: rgba(255, 255, 255, 0.9);
    cursor: pointer;
    font-size: 0.9rem;
}

.backups-container {
    min-height: 300px;
}

.backup-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    margin-bottom: 15px;
    border-left: 4px solid;
    transition: all 0.3s ease;
}

.backup-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(5px);
}

.backup-item.full {
    border-left-color: #667eea;
}

.backup-item.settings {
    border-left-color: #00ff88;
}

.backup-item.data {
    border-left-color: #feca57;
}

.backup-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.backup-item.full .backup-icon {
    background: linear-gradient(45deg, #667eea, #764ba2);
}

.backup-item.settings .backup-icon {
    background: linear-gradient(45deg, #00ff88, #00d4aa);
}

.backup-item.data .backup-icon {
    background: linear-gradient(45deg, #feca57, #ff9ff3);
}

.backup-info {
    flex: 1;
}

.backup-name {
    color: white;
    font-weight: bold;
    font-size: 1.1rem;
    margin-bottom: 5px;
}

.backup-details {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.backup-meta {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.8rem;
}

.backup-status {
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: bold;
}

.backup-status.success {
    background: #00ff88;
    color: #333;
}

.backup-status.processing {
    background: #feca57;
    color: #333;
}

.backup-status.failed {
    background: #ff6b6b;
    color: white;
}

.backup-actions {
    display: flex;
    gap: 10px;
    flex-direction: column;
}

@media (max-width: 768px) {
    .options-grid {
        grid-template-columns: 1fr;
    }
    
    .schedule-options {
        flex-direction: column;
        gap: 10px;
    }
    
    .backup-item {
        flex-direction: column;
        text-align: center;
    }
    
    .backup-actions {
        flex-direction: row;
        justify-content: center;
    }
}
</style>

<script>
// بيانات النسخ الاحتياطية التجريبية
let backups = [
    {
        id: '1',
        name: 'نسخة احتياطية كاملة',
        type: 'full',
        description: 'نسخة كاملة من جميع إعدادات الخادم',
        size: '245 MB',
        createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
        status: 'success',
        schedule: 'weekly'
    },
    {
        id: '2',
        name: 'نسخة الإعدادات',
        type: 'settings',
        description: 'إعدادات الخادم والبوت فقط',
        size: '12 MB',
        createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
        status: 'success',
        schedule: 'daily'
    },
    {
        id: '3',
        name: 'نسخة البيانات',
        type: 'data',
        description: 'بيانات الأعضاء والاقتصاد',
        size: '89 MB',
        createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000),
        status: 'processing',
        schedule: 'manual'
    }
];

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadBackups();
    updateStats();
});

// تحميل النسخ الاحتياطية
function loadBackups() {
    displayBackups();
}

// عرض النسخ الاحتياطية
function displayBackups() {
    const container = document.getElementById('backups-container');
    const filter = document.getElementById('backupFilter')?.value || 'all';
    
    let filteredBackups = backups;
    if (filter !== 'all') {
        filteredBackups = backups.filter(backup => backup.type === filter);
    }
    
    if (filteredBackups.length === 0) {
        container.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-cloud-download-alt"></i>
                <h4>لا توجد نسخ احتياطية</h4>
                <p>ابدأ بإنشاء نسخة احتياطية جديدة</p>
            </div>
        `;
        return;
    }
    
    container.innerHTML = filteredBackups.map(backup => `
        <div class="backup-item ${backup.type}">
            <div class="backup-icon">
                <i class="fas fa-${getBackupIcon(backup.type)}"></i>
            </div>
            <div class="backup-info">
                <div class="backup-name">${backup.name}</div>
                <div class="backup-details">${backup.description}</div>
                <div class="backup-meta">
                    الحجم: ${backup.size} • تم الإنشاء: ${backup.createdAt.toLocaleDateString('ar-EG')} • الجدولة: ${getScheduleText(backup.schedule)}
                </div>
            </div>
            <span class="backup-status ${backup.status}">
                ${getStatusText(backup.status)}
            </span>
            <div class="backup-actions">
                ${backup.status === 'success' ? `
                    <button class="btn btn-sm btn-success" onclick="restoreBackup('${backup.id}')">
                        <i class="fas fa-undo"></i>
                        استعادة
                    </button>
                    <button class="btn btn-sm btn-info" onclick="downloadBackup('${backup.id}')">
                        <i class="fas fa-download"></i>
                        تحميل
                    </button>
                ` : ''}
                <button class="btn btn-sm btn-danger" onclick="deleteBackup('${backup.id}')">
                    <i class="fas fa-trash"></i>
                    حذف
                </button>
            </div>
        </div>
    `).join('');
}

// إنشاء نسخة احتياطية
function createBackup() {
    const name = document.getElementById('backupName').value.trim();
    const type = document.getElementById('backupType').value;
    const description = document.getElementById('backupDescription').value.trim();
    
    if (!name) {
        showNotification('يرجى إدخال اسم النسخة الاحتياطية!', 'warning');
        return;
    }
    
    // إنشاء نسخة احتياطية جديدة
    const newBackup = {
        id: Date.now().toString(),
        name: name,
        type: type,
        description: description || 'نسخة احتياطية جديدة',
        size: Math.floor(Math.random() * 200) + 10 + ' MB',
        createdAt: new Date(),
        status: 'processing',
        schedule: document.querySelector('input[name="schedule"]:checked').value
    };
    
    backups.unshift(newBackup);
    displayBackups();
    updateStats();
    
    // إعادة تعيين النموذج
    document.getElementById('createBackupForm').reset();
    document.getElementById('schedule_manual').checked = true;
    
    showNotification(`✅ تم بدء إنشاء النسخة الاحتياطية "${name}"!`, 'success');
    
    // محاكاة اكتمال النسخة الاحتياطية
    setTimeout(() => {
        newBackup.status = 'success';
        displayBackups();
        showNotification(`🎉 تم إنشاء النسخة الاحتياطية "${name}" بنجاح!`, 'success');
    }, 3000);
}

// استعادة نسخة احتياطية
function restoreBackup(backupId) {
    const backup = backups.find(b => b.id === backupId);
    if (!backup) return;
    
    if (confirm(`هل أنت متأكد من استعادة النسخة الاحتياطية "${backup.name}"؟\nسيتم استبدال الإعدادات الحالية!`)) {
        showNotification(`🔄 جاري استعادة النسخة الاحتياطية "${backup.name}"...`, 'info');
        
        setTimeout(() => {
            showNotification(`✅ تم استعادة النسخة الاحتياطية "${backup.name}" بنجاح!`, 'success');
        }, 2000);
    }
}

// تحميل نسخة احتياطية
function downloadBackup(backupId) {
    const backup = backups.find(b => b.id === backupId);
    if (!backup) return;
    
    showNotification(`📥 جاري تحميل النسخة الاحتياطية "${backup.name}"...`, 'info');
    
    // محاكاة التحميل
    setTimeout(() => {
        showNotification(`✅ تم تحميل النسخة الاحتياطية "${backup.name}" بنجاح!`, 'success');
    }, 1500);
}

// حذف نسخة احتياطية
function deleteBackup(backupId) {
    const backup = backups.find(b => b.id === backupId);
    if (!backup) return;
    
    if (confirm(`هل أنت متأكد من حذف النسخة الاحتياطية "${backup.name}"؟\nهذا الإجراء لا يمكن التراجع عنه!`)) {
        backups = backups.filter(b => b.id !== backupId);
        displayBackups();
        updateStats();
        showNotification(`🗑️ تم حذف النسخة الاحتياطية "${backup.name}"!`, 'info');
    }
}

// فلترة النسخ الاحتياطية
function filterBackups() {
    displayBackups();
}

// تحديث النسخ الاحتياطية
function refreshBackups() {
    loadBackups();
    showNotification('تم تحديث قائمة النسخ الاحتياطية!', 'info');
}

// تحديث الإحصائيات
function updateStats() {
    const totalBackups = backups.length;
    const recentBackups = backups.filter(b => {
        const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        return b.createdAt > weekAgo;
    }).length;
    
    document.getElementById('total-backups').textContent = totalBackups;
    document.getElementById('recent-backups').textContent = recentBackups;
}

// دوال مساعدة
function getBackupIcon(type) {
    const icons = {
        full: 'database',
        settings: 'cog',
        data: 'table'
    };
    return icons[type] || 'file';
}

function getStatusText(status) {
    const statusTexts = {
        success: 'مكتملة',
        processing: 'جاري المعالجة',
        failed: 'فشلت'
    };
    return statusTexts[status] || status;
}

function getScheduleText(schedule) {
    const scheduleTexts = {
        manual: 'يدوي',
        daily: 'يومي',
        weekly: 'أسبوعي',
        monthly: 'شهري'
    };
    return scheduleTexts[schedule] || schedule;
}

// إظهار الإشعارات
function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type} notification`;
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        animation: slideIn 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}
</script>

<%- include('../partials/footer') %>
