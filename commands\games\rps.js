// ========================================
// ✂️ لعبة حجر ورقة مقص - CS Discord
// ========================================
// 📺 يوتيوب: https://www.youtube.com/@CS_Discord
// 💬 ديسكورد: https://discord.gg/yqTn2EwVsd
// ========================================

const { EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');

module.exports = {
  name: 'rps',
  aliases: ['rockpaperscissors', 'حجر_ورقة_مقص', 'حجر'],
  description: 'العب حجر ورقة مقص ضد البوت',
  usage: '[rock/paper/scissors] أو [حجر/ورقة/مقص]',
  category: 'games',
  cooldown: 3,
  
  async execute(message, args) {
    try {
      // إذا لم يتم تحديد اختيار، عرض الأزرار
      if (!args[0]) {
        return await showRPSButtons(message);
      }
      
      // تحديد اختيار اللاعب
      const playerChoice = parseChoice(args[0].toLowerCase());
      
      if (!playerChoice) {
        return await message.reply('❌ اختيار غير صحيح! استخدم: `rock`, `paper`, `scissors` أو `حجر`, `ورقة`, `مقص`');
      }
      
      // اختيار البوت العشوائي
      const botChoice = getRandomChoice();
      
      // تحديد النتيجة
      const result = determineWinner(playerChoice, botChoice);
      
      // إرسال النتيجة
      await sendResult(message, playerChoice, botChoice, result);
      
    } catch (error) {
      console.error('خطأ في لعبة حجر ورقة مقص:', error);
      await message.reply('❌ حدث خطأ أثناء اللعبة!');
    }
  }
};

// عرض أزرار اللعبة
async function showRPSButtons(message) {
  const embed = new EmbedBuilder()
    .setColor('#0099ff')
    .setTitle('🎮 حجر ورقة مقص')
    .setDescription('اختر حركتك للعب ضد البوت!')
    .addFields(
      {
        name: '🪨 حجر',
        value: 'يكسر المقص',
        inline: true
      },
      {
        name: '📄 ورقة',
        value: 'تغطي الحجر',
        inline: true
      },
      {
        name: '✂️ مقص',
        value: 'يقطع الورقة',
        inline: true
      }
    )
    .setFooter({
      text: 'CS Discord Games',
      iconURL: message.client.user.displayAvatarURL()
    })
    .setTimestamp();
  
  const row = new ActionRowBuilder()
    .addComponents(
      new ButtonBuilder()
        .setCustomId('rps_rock')
        .setLabel('🪨 حجر')
        .setStyle(ButtonStyle.Primary),
      new ButtonBuilder()
        .setCustomId('rps_paper')
        .setLabel('📄 ورقة')
        .setStyle(ButtonStyle.Success),
      new ButtonBuilder()
        .setCustomId('rps_scissors')
        .setLabel('✂️ مقص')
        .setStyle(ButtonStyle.Danger)
    );
  
  const gameMessage = await message.reply({ embeds: [embed], components: [row] });
  
  // انتظار التفاعل
  const filter = (interaction) => {
    return interaction.user.id === message.author.id && 
           interaction.customId.startsWith('rps_');
  };
  
  try {
    const interaction = await gameMessage.awaitMessageComponent({ 
      filter, 
      time: 30000 
    });
    
    const playerChoice = interaction.customId.split('_')[1];
    const botChoice = getRandomChoice();
    const result = determineWinner(playerChoice, botChoice);
    
    await sendResultInteraction(interaction, playerChoice, botChoice, result);
    
  } catch (error) {
    // انتهت المهلة
    const timeoutEmbed = new EmbedBuilder()
      .setColor('#ffa500')
      .setTitle('⏰ انتهت المهلة')
      .setDescription('انتهت مهلة اللعبة!')
      .setTimestamp();
    
    await gameMessage.edit({ embeds: [timeoutEmbed], components: [] });
  }
}

// تحليل اختيار اللاعب
function parseChoice(choice) {
  const choices = {
    'rock': 'rock',
    'حجر': 'rock',
    'r': 'rock',
    'paper': 'paper',
    'ورقة': 'paper',
    'p': 'paper',
    'scissors': 'scissors',
    'مقص': 'scissors',
    's': 'scissors'
  };
  
  return choices[choice] || null;
}

// اختيار عشوائي للبوت
function getRandomChoice() {
  const choices = ['rock', 'paper', 'scissors'];
  return choices[Math.floor(Math.random() * choices.length)];
}

// تحديد الفائز
function determineWinner(playerChoice, botChoice) {
  if (playerChoice === botChoice) {
    return 'tie';
  }
  
  const winConditions = {
    'rock': 'scissors',
    'paper': 'rock',
    'scissors': 'paper'
  };
  
  return winConditions[playerChoice] === botChoice ? 'win' : 'lose';
}

// إرسال النتيجة (للأمر النصي)
async function sendResult(message, playerChoice, botChoice, result) {
  const embed = createResultEmbed(message.author, playerChoice, botChoice, result);
  await message.reply({ embeds: [embed] });
}

// إرسال النتيجة (للتفاعل)
async function sendResultInteraction(interaction, playerChoice, botChoice, result) {
  const embed = createResultEmbed(interaction.user, playerChoice, botChoice, result);
  await interaction.update({ embeds: [embed], components: [] });
}

// إنشاء embed النتيجة
function createResultEmbed(user, playerChoice, botChoice, result) {
  const choiceEmojis = {
    'rock': '🪨',
    'paper': '📄',
    'scissors': '✂️'
  };
  
  const choiceNames = {
    'rock': 'حجر',
    'paper': 'ورقة',
    'scissors': 'مقص'
  };
  
  let color, title, description, resultEmoji;
  
  switch (result) {
    case 'win':
      color = '#00ff00';
      title = '🎉 فزت!';
      description = 'تهانينا! لقد هزمت البوت!';
      resultEmoji = '🏆';
      break;
    case 'lose':
      color = '#ff0000';
      title = '😢 خسرت!';
      description = 'البوت فاز هذه المرة! حاول مرة أخرى!';
      resultEmoji = '💔';
      break;
    case 'tie':
      color = '#ffa500';
      title = '🤝 تعادل!';
      description = 'نفس الاختيار! العبوا مرة أخرى!';
      resultEmoji = '🤝';
      break;
  }
  
  const embed = new EmbedBuilder()
    .setColor(color)
    .setTitle(`${resultEmoji} ${title}`)
    .setDescription(description)
    .addFields(
      {
        name: '👤 اختيارك',
        value: `${choiceEmojis[playerChoice]} ${choiceNames[playerChoice]}`,
        inline: true
      },
      {
        name: '🤖 اختيار البوت',
        value: `${choiceEmojis[botChoice]} ${choiceNames[botChoice]}`,
        inline: true
      },
      {
        name: '📊 النتيجة',
        value: getResultExplanation(playerChoice, botChoice, result),
        inline: false
      }
    )
    .setFooter({
      text: 'CS Discord Games',
      iconURL: user.client.user.displayAvatarURL()
    })
    .setTimestamp();
  
  // إضافة صورة متحركة حسب النتيجة
  if (result === 'win') {
    embed.setImage('https://media.giphy.com/media/g9582DNuQppxC/giphy.gif');
  } else if (result === 'lose') {
    embed.setImage('https://media.giphy.com/media/l2JehQ2GitHGdVG9y/giphy.gif');
  }
  
  // إضافة إحصائيات اللاعب (محاكاة)
  const stats = generatePlayerStats();
  embed.addFields({
    name: '📈 إحصائياتك',
    value: `🏆 انتصارات: ${stats.wins} | 💔 هزائم: ${stats.losses} | 🤝 تعادل: ${stats.ties}`,
    inline: false
  });
  
  return embed;
}

// شرح النتيجة
function getResultExplanation(playerChoice, botChoice, result) {
  if (result === 'tie') {
    return 'نفس الاختيار = تعادل!';
  }
  
  const explanations = {
    'rock-scissors': 'الحجر يكسر المقص',
    'paper-rock': 'الورقة تغطي الحجر',
    'scissors-paper': 'المقص يقطع الورقة',
    'scissors-rock': 'الحجر يكسر المقص',
    'rock-paper': 'الورقة تغطي الحجر',
    'paper-scissors': 'المقص يقطع الورقة'
  };
  
  const key = result === 'win' ? `${playerChoice}-${botChoice}` : `${botChoice}-${playerChoice}`;
  return explanations[key] || 'نتيجة غير متوقعة!';
}

// إنشاء إحصائيات وهمية للاعب
function generatePlayerStats() {
  return {
    wins: Math.floor(Math.random() * 50) + 10,
    losses: Math.floor(Math.random() * 40) + 5,
    ties: Math.floor(Math.random() * 20) + 2
  };
}
