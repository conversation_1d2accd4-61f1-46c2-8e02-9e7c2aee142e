// ========================================
// 🛒 أمر المتجر - CS Discord
// ========================================
// 📺 يوتيوب: https://www.youtube.com/@CS_Discord
// 💬 ديسكورد: https://discord.gg/yqTn2EwVsd
// ========================================

const { EmbedBuilder, ActionRowBuilder, StringSelectMenuBuilder } = require('discord.js');
const { ShopItem, UserInventory } = require('../../models/Shop');
const Currency = require('../../models/Currency');

module.exports = {
  name: 'shop',
  aliases: ['store', 'متجر', 'دكان'],
  description: 'عرض المتجر وشراء العناصر',
  usage: '[buy <رقم العنصر>]',
  category: 'economy',
  cooldown: 3,
  
  async execute(message, args) {
    try {
      // إذا كان الأمر للشراء
      if (args[0] === 'buy' && args[1]) {
        return await handlePurchase(message, args[1]);
      }
      
      // عرض المتجر
      await displayShop(message);
      
    } catch (error) {
      console.error('خطأ في أمر المتجر:', error);
      await message.reply('❌ حدث خطأ أثناء عرض المتجر!');
    }
  }
};

async function displayShop(message) {
  // الحصول على عناصر المتجر أو إنشاؤها إذا لم تكن موجودة
  let shopItems = await ShopItem.find({ guildId: message.guild.id, isActive: true });
  
  if (shopItems.length === 0) {
    // إنشاء عناصر افتراضية للمتجر
    await createDefaultShopItems(message.guild.id);
    shopItems = await ShopItem.find({ guildId: message.guild.id, isActive: true });
  }
  
  // تجميع العناصر حسب النوع
  const categories = {
    role: { name: '🎭 الرتب', items: [] },
    item: { name: '📦 العناصر', items: [] },
    boost: { name: '⚡ التعزيزات', items: [] },
    cosmetic: { name: '✨ التجميل', items: [] },
    special: { name: '🌟 خاص', items: [] }
  };
  
  shopItems.forEach(item => {
    if (categories[item.type]) {
      categories[item.type].items.push(item);
    }
  });
  
  // إنشاء الـ embed الرئيسي
  const embed = new EmbedBuilder()
    .setColor('#00ff00')
    .setTitle('🛒 متجر الخادم')
    .setDescription('مرحباً بك في المتجر! اختر فئة لعرض العناصر المتاحة.')
    .setThumbnail(message.guild.iconURL({ dynamic: true }))
    .setFooter({
      text: 'CS Discord Economy System',
      iconURL: message.client.user.displayAvatarURL()
    })
    .setTimestamp();
  
  // إضافة معلومات الفئات
  Object.entries(categories).forEach(([key, category]) => {
    if (category.items.length > 0) {
      embed.addFields({
        name: category.name,
        value: `${category.items.length} عنصر متاح`,
        inline: true
      });
    }
  });
  
  // إضافة معلومات المستخدم
  const userCurrency = await Currency.getOrCreate(message.author.id, message.guild.id);
  embed.addFields({
    name: '💰 رصيدك',
    value: `**${userCurrency.balance.toLocaleString('ar-EG')}** عملة`,
    inline: true
  });
  
  // إنشاء قائمة الاختيار
  const selectMenu = new StringSelectMenuBuilder()
    .setCustomId('shop_category')
    .setPlaceholder('اختر فئة لعرض العناصر...');
  
  Object.entries(categories).forEach(([key, category]) => {
    if (category.items.length > 0) {
      selectMenu.addOptions({
        label: category.name,
        value: key,
        description: `عرض ${category.items.length} عنصر`,
        emoji: category.name.split(' ')[0]
      });
    }
  });
  
  const row = new ActionRowBuilder().addComponents(selectMenu);
  
  await message.reply({ embeds: [embed], components: [row] });
}

async function handlePurchase(message, itemId) {
  try {
    // البحث عن العنصر
    const item = await ShopItem.findById(itemId);
    if (!item || !item.isActive) {
      return await message.reply('❌ العنصر غير موجود أو غير متاح!');
    }
    
    // محاولة الشراء
    const newItem = await item.purchase(message.author.id, message.guild.id);
    
    // إنشاء embed للتأكيد
    const embed = new EmbedBuilder()
      .setColor('#00ff00')
      .setTitle('🎉 تم الشراء بنجاح!')
      .setDescription(`تم شراء **${item.name}** بنجاح!`)
      .addFields(
        {
          name: '💰 السعر',
          value: `${item.price.toLocaleString('ar-EG')} عملة`,
          inline: true
        },
        {
          name: '📦 العنصر',
          value: item.description,
          inline: true
        }
      )
      .setFooter({
        text: 'CS Discord Economy System',
        iconURL: message.client.user.displayAvatarURL()
      })
      .setTimestamp();
    
    await message.reply({ embeds: [embed] });
    
  } catch (error) {
    await message.reply(`❌ فشل الشراء: ${error.message}`);
  }
}

async function createDefaultShopItems(guildId) {
  const defaultItems = [
    {
      guildId,
      name: 'رتبة VIP',
      description: 'رتبة مميزة لمدة 30 يوم',
      price: 5000,
      type: 'role',
      settings: {
        roleDuration: 30,
        emoji: '👑'
      }
    },
    {
      guildId,
      name: 'تعزيز XP',
      description: 'مضاعفة XP لمدة 24 ساعة',
      price: 1000,
      type: 'boost',
      settings: {
        boostType: 'xp',
        boostAmount: 2,
        boostDuration: 24,
        emoji: '⚡'
      }
    },
    {
      guildId,
      name: 'تعزيز العملات',
      description: 'مضاعفة أرباح العمل لمدة 12 ساعة',
      price: 2000,
      type: 'boost',
      settings: {
        boostType: 'currency',
        boostAmount: 2,
        boostDuration: 12,
        emoji: '💰'
      }
    },
    {
      guildId,
      name: 'حظ الذهب',
      description: 'زيادة حظ المقامرة لمدة 6 ساعات',
      price: 3000,
      type: 'boost',
      settings: {
        boostType: 'luck',
        boostAmount: 1.5,
        boostDuration: 6,
        emoji: '🍀'
      }
    },
    {
      guildId,
      name: 'لقب مخصص',
      description: 'إمكانية وضع لقب مخصص لمدة 7 أيام',
      price: 1500,
      type: 'cosmetic',
      settings: {
        emoji: '✨',
        maxUses: 1
      }
    },
    {
      guildId,
      name: 'صندوق الكنز',
      description: 'صندوق يحتوي على مكافآت عشوائية',
      price: 500,
      type: 'item',
      settings: {
        emoji: '📦',
        rarity: 'common'
      }
    },
    {
      guildId,
      name: 'صندوق ذهبي',
      description: 'صندوق نادر بمكافآت أفضل',
      price: 2500,
      type: 'item',
      settings: {
        emoji: '🏆',
        rarity: 'rare'
      }
    },
    {
      guildId,
      name: 'إعادة تعيين الكولداون',
      description: 'إعادة تعيين جميع أوقات الانتظار',
      price: 1000,
      type: 'special',
      settings: {
        emoji: '🔄',
        maxUses: 1
      }
    }
  ];
  
  await ShopItem.insertMany(defaultItems);
}
