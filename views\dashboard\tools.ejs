<%- include('../partials/header') %>

<div class="main-content">
    <div class="content-header">
        <h1 class="page-title">
            <i class="fas fa-tools"></i>
            الأدوات المتقدمة
        </h1>
        <p class="page-description">
            مجموعة شاملة من الأدوات لإدارة وتحسين الخادم
        </p>
    </div>

    <!-- أدوات سريعة -->
    <div class="tools-grid">
        <!-- أداة تنظيف الخادم -->
        <div class="tool-card cleanup">
            <div class="tool-icon">
                <i class="fas fa-broom"></i>
            </div>
            <div class="tool-content">
                <h3>تنظيف الخادم</h3>
                <p>حذف الرسائل القديمة والقنوات غير المستخدمة</p>
                <div class="tool-stats">
                    <span>📊 آخر تنظيف: منذ 3 أيام</span>
                </div>
                <button class="btn btn-primary" onclick="openCleanupTool()">
                    <i class="fas fa-play"></i>
                    بدء التنظيف
                </button>
            </div>
        </div>

        <!-- أداة النسخ الاحتياطي -->
        <div class="tool-card backup">
            <div class="tool-icon">
                <i class="fas fa-download"></i>
            </div>
            <div class="tool-content">
                <h3>النسخ الاحتياطي</h3>
                <p>إنشاء نسخة احتياطية من إعدادات الخادم</p>
                <div class="tool-stats">
                    <span>💾 آخر نسخة: منذ أسبوع</span>
                </div>
                <button class="btn btn-success" onclick="createBackup()">
                    <i class="fas fa-save"></i>
                    إنشاء نسخة
                </button>
            </div>
        </div>

        <!-- أداة الإحصائيات المتقدمة -->
        <div class="tool-card analytics">
            <div class="tool-icon">
                <i class="fas fa-chart-line"></i>
            </div>
            <div class="tool-content">
                <h3>تحليلات متقدمة</h3>
                <p>إحصائيات مفصلة عن نشاط الخادم</p>
                <div class="tool-stats">
                    <span>📈 نمو الأعضاء: +15%</span>
                </div>
                <button class="btn btn-info" onclick="openAnalytics()">
                    <i class="fas fa-chart-bar"></i>
                    عرض التحليلات
                </button>
            </div>
        </div>

        <!-- أداة إدارة البوتات -->
        <div class="tool-card bots">
            <div class="tool-icon">
                <i class="fas fa-robot"></i>
            </div>
            <div class="tool-content">
                <h3>إدارة البوتات</h3>
                <p>مراقبة وإدارة البوتات في الخادم</p>
                <div class="tool-stats">
                    <span>🤖 البوتات النشطة: 5</span>
                </div>
                <button class="btn btn-warning" onclick="manageBots()">
                    <i class="fas fa-cogs"></i>
                    إدارة البوتات
                </button>
            </div>
        </div>

        <!-- أداة مراقبة الأمان -->
        <div class="tool-card security">
            <div class="tool-icon">
                <i class="fas fa-shield-alt"></i>
            </div>
            <div class="tool-content">
                <h3>مراقبة الأمان</h3>
                <p>فحص التهديدات ومراقبة الأنشطة المشبوهة</p>
                <div class="tool-stats">
                    <span>🛡️ التهديدات المحجوبة: 23</span>
                </div>
                <button class="btn btn-danger" onclick="securityScan()">
                    <i class="fas fa-search"></i>
                    فحص الأمان
                </button>
            </div>
        </div>

        <!-- أداة تحسين الأداء -->
        <div class="tool-card performance">
            <div class="tool-icon">
                <i class="fas fa-tachometer-alt"></i>
            </div>
            <div class="tool-content">
                <h3>تحسين الأداء</h3>
                <p>تحليل وتحسين أداء الخادم والبوت</p>
                <div class="tool-stats">
                    <span>⚡ نقاط الأداء: 95/100</span>
                </div>
                <button class="btn btn-primary" onclick="optimizePerformance()">
                    <i class="fas fa-rocket"></i>
                    تحسين الأداء
                </button>
            </div>
        </div>

        <!-- أداة إدارة الملفات -->
        <div class="tool-card files">
            <div class="tool-icon">
                <i class="fas fa-folder-open"></i>
            </div>
            <div class="tool-content">
                <h3>إدارة الملفات</h3>
                <p>تنظيم وإدارة ملفات الخادم والوسائط</p>
                <div class="tool-stats">
                    <span>📁 الملفات المحفوظة: 1.2GB</span>
                </div>
                <button class="btn btn-secondary" onclick="manageFiles()">
                    <i class="fas fa-file-alt"></i>
                    إدارة الملفات
                </button>
            </div>
        </div>

        <!-- أداة التقارير -->
        <div class="tool-card reports">
            <div class="tool-icon">
                <i class="fas fa-file-chart-line"></i>
            </div>
            <div class="tool-content">
                <h3>تقارير مفصلة</h3>
                <p>إنشاء تقارير شاملة عن نشاط الخادم</p>
                <div class="tool-stats">
                    <span>📋 آخر تقرير: اليوم</span>
                </div>
                <button class="btn btn-info" onclick="generateReport()">
                    <i class="fas fa-file-pdf"></i>
                    إنشاء تقرير
                </button>
            </div>
        </div>

        <!-- أداة الصيانة -->
        <div class="tool-card maintenance">
            <div class="tool-icon">
                <i class="fas fa-wrench"></i>
            </div>
            <div class="tool-content">
                <h3>وضع الصيانة</h3>
                <p>تفعيل وضع الصيانة للخادم مؤقتاً</p>
                <div class="tool-stats">
                    <span>🔧 الحالة: غير مفعل</span>
                </div>
                <button class="btn btn-warning" onclick="toggleMaintenance()">
                    <i class="fas fa-power-off"></i>
                    تفعيل الصيانة
                </button>
            </div>
        </div>
    </div>

    <!-- أدوات إضافية -->
    <div class="card mt-4">
        <div class="card-header">
            <div class="card-icon">
                <i class="fas fa-magic"></i>
            </div>
            <div>
                <h3 class="card-title">أدوات إضافية</h3>
            </div>
        </div>
        <div class="card-body">
            <div class="additional-tools">
                <div class="tool-item">
                    <div class="tool-info">
                        <h5>مولد الدعوات المخصصة</h5>
                        <p>إنشاء روابط دعوة مخصصة مع إعدادات متقدمة</p>
                    </div>
                    <button class="btn btn-primary" onclick="createCustomInvite()">
                        <i class="fas fa-link"></i>
                        إنشاء دعوة
                    </button>
                </div>

                <div class="tool-item">
                    <div class="tool-info">
                        <h5>مدقق الروابط</h5>
                        <p>فحص الروابط المشبوهة والضارة في الرسائل</p>
                    </div>
                    <button class="btn btn-warning" onclick="scanLinks()">
                        <i class="fas fa-search"></i>
                        فحص الروابط
                    </button>
                </div>

                <div class="tool-item">
                    <div class="tool-info">
                        <h5>مزامنة الرتب</h5>
                        <p>مزامنة الرتب بين خوادم متعددة</p>
                    </div>
                    <button class="btn btn-info" onclick="syncRoles()">
                        <i class="fas fa-sync"></i>
                        مزامنة الرتب
                    </button>
                </div>

                <div class="tool-item">
                    <div class="tool-info">
                        <h5>مراقب الكلمات</h5>
                        <p>مراقبة وتصفية الكلمات غير المرغوبة</p>
                    </div>
                    <button class="btn btn-danger" onclick="wordMonitor()">
                        <i class="fas fa-eye"></i>
                        مراقبة الكلمات
                    </button>
                </div>

                <div class="tool-item">
                    <div class="tool-info">
                        <h5>مولد الأكواد</h5>
                        <p>إنشاء أكواد تفعيل وكوبونات للأعضاء</p>
                    </div>
                    <button class="btn btn-success" onclick="generateCodes()">
                        <i class="fas fa-ticket-alt"></i>
                        إنشاء أكواد
                    </button>
                </div>

                <div class="tool-item">
                    <div class="tool-info">
                        <h5>محلل المشاعر</h5>
                        <p>تحليل مشاعر الأعضاء من خلال الرسائل</p>
                    </div>
                    <button class="btn btn-info" onclick="analyzeSentiment()">
                        <i class="fas fa-heart"></i>
                        تحليل المشاعر
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal تنظيف الخادم -->
<div class="modal fade" id="cleanupModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">🧹 تنظيف الخادم</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="cleanup-options">
                    <div class="option-group">
                        <h6>تنظيف الرسائل</h6>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="deleteOldMessages">
                            <label class="form-check-label" for="deleteOldMessages">
                                حذف الرسائل الأقدم من 30 يوم
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="deleteBotMessages">
                            <label class="form-check-label" for="deleteBotMessages">
                                حذف رسائل البوتات القديمة
                            </label>
                        </div>
                    </div>

                    <div class="option-group">
                        <h6>تنظيف القنوات</h6>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="deleteEmptyChannels">
                            <label class="form-check-label" for="deleteEmptyChannels">
                                حذف القنوات الفارغة
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="archiveOldChannels">
                            <label class="form-check-label" for="archiveOldChannels">
                                أرشفة القنوات غير النشطة
                            </label>
                        </div>
                    </div>

                    <div class="option-group">
                        <h6>تنظيف الأعضاء</h6>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="removeInactiveMembers">
                            <label class="form-check-label" for="removeInactiveMembers">
                                إزالة الأعضاء غير النشطين (90+ يوم)
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="removeBannedUsers">
                            <label class="form-check-label" for="removeBannedUsers">
                                تنظيف قائمة المحظورين
                            </label>
                        </div>
                    </div>
                </div>

                <div class="cleanup-preview mt-4">
                    <h6>معاينة التنظيف</h6>
                    <div class="preview-stats">
                        <div class="stat-item">
                            <span class="stat-label">الرسائل المحذوفة:</span>
                            <span class="stat-value">~1,250</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">القنوات المؤرشفة:</span>
                            <span class="stat-value">~3</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">المساحة المحررة:</span>
                            <span class="stat-value">~45MB</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-warning" onclick="previewCleanup()">
                    <i class="fas fa-eye"></i>
                    معاينة
                </button>
                <button type="button" class="btn btn-danger" onclick="startCleanup()">
                    <i class="fas fa-broom"></i>
                    بدء التنظيف
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
}

.tool-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    padding: 25px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.tool-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.tool-card:hover::before {
    transform: scaleX(1);
}

.tool-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    background: rgba(255, 255, 255, 0.1);
}

.tool-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-bottom: 20px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
}

.tool-content h3 {
    color: white;
    margin-bottom: 10px;
    font-size: 1.2rem;
}

.tool-content p {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 15px;
    font-size: 0.9rem;
    line-height: 1.5;
}

.tool-stats {
    margin-bottom: 20px;
}

.tool-stats span {
    background: rgba(255, 255, 255, 0.1);
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.9);
}

.additional-tools {
    display: grid;
    gap: 20px;
}

.tool-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.05);
    padding: 20px;
    border-radius: 15px;
    border-left: 4px solid #667eea;
    transition: all 0.3s ease;
}

.tool-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(5px);
}

.tool-info h5 {
    color: white;
    margin-bottom: 5px;
    font-size: 1.1rem;
}

.tool-info p {
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
    font-size: 0.9rem;
}

.cleanup-options {
    display: grid;
    gap: 25px;
}

.option-group h6 {
    color: #667eea;
    margin-bottom: 15px;
    font-weight: bold;
}

.form-check {
    margin-bottom: 10px;
}

.form-check-label {
    color: rgba(255, 255, 255, 0.9);
    cursor: pointer;
}

.cleanup-preview {
    background: rgba(255, 255, 255, 0.05);
    padding: 20px;
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.preview-stats {
    display: grid;
    gap: 10px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-label {
    color: rgba(255, 255, 255, 0.8);
}

.stat-value {
    color: #667eea;
    font-weight: bold;
}

/* ألوان مخصصة للأدوات */
.tool-card.cleanup .tool-icon {
    background: linear-gradient(45deg, #ff6b6b, #ee5a52);
}

.tool-card.backup .tool-icon {
    background: linear-gradient(45deg, #00ff88, #00d4aa);
}

.tool-card.analytics .tool-icon {
    background: linear-gradient(45deg, #667eea, #764ba2);
}

.tool-card.bots .tool-icon {
    background: linear-gradient(45deg, #feca57, #ff9ff3);
}

.tool-card.security .tool-icon {
    background: linear-gradient(45deg, #ff6b6b, #ee5a52);
}

.tool-card.performance .tool-icon {
    background: linear-gradient(45deg, #4ecdc4, #44a08d);
}

.tool-card.files .tool-icon {
    background: linear-gradient(45deg, #a8edea, #fed6e3);
}

.tool-card.reports .tool-icon {
    background: linear-gradient(45deg, #667eea, #764ba2);
}

.tool-card.maintenance .tool-icon {
    background: linear-gradient(45deg, #feca57, #ff9ff3);
}

@media (max-width: 768px) {
    .tools-grid {
        grid-template-columns: 1fr;
    }
    
    .tool-item {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .tool-info {
        text-align: center;
    }
}
</style>

<script src="/static/js/tools.js"></script>

<%- include('../partials/footer') %>
