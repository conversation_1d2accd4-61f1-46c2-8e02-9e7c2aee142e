// ========================================
// 💰 نموذج نظام العملات - CS Discord
// ========================================
// 📺 يوتيوب: https://www.youtube.com/@CS_Discord
// 💬 ديسكورد: https://discord.gg/yqTn2EwVsd
// ========================================

const mongoose = require('mongoose');

// نموذج العملات للمستخدمين
const currencySchema = new mongoose.Schema({
  // معرف المستخدم
  userId: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  
  // معرف الخادم
  guildId: {
    type: String,
    required: true,
    index: true
  },
  
  // الرصيد الحالي
  balance: {
    type: Number,
    default: 0,
    min: 0
  },
  
  // الراتب اليومي
  dailyReward: {
    lastClaimed: {
      type: Date,
      default: null
    },
    streak: {
      type: Number,
      default: 0
    },
    totalClaimed: {
      type: Number,
      default: 0
    }
  },
  
  // الراتب الأسبوعي
  weeklyReward: {
    lastClaimed: {
      type: Date,
      default: null
    },
    totalClaimed: {
      type: Number,
      default: 0
    }
  },
  
  // إحصائيات العمل
  work: {
    lastWorked: {
      type: Date,
      default: null
    },
    totalWorked: {
      type: Number,
      default: 0
    },
    totalEarned: {
      type: Number,
      default: 0
    }
  },
  
  // إحصائيات المقامرة
  gambling: {
    totalBet: {
      type: Number,
      default: 0
    },
    totalWon: {
      type: Number,
      default: 0
    },
    totalLost: {
      type: Number,
      default: 0
    },
    winStreak: {
      type: Number,
      default: 0
    },
    loseStreak: {
      type: Number,
      default: 0
    }
  },
  
  // المعاملات
  transactions: [{
    type: {
      type: String,
      enum: ['daily', 'weekly', 'work', 'gambling', 'transfer', 'shop', 'admin'],
      required: true
    },
    amount: {
      type: Number,
      required: true
    },
    description: {
      type: String,
      required: true
    },
    timestamp: {
      type: Date,
      default: Date.now
    },
    fromUser: String,
    toUser: String
  }],
  
  // الإنجازات
  achievements: [{
    name: String,
    description: String,
    reward: Number,
    unlockedAt: {
      type: Date,
      default: Date.now
    }
  }],
  
  // إعدادات المستخدم
  settings: {
    notifications: {
      type: Boolean,
      default: true
    },
    publicProfile: {
      type: Boolean,
      default: true
    }
  }
}, {
  timestamps: true
});

// فهرس مركب للبحث السريع
currencySchema.index({ userId: 1, guildId: 1 });

// دوال مساعدة
currencySchema.methods.addBalance = function(amount, type = 'admin', description = 'إضافة رصيد') {
  this.balance += amount;
  this.transactions.push({
    type,
    amount,
    description,
    timestamp: new Date()
  });
  return this.save();
};

currencySchema.methods.removeBalance = function(amount, type = 'admin', description = 'خصم رصيد') {
  if (this.balance < amount) {
    throw new Error('الرصيد غير كافي');
  }
  this.balance -= amount;
  this.transactions.push({
    type,
    amount: -amount,
    description,
    timestamp: new Date()
  });
  return this.save();
};

currencySchema.methods.canClaimDaily = function() {
  if (!this.dailyReward.lastClaimed) return true;
  
  const now = new Date();
  const lastClaimed = new Date(this.dailyReward.lastClaimed);
  const diffTime = Math.abs(now - lastClaimed);
  const diffHours = Math.ceil(diffTime / (1000 * 60 * 60));
  
  return diffHours >= 24;
};

currencySchema.methods.canClaimWeekly = function() {
  if (!this.weeklyReward.lastClaimed) return true;
  
  const now = new Date();
  const lastClaimed = new Date(this.weeklyReward.lastClaimed);
  const diffTime = Math.abs(now - lastClaimed);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  return diffDays >= 7;
};

currencySchema.methods.canWork = function() {
  if (!this.work.lastWorked) return true;
  
  const now = new Date();
  const lastWorked = new Date(this.work.lastWorked);
  const diffTime = Math.abs(now - lastWorked);
  const diffHours = Math.ceil(diffTime / (1000 * 60 * 60));
  
  return diffHours >= 1; // يمكن العمل كل ساعة
};

currencySchema.methods.claimDaily = function() {
  if (!this.canClaimDaily()) {
    throw new Error('لا يمكنك استلام الراتب اليومي الآن');
  }
  
  // حساب المكافأة بناءً على التتالي
  let reward = 100; // المكافأة الأساسية
  
  // فحص التتالي
  const now = new Date();
  const lastClaimed = this.dailyReward.lastClaimed ? new Date(this.dailyReward.lastClaimed) : null;
  
  if (lastClaimed) {
    const diffTime = Math.abs(now - lastClaimed);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 1) {
      // استمرار التتالي
      this.dailyReward.streak += 1;
    } else if (diffDays > 1) {
      // انقطاع التتالي
      this.dailyReward.streak = 1;
    }
  } else {
    this.dailyReward.streak = 1;
  }
  
  // مكافأة إضافية للتتالي
  reward += Math.min(this.dailyReward.streak * 10, 500); // حد أقصى 500 مكافأة إضافية
  
  this.balance += reward;
  this.dailyReward.lastClaimed = now;
  this.dailyReward.totalClaimed += 1;
  
  this.transactions.push({
    type: 'daily',
    amount: reward,
    description: `راتب يومي - يوم ${this.dailyReward.streak}`,
    timestamp: now
  });
  
  return { reward, streak: this.dailyReward.streak };
};

currencySchema.methods.doWork = function() {
  if (!this.canWork()) {
    throw new Error('لا يمكنك العمل الآن، انتظر ساعة');
  }

  // أعمال مختلفة بمكافآت مختلفة
  const jobs = [
    { name: 'برمجة', reward: [50, 150], description: 'عملت كمبرمج' },
    { name: 'تصميم', reward: [40, 120], description: 'عملت كمصمم' },
    { name: 'كتابة', reward: [30, 100], description: 'عملت ككاتب' },
    { name: 'تدريس', reward: [60, 140], description: 'عملت كمدرس' },
    { name: 'ترجمة', reward: [35, 110], description: 'عملت كمترجم' }
  ];

  const randomJob = jobs[Math.floor(Math.random() * jobs.length)];
  const reward = Math.floor(Math.random() * (randomJob.reward[1] - randomJob.reward[0] + 1)) + randomJob.reward[0];

  this.balance += reward;
  this.work.lastWorked = new Date();
  this.work.totalWorked += 1;
  this.work.totalEarned += reward;

  this.transactions.push({
    type: 'work',
    amount: reward,
    description: randomJob.description,
    timestamp: new Date()
  });

  return { job: randomJob.name, reward, description: randomJob.description };
};

// دالة للحصول على أو إنشاء ملف المستخدم
currencySchema.statics.getOrCreate = async function(userId, guildId) {
  let user = await this.findOne({ userId, guildId });
  
  if (!user) {
    user = new this({
      userId,
      guildId,
      balance: 1000 // رصيد ابتدائي
    });
    await user.save();
  }
  
  return user;
};

module.exports = mongoose.model('Currency', currencySchema);
