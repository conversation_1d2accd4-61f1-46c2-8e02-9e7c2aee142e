# 🚀 دليل التثبيت السريع

## 📋 المتطلبات الأساسية

قبل البدء، تأكد من تثبيت:
- [Node.js](https://nodejs.org/) (الإصدار 16 أو أحدث)
- [MongoDB](https://www.mongodb.com/) أو حساب [MongoDB Atlas](https://www.mongodb.com/atlas)
- حساب [Discord Developer](https://discord.com/developers/applications)

## ⚡ التثبيت السريع

### 1. تحميل المشروع
```bash
git clone https://github.com/your-repo/discord-bot-dashboard.git
cd discord-bot-dashboard
```

### 2. تثبيت المكتبات
```bash
npm install
```

### 3. إعداد الإعدادات
```bash
# تعديل ملف الإعدادات
notepad config.js  # في Windows
nano config.js     # في Linux/Mac
code config.js     # VS Code
```

**ملاحظة مهمة:** جميع الإعدادات موجودة في ملف `config.js` - لا تحتاج لإنشاء ملف `.env`

### 4. إعداد بوت Discord

1. اذهب إلى [Discord Developer Portal](https://discord.com/developers/applications)
2. أنشئ تطبيق جديد
3. اذهب إلى قسم "Bot" وأنشئ بوت
4. انسخ التوكن وضعه في `BOT_TOKEN`
5. في قسم "OAuth2"، أضف رابط إعادة التوجيه:
   ```
   http://localhost:3000/auth/discord/callback
   ```

### 5. تشغيل المشروع
```bash
# للتطوير
npm run dev

# أو للإنتاج
npm start

# أو استخدم ملف التشغيل السريع
./start.sh      # Linux/Mac
start.bat       # Windows
```

## 🔧 الإعدادات المطلوبة في config.js

افتح ملف `config.js` وعدّل الإعدادات التالية:

```javascript
// إعدادات البوت الأساسية
bot: {
  token: 'your_bot_token_here',
  clientId: 'your_client_id_here',
  ownerId: 'your_discord_user_id'
},

// إعدادات OAuth2
oauth: {
  clientSecret: 'your_client_secret_here'
},

// إعدادات قاعدة البيانات
database: {
  mongoUri: 'your_mongodb_connection_string'
}
```

## 🌐 الوصول للوحة التحكم

بعد التشغيل، افتح المتصفح واذهب إلى:
```
http://localhost:3000
```

## 🤖 إضافة البوت للخادم

استخدم هذا الرابط لإضافة البوت (استبدل CLIENT_ID):
```
https://discord.com/api/oauth2/authorize?client_id=CLIENT_ID&permissions=8&scope=bot%20applications.commands
```

## ❗ حل المشاكل الشائعة

### خطأ في الاتصال بقاعدة البيانات
```bash
# تأكد من تشغيل MongoDB
sudo systemctl start mongod  # Linux
brew services start mongodb  # Mac
# أو استخدم MongoDB Atlas
```

### خطأ في تسجيل الدخول
- تأكد من صحة `CLIENT_ID` و `CLIENT_SECRET`
- تأكد من إضافة رابط إعادة التوجيه في Discord Developer Portal

### خطأ في الصلاحيات
- تأكد من إضافة `OWNER_ID` في ملف `.env`
- تأكد من أن البوت لديه صلاحيات كافية في الخادم

## 📞 الدعم الفني

إذا واجهت أي مشكلة:

📺 **يوتيوب**: [https://www.youtube.com/@CS_Discord](https://www.youtube.com/@CS_Discord)
💬 **ديسكورد**: [https://discord.gg/yqTn2EwVsd](https://discord.gg/yqTn2EwVsd)

## 🔒 ملاحظات الأمان

- لا تشارك ملف `.env` مع أي شخص
- استخدم كلمات مرور قوية للمفاتيح السرية
- فعّل المصادقة الثنائية لحساب Discord
- استخدم HTTPS في الإنتاج

## 🎯 الخطوات التالية

بعد التثبيت الناجح:

1. سجل دخولك عبر Discord
2. أضف البوت لخادمك
3. اذهب إلى إعدادات الخادم في اللوحة
4. فعّل الميزات المطلوبة
5. خصص الإعدادات حسب احتياجاتك

---

**تم التطوير بواسطة CS Discord** ❤️
