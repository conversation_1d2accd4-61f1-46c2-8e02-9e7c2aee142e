<% layout('layout') -%>

<div class="main-content">
    <div class="content-header">
        <h1 class="page-title">
            <i class="fas fa-cog"></i>
            الإعدادات العامة
        </h1>
        <p class="page-description">
            تخصيص البريفكس، اللغة، والإعدادات الأساسية للبوت
        </p>
    </div>

    <!-- إعدادات البوت الأساسية -->
    <div class="card">
        <div class="card-header">
            <div class="card-icon">
                <i class="fas fa-robot"></i>
            </div>
            <div>
                <h3 class="card-title">إعدادات البوت</h3>
            </div>
        </div>
        <div class="card-body">
            <form id="botSettingsForm">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">بريفكس البوت</label>
                            <input type="text" class="form-control" id="botPrefix" value="!" maxlength="5">
                            <small class="form-text">الرمز المستخدم قبل الأوامر (مثال: !help)</small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">لغة البوت</label>
                            <select class="form-control" id="botLanguage">
                                <option value="ar" selected>العربية</option>
                                <option value="en">English</option>
                                <option value="fr">Français</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">حالة البوت</label>
                            <select class="form-control" id="botStatus">
                                <option value="online">متصل</option>
                                <option value="idle">خامل</option>
                                <option value="dnd">مشغول</option>
                                <option value="invisible">غير مرئي</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">نشاط البوت</label>
                            <input type="text" class="form-control" id="botActivity" value="CS Discord Dashboard" placeholder="مثال: يلعب Minecraft">
                        </div>
                    </div>
                </div>
                
                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="enableCommands" checked>
                        <label class="form-check-label" for="enableCommands">
                            تفعيل الأوامر العامة
                        </label>
                    </div>
                </div>
                
                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="enableLogs" checked>
                        <label class="form-check-label" for="enableLogs">
                            تفعيل سجلات النظام
                        </label>
                    </div>
                </div>
                
                <button type="button" class="btn btn-success" onclick="saveSettings()">
                    <i class="fas fa-save"></i>
                    حفظ الإعدادات
                </button>
            </form>
        </div>
    </div>

    <!-- إعدادات الخادم -->
    <div class="card">
        <div class="card-header">
            <div class="card-icon">
                <i class="fas fa-server"></i>
            </div>
            <div>
                <h3 class="card-title">إعدادات الخادم</h3>
            </div>
        </div>
        <div class="card-body">
            <form id="serverSettingsForm">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">قناة الترحيب</label>
                            <select class="form-control" id="welcomeChannel">
                                <option value="">اختر قناة...</option>
                                <option value="general">العام</option>
                                <option value="welcome">الترحيب</option>
                                <option value="lobby">اللوبي</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">قناة السجلات</label>
                            <select class="form-control" id="logsChannel">
                                <option value="">اختر قناة...</option>
                                <option value="logs">السجلات</option>
                                <option value="admin-logs">سجلات الإدارة</option>
                                <option value="mod-logs">سجلات المشرفين</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">رتبة المشرفين</label>
                            <select class="form-control" id="modRole">
                                <option value="">اختر رتبة...</option>
                                <option value="moderator">مشرف</option>
                                <option value="admin">إدارة</option>
                                <option value="staff">طاقم</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">رتبة الأعضاء</label>
                            <select class="form-control" id="memberRole">
                                <option value="">اختر رتبة...</option>
                                <option value="member">عضو</option>
                                <option value="verified">موثق</option>
                                <option value="user">مستخدم</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="autoRole" checked>
                        <label class="form-check-label" for="autoRole">
                            منح رتبة تلقائية للأعضاء الجدد
                        </label>
                    </div>
                </div>
                
                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="antiSpam" checked>
                        <label class="form-check-label" for="antiSpam">
                            تفعيل حماية ضد السبام
                        </label>
                    </div>
                </div>
                
                <button type="button" class="btn btn-success" onclick="saveServerSettings()">
                    <i class="fas fa-save"></i>
                    حفظ إعدادات الخادم
                </button>
            </form>
        </div>
    </div>

    <!-- إعدادات الأمان -->
    <div class="card">
        <div class="card-header">
            <div class="card-icon">
                <i class="fas fa-shield-alt"></i>
            </div>
            <div>
                <h3 class="card-title">إعدادات الأمان</h3>
            </div>
        </div>
        <div class="card-body">
            <form id="securitySettingsForm">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">مستوى التحقق المطلوب</label>
                            <select class="form-control" id="verificationLevel">
                                <option value="0">بدون تحقق</option>
                                <option value="1" selected>بريد إلكتروني موثق</option>
                                <option value="2">مسجل لأكثر من 5 دقائق</option>
                                <option value="3">عضو في الخادم لأكثر من 10 دقائق</option>
                                <option value="4">رقم هاتف موثق</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">فلتر المحتوى الصريح</label>
                            <select class="form-control" id="explicitFilter">
                                <option value="0">معطل</option>
                                <option value="1" selected>الأعضاء بدون رتبة</option>
                                <option value="2">جميع الأعضاء</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="enableRaid" checked>
                        <label class="form-check-label" for="enableRaid">
                            تفعيل حماية ضد الغارات
                        </label>
                    </div>
                </div>
                
                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="enableAntiBot" checked>
                        <label class="form-check-label" for="enableAntiBot">
                            تفعيل حماية ضد البوتات المزيفة
                        </label>
                    </div>
                </div>
                
                <button type="button" class="btn btn-success" onclick="saveSecuritySettings()">
                    <i class="fas fa-shield-alt"></i>
                    حفظ إعدادات الأمان
                </button>
            </form>
        </div>
    </div>
</div>

<script>
// حفظ إعدادات البوت
function saveSettings() {
    const settings = {
        prefix: document.getElementById('botPrefix').value,
        language: document.getElementById('botLanguage').value,
        status: document.getElementById('botStatus').value,
        activity: document.getElementById('botActivity').value,
        enableCommands: document.getElementById('enableCommands').checked,
        enableLogs: document.getElementById('enableLogs').checked
    };
    
    console.log('حفظ إعدادات البوت:', settings);
    showNotification('✅ تم حفظ إعدادات البوت بنجاح!', 'success');
}

// حفظ إعدادات الخادم
function saveServerSettings() {
    const settings = {
        welcomeChannel: document.getElementById('welcomeChannel').value,
        logsChannel: document.getElementById('logsChannel').value,
        modRole: document.getElementById('modRole').value,
        memberRole: document.getElementById('memberRole').value,
        autoRole: document.getElementById('autoRole').checked,
        antiSpam: document.getElementById('antiSpam').checked
    };
    
    console.log('حفظ إعدادات الخادم:', settings);
    showNotification('✅ تم حفظ إعدادات الخادم بنجاح!', 'success');
}

// حفظ إعدادات الأمان
function saveSecuritySettings() {
    const settings = {
        verificationLevel: document.getElementById('verificationLevel').value,
        explicitFilter: document.getElementById('explicitFilter').value,
        enableRaid: document.getElementById('enableRaid').checked,
        enableAntiBot: document.getElementById('enableAntiBot').checked
    };
    
    console.log('حفظ إعدادات الأمان:', settings);
    showNotification('✅ تم حفظ إعدادات الأمان بنجاح!', 'success');
}

// إظهار الإشعارات
function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type} notification`;
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        animation: slideIn 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}
</script>
