<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> - <%= config.dashboard.title %></title>
    
    <!-- CSS Libraries -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #ffffff;
        }
        
        .login-container {
            max-width: 450px;
            width: 100%;
            margin: 0 auto;
            padding: 20px;
        }
        
        .login-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 50px 40px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }
        
        .login-logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 30px;
            background: linear-gradient(135deg, #7289da, #99aab5);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            color: white;
            box-shadow: 0 10px 30px rgba(114, 137, 218, 0.4);
        }
        
        .login-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 10px;
            color: #ffffff;
        }
        
        .login-subtitle {
            font-size: 1rem;
            opacity: 0.8;
            margin-bottom: 40px;
            line-height: 1.6;
        }
        
        .discord-btn {
            background: #7289da;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 12px;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(114, 137, 218, 0.4);
            width: 100%;
            justify-content: center;
        }
        
        .discord-btn:hover {
            background: #677bc4;
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(114, 137, 218, 0.6);
            color: white;
        }
        
        .discord-btn:active {
            transform: translateY(0);
        }
        
        .discord-btn i {
            font-size: 1.3rem;
        }
        
        .divider {
            margin: 30px 0;
            position: relative;
            text-align: center;
        }
        
        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: rgba(255, 255, 255, 0.2);
        }
        
        .divider span {
            background: rgba(255, 255, 255, 0.1);
            padding: 0 20px;
            font-size: 0.9rem;
            opacity: 0.7;
        }
        
        .features-list {
            text-align: right;
            margin-top: 30px;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            font-size: 0.95rem;
            opacity: 0.8;
        }
        
        .feature-item i {
            color: #43b581;
            margin-left: 12px;
            width: 16px;
        }
        
        .back-home {
            position: absolute;
            top: 30px;
            right: 30px;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }
        
        .back-home:hover {
            color: white;
            transform: translateX(5px);
        }
        
        .alert {
            background: rgba(240, 71, 71, 0.2);
            border: 1px solid rgba(240, 71, 71, 0.3);
            color: #f04747;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 25px;
            text-align: center;
        }
        
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }
        
        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid #7289da;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @media (max-width: 576px) {
            .login-container {
                padding: 15px;
            }
            
            .login-card {
                padding: 40px 25px;
            }
            
            .login-title {
                font-size: 1.7rem;
            }
            
            .back-home {
                top: 20px;
                right: 20px;
            }
        }
        
        .floating-shapes {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            pointer-events: none;
        }
        
        .shape {
            position: absolute;
            opacity: 0.1;
            animation: float 8s ease-in-out infinite;
        }
        
        .shape:nth-child(1) {
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }
        
        .shape:nth-child(2) {
            top: 20%;
            right: 10%;
            animation-delay: 2s;
        }
        
        .shape:nth-child(3) {
            bottom: 20%;
            left: 15%;
            animation-delay: 4s;
        }
        
        .shape:nth-child(4) {
            bottom: 10%;
            right: 20%;
            animation-delay: 6s;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-30px) rotate(180deg); }
        }
    </style>
</head>
<body>
    <div class="floating-shapes">
        <i class="fas fa-robot shape" style="font-size: 3rem;"></i>
        <i class="fas fa-cog shape" style="font-size: 2rem;"></i>
        <i class="fas fa-shield-alt shape" style="font-size: 2.5rem;"></i>
        <i class="fas fa-users shape" style="font-size: 2rem;"></i>
    </div>
    
    <a href="/" class="back-home">
        <i class="fas fa-arrow-right"></i>
        العودة للرئيسية
    </a>
    
    <div class="login-container">
        <div class="login-card">
            <div class="login-logo">
                <i class="fas fa-robot"></i>
            </div>
            
            <h1 class="login-title">تسجيل الدخول</h1>
            <p class="login-subtitle">
                سجل دخولك باستخدام حساب Discord للوصول إلى لوحة تحكم البوت
            </p>
            
            <% if (locals.error) { %>
                <div class="alert">
                    <i class="fas fa-exclamation-triangle"></i>
                    <% if (error === 'auth_failed') { %>
                        فشل في تسجيل الدخول. يرجى المحاولة مرة أخرى.
                    <% } else if (error === 'login_error') { %>
                        حدث خطأ أثناء تسجيل الدخول. يرجى المحاولة لاحقاً.
                    <% } else { %>
                        حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.
                    <% } %>
                </div>
            <% } %>
            
            <a href="/auth/discord" class="discord-btn" onclick="showLoading()">
                <i class="fab fa-discord"></i>
                تسجيل الدخول عبر Discord
            </a>
            
            <div class="divider">
                <span>ميزات لوحة التحكم</span>
            </div>
            
            <div class="features-list">
                <div class="feature-item">
                    <i class="fas fa-check"></i>
                    إدارة شاملة للخادم
                </div>
                <div class="feature-item">
                    <i class="fas fa-check"></i>
                    نظام تذاكر متطور
                </div>
                <div class="feature-item">
                    <i class="fas fa-check"></i>
                    أدوات مودريشن قوية
                </div>
                <div class="feature-item">
                    <i class="fas fa-check"></i>
                    نظام مستويات تفاعلي
                </div>
                <div class="feature-item">
                    <i class="fas fa-check"></i>
                    واجهة عربية سهلة
                </div>
            </div>
        </div>
    </div>
    
    <div class="loading-overlay" id="loadingOverlay">
        <div class="text-center">
            <div class="loading-spinner"></div>
            <p class="mt-3">جاري تسجيل الدخول...</p>
        </div>
    </div>
    
    <script>
        function showLoading() {
            document.getElementById('loadingOverlay').style.display = 'flex';
        }
        
        // إخفاء التحميل إذا عاد المستخدم للصفحة
        window.addEventListener('pageshow', function() {
            document.getElementById('loadingOverlay').style.display = 'none';
        });
        
        // إضافة تأثيرات تفاعلية
        document.addEventListener('DOMContentLoaded', function() {
            const card = document.querySelector('.login-card');
            
            // تأثير الحركة عند تحريك الماوس
            card.addEventListener('mousemove', function(e) {
                const rect = card.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                
                const centerX = rect.width / 2;
                const centerY = rect.height / 2;
                
                const rotateX = (y - centerY) / 20;
                const rotateY = (centerX - x) / 20;
                
                card.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg)`;
            });
            
            card.addEventListener('mouseleave', function() {
                card.style.transform = 'perspective(1000px) rotateX(0deg) rotateY(0deg)';
            });
        });
    </script>
</body>
</html>
