<%- include('../partials/header') %>

<div class="main-content">
    <div class="content-header">
        <h1 class="page-title">
            <i class="fas fa-server"></i>
            اختيار الخادم
        </h1>
        <p class="page-description">
            اختر الخادم الذي تريد إدارته من خلال لوحة التحكم
        </p>
    </div>

    <!-- فلاتر الخوادم -->
    <div class="guild-filters">
        <div class="filter-group">
            <label>البحث:</label>
            <input type="text" class="form-control" id="guild-search" placeholder="ابحث عن خادم..." onkeyup="filterGuilds()">
        </div>

        <div class="filter-group">
            <label>الفلتر:</label>
            <select class="form-control" id="guild-filter" onchange="filterGuilds()">
                <option value="all">جميع الخوادم</option>
                <option value="owner">مالك فقط</option>
                <option value="admin">أدمن فقط</option>
                <option value="online">متصل فقط</option>
                <option value="offline">غير متصل فقط</option>
            </select>
        </div>

        <div class="filter-group">
            <button class="btn btn-primary" onclick="refreshGuilds()">
                <i class="fas fa-sync"></i>
                تحديث
            </button>
        </div>
    </div>

    <!-- قائمة الخوادم -->
    <div class="guilds-container" id="guilds-container">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            جاري تحميل الخوادم...
                                </div>
                                <div class="guild-info">
                                    <h5 class="guild-name">${guild.guildName || 'خادم غير معروف'}</h5>
                                    <p class="guild-id">ID: ${guild.guildId}</p>
                                </div>
                                <div class="guild-status">
                                    <span class="status-badge online">
                                        <i class="fas fa-circle"></i>
                                        متصل
                                    </span>
                                </div>
                            </div>
                            
                            <div class="guild-permissions">
                                <h6>صلاحياتك:</h6>
                                <div class="permissions-list">
                                    ${guild.permissions.owner ? '<span class="permission-badge owner"><i class="fas fa-crown"></i> مالك</span>' : ''}
                                    ${guild.permissions.admin ? '<span class="permission-badge admin"><i class="fas fa-user-shield"></i> مدير</span>' : ''}
                                    ${guild.permissions.moderator ? '<span class="permission-badge moderator"><i class="fas fa-gavel"></i> مشرف</span>' : ''}
                                    ${guild.permissions.canManageSettings ? '<span class="permission-badge setting"><i class="fas fa-cog"></i> إعدادات</span>' : ''}
                                    ${guild.permissions.canManageModeration ? '<span class="permission-badge moderation"><i class="fas fa-shield-alt"></i> مودريشن</span>' : ''}
                                    ${guild.permissions.canManageTickets ? '<span class="permission-badge tickets"><i class="fas fa-ticket-alt"></i> تذاكر</span>' : ''}
                                </div>
                            </div>
                            
                            <div class="guild-stats">
                                <div class="stat-item">
                                    <i class="fas fa-users"></i>
                                    <span>-- عضو</span>
                                </div>
                                <div class="stat-item">
                                    <i class="fas fa-ticket-alt"></i>
                                    <span>-- تذكرة</span>
                                </div>
                                <div class="stat-item">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    <span>-- إنذار</span>
                                </div>
                            </div>
                            
                            <div class="guild-actions">
                                <a href="/dashboard/guild/${guild.guildId}" class="btn btn-primary btn-manage">
                                    <i class="fas fa-cog"></i>
                                    إدارة الخادم
                                </a>
                                <div class="quick-actions">
                                    <a href="/dashboard/guild/${guild.guildId}/settings" class="quick-action" title="الإعدادات">
                                        <i class="fas fa-cog"></i>
                                    </a>
                                    <a href="/dashboard/guild/${guild.guildId}/moderation" class="quick-action" title="المودريشن">
                                        <i class="fas fa-shield-alt"></i>
                                    </a>
                                    <a href="/dashboard/guild/${guild.guildId}/tickets" class="quick-action" title="التذاكر">
                                        <i class="fas fa-ticket-alt"></i>
                                    </a>
                                    <a href="/dashboard/guild/${guild.guildId}/logs" class="quick-action" title="السجلات">
                                        <i class="fas fa-file-alt"></i>
                                    </a>
                                </div>
                            </div>
                            
                            <div class="guild-footer">
                                <small class="text-muted">
                                    <i class="fas fa-clock"></i>
                                    تمت الإضافة: ${new Date(guild.addedAt).toLocaleDateString('ar-SA')}
                                </small>
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>
        </div>
    ` : `
        <div class="empty-guilds">
            <div class="empty-state">
                <div class="empty-icon">
                    <i class="fas fa-server"></i>
                </div>
                <h3>لا توجد خوادم متاحة</h3>
                <p>لم يتم منحك صلاحيات لإدارة أي خادم بعد.</p>
                <div class="empty-actions">
                    <a href="https://discord.com/api/oauth2/authorize?client_id=${config.bot.clientId}&permissions=8&scope=bot%20applications.commands" 
                       class="btn btn-primary btn-lg" target="_blank">
                        <i class="fas fa-plus"></i>
                        إضافة البوت لخادمك
                    </a>
                    <a href="/dashboard" class="btn btn-outline-secondary btn-lg">
                        <i class="fas fa-arrow-right"></i>
                        العودة للرئيسية
                    </a>
                </div>
            </div>
        </div>
    `}
</div>

<style>
.page-header {
    text-align: center;
    margin-bottom: 40px;
}

.page-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 10px;
}

.page-subtitle {
    font-size: 1.1rem;
    color: #b9bbbe;
    margin: 0;
}

.guild-selection-card {
    background: #40444b;
    border-radius: 15px;
    padding: 25px;
    border: 1px solid #2c2f33;
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.guild-selection-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.3);
    border-color: #7289da;
}

.guild-header {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    margin-bottom: 20px;
}

.guild-avatar {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #7289da, #99aab5);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.guild-info {
    flex: 1;
}

.guild-name {
    color: #ffffff;
    font-weight: 600;
    margin: 0 0 5px 0;
    font-size: 1.2rem;
}

.guild-id {
    color: #72767d;
    font-size: 0.85rem;
    margin: 0;
    font-family: 'Courier New', monospace;
}

.guild-status {
    flex-shrink: 0;
}

.status-badge {
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 5px;
}

.status-badge.online {
    background: rgba(67, 181, 129, 0.2);
    color: #43b581;
    border: 1px solid rgba(67, 181, 129, 0.3);
}

.status-badge.offline {
    background: rgba(114, 118, 125, 0.2);
    color: #72767d;
    border: 1px solid rgba(114, 118, 125, 0.3);
}

.guild-permissions {
    margin-bottom: 20px;
}

.guild-permissions h6 {
    color: #b9bbbe;
    font-size: 0.9rem;
    margin-bottom: 10px;
    font-weight: 600;
}

.permissions-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.permission-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 4px;
}

.permission-badge.owner {
    background: rgba(255, 193, 7, 0.2);
    color: #ffc107;
    border: 1px solid rgba(255, 193, 7, 0.3);
}

.permission-badge.admin {
    background: rgba(220, 53, 69, 0.2);
    color: #dc3545;
    border: 1px solid rgba(220, 53, 69, 0.3);
}

.permission-badge.moderator {
    background: rgba(0, 123, 255, 0.2);
    color: #007bff;
    border: 1px solid rgba(0, 123, 255, 0.3);
}

.permission-badge.setting,
.permission-badge.moderation,
.permission-badge.tickets {
    background: rgba(108, 117, 125, 0.2);
    color: #6c757d;
    border: 1px solid rgba(108, 117, 125, 0.3);
}

.guild-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    padding: 15px;
    background: #36393f;
    border-radius: 10px;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    color: #b9bbbe;
    font-size: 0.85rem;
}

.stat-item i {
    color: #7289da;
    font-size: 1.1rem;
}

.guild-actions {
    margin-top: auto;
    margin-bottom: 15px;
}

.btn-manage {
    width: 100%;
    padding: 12px;
    font-weight: 600;
    margin-bottom: 15px;
}

.quick-actions {
    display: flex;
    justify-content: center;
    gap: 10px;
}

.quick-action {
    width: 40px;
    height: 40px;
    background: #36393f;
    border: 1px solid #2c2f33;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #b9bbbe;
    text-decoration: none;
    transition: all 0.3s ease;
}

.quick-action:hover {
    background: #7289da;
    color: white;
    border-color: #7289da;
}

.guild-footer {
    text-align: center;
    padding-top: 15px;
    border-top: 1px solid #2c2f33;
}

.empty-guilds {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 60vh;
}

.empty-state {
    text-align: center;
    max-width: 500px;
}

.empty-icon {
    width: 120px;
    height: 120px;
    background: #36393f;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 30px;
    font-size: 3rem;
    color: #72767d;
}

.empty-state h3 {
    color: #ffffff;
    font-weight: 600;
    margin-bottom: 15px;
}

.empty-state p {
    color: #b9bbbe;
    font-size: 1.1rem;
    margin-bottom: 30px;
    line-height: 1.6;
}

.empty-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.empty-actions .btn {
    min-width: 200px;
}

@media (max-width: 768px) {
    .guild-header {
        flex-direction: column;
        text-align: center;
    }
    
    .guild-stats {
        flex-direction: column;
        gap: 15px;
    }
    
    .stat-item {
        flex-direction: row;
        justify-content: space-between;
    }
    
    .empty-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .empty-actions .btn {
        width: 100%;
        max-width: 300px;
    }
}
</style>

<script>
// تحديث إحصائيات الخوادم (يمكن ربطها بـ API لاحقاً)
document.addEventListener('DOMContentLoaded', function() {
    // هنا يمكن إضافة كود لجلب الإحصائيات الفعلية من API
    console.log('تم تحميل صفحة اختيار الخادم');
});
</script>
` }) %>
