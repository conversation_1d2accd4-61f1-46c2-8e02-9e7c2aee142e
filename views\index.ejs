<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    
    <!-- CSS Libraries -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #ffffff;
        }
        
        .hero-section {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 50px 0;
        }
        
        .hero-content {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .hero-title {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .hero-subtitle {
            font-size: 1.3rem;
            margin-bottom: 40px;
            opacity: 0.9;
        }
        
        .hero-description {
            font-size: 1.1rem;
            margin-bottom: 50px;
            line-height: 1.8;
            opacity: 0.8;
        }
        
        .cta-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn-hero {
            padding: 15px 40px;
            font-size: 1.1rem;
            font-weight: 600;
            border-radius: 50px;
            text-decoration: none;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }
        
        .btn-primary-hero {
            background: #7289da;
            color: white;
            box-shadow: 0 8px 25px rgba(114, 137, 218, 0.4);
        }
        
        .btn-primary-hero:hover {
            background: #677bc4;
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(114, 137, 218, 0.6);
            color: white;
        }
        
        .btn-outline-hero {
            background: transparent;
            color: white;
            border: 2px solid rgba(255,255,255,0.3);
        }
        
        .btn-outline-hero:hover {
            background: rgba(255,255,255,0.1);
            border-color: rgba(255,255,255,0.5);
            transform: translateY(-2px);
            color: white;
        }
        
        .features-section {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            padding: 80px 0;
            margin-top: 100px;
        }
        
        .feature-card {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px 30px;
            text-align: center;
            height: 100%;
            border: 1px solid rgba(255,255,255,0.2);
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }
        
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            color: #ffd700;
        }
        
        .feature-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 15px;
        }
        
        .feature-description {
            opacity: 0.8;
            line-height: 1.6;
        }
        
        .stats-section {
            padding: 60px 0;
            text-align: center;
        }
        
        .stat-item {
            margin-bottom: 30px;
        }
        
        .stat-number {
            font-size: 3rem;
            font-weight: 700;
            color: #ffd700;
            display: block;
        }
        
        .stat-label {
            font-size: 1.1rem;
            opacity: 0.8;
        }
        
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }
            
            .hero-subtitle {
                font-size: 1.1rem;
            }
            
            .cta-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .btn-hero {
                width: 100%;
                max-width: 300px;
            }
        }
        
        .floating-elements {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            pointer-events: none;
        }
        
        .floating-element {
            position: absolute;
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
    </style>
</head>
<body>
    <div class="floating-elements">
        <i class="fas fa-robot floating-element" style="top: 10%; left: 10%; font-size: 2rem; animation-delay: 0s;"></i>
        <i class="fas fa-cog floating-element" style="top: 20%; right: 15%; font-size: 1.5rem; animation-delay: 1s;"></i>
        <i class="fas fa-shield-alt floating-element" style="top: 60%; left: 5%; font-size: 2.5rem; animation-delay: 2s;"></i>
        <i class="fas fa-chart-line floating-element" style="top: 70%; right: 10%; font-size: 2rem; animation-delay: 3s;"></i>
        <i class="fas fa-users floating-element" style="top: 40%; left: 80%; font-size: 1.8rem; animation-delay: 4s;"></i>
    </div>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="hero-content">
                <h1 class="hero-title">
                    <i class="fas fa-robot"></i>
                    مرحباً بك في لوحة تحكم البوت
                </h1>
                
                <p class="hero-subtitle">
                    <%= description %>
                </p>
                
                <p class="hero-description">
                    لوحة تحكم شاملة ومتطورة لإدارة بوت ديسكورد الخاص بك بسهولة ومرونة. 
                    تحكم في جميع ميزات البوت من مكان واحد مع واجهة عربية سهلة الاستخدام.
                </p>
                
                <div class="cta-buttons">
                    <a href="/auth/discord" class="btn-hero btn-primary-hero">
                        <i class="fab fa-discord"></i>
                        تسجيل الدخول عبر Discord
                    </a>
                    
                    <a href="#features" class="btn-hero btn-outline-hero">
                        <i class="fas fa-info-circle"></i>
                        تعرف على الميزات
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features-section" id="features">
        <div class="container">
            <div class="row text-center mb-5">
                <div class="col-12">
                    <h2 class="display-4 fw-bold mb-3">ميزات رائعة</h2>
                    <p class="lead">اكتشف جميع الميزات المتقدمة التي تجعل إدارة بوتك أسهل من أي وقت مضى</p>
                </div>
            </div>
            
            <div class="row g-4">
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h3 class="feature-title">نظام الإدارة والمودريشن</h3>
                        <p class="feature-description">
                            أدوات قوية للإدارة تشمل الإنذارات، الكتم، الطرد، والحظر مع سجل مفصل لجميع الإجراءات
                        </p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-ticket-alt"></i>
                        </div>
                        <h3 class="feature-title">نظام التذاكر</h3>
                        <p class="feature-description">
                            نظام تذاكر متطور لدعم الأعضاء مع إمكانية التصنيف والأولوية وحفظ المحادثات
                        </p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h3 class="feature-title">نظام المستويات والـ XP</h3>
                        <p class="feature-description">
                            نظام نقاط وترقيات تفاعلي لتحفيز الأعضاء مع لوحة متصدرين ومكافآت مخصصة
                        </p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-hand-wave"></i>
                        </div>
                        <h3 class="feature-title">الترحيب والمغادرة</h3>
                        <p class="feature-description">
                            رسائل ترحيب ومغادرة قابلة للتخصيص مع دعم الصور والـ Embeds والرتب التلقائية
                        </p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-robot"></i>
                        </div>
                        <h3 class="feature-title">الردود التلقائية</h3>
                        <p class="feature-description">
                            إنشاء ردود تلقائية ذكية للكلمات المفتاحية مع دعم النصوص والصور والملفات
                        </p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <h3 class="feature-title">سجلات مفصلة</h3>
                        <p class="feature-description">
                            تتبع جميع الأنشطة في الخادم مع سجلات مفصلة وقابلة للبحث والتصفية
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="stats-section">
        <div class="container">
            <div class="row">
                <div class="col-lg-3 col-md-6">
                    <div class="stat-item">
                        <span class="stat-number">24/7</span>
                        <span class="stat-label">متاح دائماً</span>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6">
                    <div class="stat-item">
                        <span class="stat-number">100%</span>
                        <span class="stat-label">مجاني</span>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6">
                    <div class="stat-item">
                        <span class="stat-number">∞</span>
                        <span class="stat-label">خوادم غير محدودة</span>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6">
                    <div class="stat-item">
                        <span class="stat-number">🇸🇦</span>
                        <span class="stat-label">دعم عربي كامل</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
        
        // Add animation on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);
        
        // Observe feature cards
        document.querySelectorAll('.feature-card').forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            card.style.transition = 'all 0.6s ease';
            observer.observe(card);
        });
    </script>
</body>
</html>
