# 🤖 لوحة تحكم بوت ديسكورد

لوحة تحكم شاملة ومتطورة لإدارة بوت ديسكورد مع واجهة عربية سهلة الاستخدام.

## 👨‍💻 المطور

**CS Discord**
- 📺 يوتيوب: [https://www.youtube.com/@CS_Discord](https://www.youtube.com/@CS_Discord)
- 💬 ديسكورد: [https://discord.gg/yqTn2EwVsd](https://discord.gg/yqTn2EwVsd)

> ⚠️ **تحذير مهم**: يرجى عدم حذف أو تعديل حقوق المطور. هذا المشروع مجاني ومفتوح المصدر للمجتمع العربي.

## ✨ الميزات

### 🏠 الصفحة الرئيسية
- إحصائيات شاملة للبوت والخوادم
- حالة البوت المباشرة
- أزرار سريعة للإجراءات المهمة
- نظرة عامة على النشاطات الأخيرة

### ⚙️ الإعدادات العامة
- تخصيص البريفكس
- اختيار اللغة (عربي/إنجليزي)
- إعدادات المنطقة الزمنية
- تخصيص المظهر (فاتح/غامق)

### 🛡️ نظام الإدارة والمودريشن
- أوامر الطرد والحظر والكتم
- نظام الإنذارات المتقدم
- إجراءات تلقائية عند تكرار المخالفات
- سجل مفصل لجميع إجراءات المودريشن

### 🎟️ نظام التذاكر
- إنشاء وإدارة التذاكر
- تصنيف التذاكر حسب الأولوية
- حفظ نسخ من المحادثات
- إغلاق تلقائي للتذاكر القديمة

### 👋 الترحيب والمغادرة
- رسائل ترحيب قابلة للتخصيص
- دعم الصور والـ Embeds
- رتب تلقائية عند الانضمام
- رسائل مغادرة مخصصة

### 🤖 الردود التلقائية
- ردود ذكية للكلمات المفتاحية
- دعم النصوص والصور والملفات
- فترات تهدئة قابلة للتخصيص
- شروط متقدمة للتفعيل

### 🧬 نظام المستويات والـ XP
- نظام نقاط تفاعلي
- لوحة متصدرين
- مكافآت عند الوصول لمستويات معينة
- إحصائيات مفصلة للأعضاء

### 📜 سجلات النظام
- تتبع جميع الأنشطة
- سجلات قابلة للبحث والتصفية
- أرشفة تلقائية للسجلات القديمة
- تصدير السجلات

### 💾 النسخ الاحتياطي
- إنشاء نسخ احتياطية شاملة
- استعادة الإعدادات
- تصدير/استيراد البيانات
- جدولة النسخ التلقائية

## 🚀 التثبيت

### المتطلبات
- Node.js (الإصدار 16 أو أحدث)
- MongoDB
- حساب Discord Bot

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone https://github.com/your-username/discord-bot-dashboard.git
cd discord-bot-dashboard
```

2. **تثبيت المكتبات**
```bash
npm install
```

3. **إعداد متغيرات البيئة**
```bash
cp .env.example .env
```

قم بتعديل ملف `.env` وإضافة المعلومات المطلوبة:
```env
# إعدادات البوت
BOT_TOKEN=your_bot_token_here
CLIENT_ID=your_client_id_here
CLIENT_SECRET=your_client_secret_here
OWNER_ID=your_discord_user_id

# إعدادات قاعدة البيانات
MONGO_URI=mongodb://localhost:27017/discord-bot-dashboard

# إعدادات الأمان
SESSION_SECRET=your-super-secret-session-key-here
JWT_SECRET=your-jwt-secret-key

# إعدادات الخادم
PORT=3000
BASE_URL=http://localhost:3000
```

4. **تشغيل التطبيق**
```bash
# للتطوير
npm run dev

# للإنتاج
npm start
```

## 🔧 الإعداد

### إنشاء بوت Discord

1. اذهب إلى [Discord Developer Portal](https://discord.com/developers/applications)
2. أنشئ تطبيق جديد
3. اذهب إلى قسم "Bot" وأنشئ بوت
4. انسخ التوكن وضعه في متغير `BOT_TOKEN`
5. في قسم "OAuth2"، أضف رابط إعادة التوجيه: `http://localhost:3000/auth/discord/callback`

### إعداد قاعدة البيانات

تأكد من تشغيل MongoDB على جهازك أو استخدم خدمة سحابية مثل MongoDB Atlas.

### إضافة البوت للخادم

استخدم الرابط التالي لإضافة البوت لخادمك (استبدل CLIENT_ID بمعرف تطبيقك):
```
https://discord.com/api/oauth2/authorize?client_id=CLIENT_ID&permissions=8&scope=bot%20applications.commands
```

## 📱 التصميم المتجاوب

اللوحة مصممة لتعمل بشكل مثالي على:
- أجهزة الكمبيوتر المكتبية
- الأجهزة اللوحية
- الهواتف الذكية

## 🎨 المظاهر

- **المظهر الغامق**: المظهر الافتراضي المحسن للعيون
- **المظهر الفاتح**: مظهر كلاسيكي ونظيف

## 🌐 اللغات المدعومة

- العربية (افتراضي)
- الإنجليزية

## 🔒 الأمان

- مصادقة OAuth2 عبر Discord
- حماية من هجمات CSRF
- تشفير كلمات المرور
- حد أقصى للطلبات لمنع الإساءة
- جلسات آمنة

## 📊 قواعد البيانات

المشروع يستخدم MongoDB مع النماذج التالية:
- `GuildSettings`: إعدادات الخوادم
- `ModerationLogs`: سجلات المودريشن
- `Tickets`: نظام التذاكر
- `AutoResponses`: الردود التلقائية
- `XPLevels`: نظام المستويات
- `DashboardUsers`: مستخدمي اللوحة

## 🛠️ التطوير

### بنية المشروع
```
├── models/              # نماذج قاعدة البيانات
├── routes/              # مسارات التطبيق
├── views/               # قوالب EJS
├── public/              # الملفات الثابتة
├── middleware/          # الوسطاء
├── auth/                # إعدادات المصادقة
├── config.js            # إعدادات التطبيق
└── index.js             # نقطة البداية
```

### إضافة ميزات جديدة

1. أنشئ النموذج في مجلد `models/`
2. أضف المسارات في مجلد `routes/`
3. أنشئ القوالب في مجلد `views/`
4. أضف الأنماط في مجلد `public/css/`
5. أضف JavaScript في مجلد `public/js/`

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع للميزة الجديدة
3. إجراء التغييرات
4. إرسال Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 الدعم

- إنشاء [Issue](https://github.com/your-username/discord-bot-dashboard/issues) للإبلاغ عن الأخطاء
- انضم لخادم Discord للدعم: [رابط الدعم]
- راسلنا على: <EMAIL>

## 🙏 شكر خاص

- [Discord.js](https://discord.js.org/) - مكتبة Discord
- [Express.js](https://expressjs.com/) - إطار عمل الويب
- [MongoDB](https://www.mongodb.com/) - قاعدة البيانات
- [Bootstrap](https://getbootstrap.com/) - إطار عمل CSS
- [Font Awesome](https://fontawesome.com/) - الأيقونات

---

صنع بـ ❤️ للمجتمع العربي
