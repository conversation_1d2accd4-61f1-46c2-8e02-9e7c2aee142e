# 🔐 دليل الإعدادات المحمية

## 🛡️ نظام الحماية المتقدم

تم إنشاء نظام حماية متقدم للإعدادات الحساسة لحمايتها من التعديل أو السرقة.

## 📁 مكان الإعدادات المحمية

الإعدادات الحساسة موجودة في:
```
core/security/credentials.js
```

## 🔧 كيفية تعديل الإعدادات

### 1. فتح الملف المحمي
```bash
notepad core/security/credentials.js  # Windows
nano core/security/credentials.js     # Linux/Mac
```

### 2. تعديل الإعدادات المشفرة

ابحث عن `ENCRYPTED_CREDENTIALS` وعدّل القيم:

```javascript
const ENCRYPTED_CREDENTIALS = {
  // توكن البوت (غير مشفر - طبيعي من Discord)
  botToken: 'ضع_توكن_البوت_هنا',
  
  // معرف التطبيق (مشفر base64)
  clientId: 'base64_encoded_client_id',
  
  // سر التطبيق (مشفر base64)
  clientSecret: 'base64_encoded_client_secret',
  
  // معرف المالك (مشفر base64)
  ownerId: 'base64_encoded_owner_id',
  
  // رابط قاعدة البيانات (مشفر base64)
  mongoUri: 'base64_encoded_mongo_uri',
  
  // مفتاح الجلسة (مشفر base64)
  sessionSecret: 'base64_encoded_session_secret',
  
  // مفتاح JWT (مشفر base64)
  jwtSecret: 'base64_encoded_jwt_secret'
};
```

## 🔐 كيفية تشفير القيم

### طريقة سريعة (أونلاين):
1. اذهب إلى: https://www.base64encode.org/
2. ضع القيمة المطلوبة
3. اضغط "Encode"
4. انسخ النتيجة

### طريقة Node.js:
```javascript
// لتشفير قيمة
const encoded = Buffer.from('your_value_here').toString('base64');
console.log(encoded);

// لفك التشفير (للتأكد)
const decoded = Buffer.from(encoded, 'base64').toString('utf-8');
console.log(decoded);
```

## 🛡️ مميزات الحماية

### 1. إخفاء الإعدادات
- ✅ الإعدادات مشفرة بـ base64
- ✅ صعبة القراءة بالعين المجردة
- ✅ محمية من النسخ السريع

### 2. حماية من التعديل
- ✅ فحص دوري كل 30 ثانية
- ✅ إيقاف النظام عند اكتشاف تعديل
- ✅ حماية ضد تجميد الكائنات

### 3. حماية من المشاركة
- ✅ الملف مخفي في .gitignore
- ✅ لن يتم رفعه على GitHub
- ✅ محمي من المشاركة العرضية

## ⚠️ تحذيرات مهمة

1. **لا تشارك ملف credentials.js مع أي شخص**
2. **احتفظ بنسخة احتياطية من الإعدادات**
3. **لا تحذف أو تعدل حماية CS Discord**
4. **تأكد من صحة التشفير قبل الحفظ**

## 🔧 حل المشاكل

### مشكلة: "Authentication failed"
- تحقق من صحة رابط قاعدة البيانات المشفر
- تأكد من صحة اسم المستخدم وكلمة المرور

### مشكلة: "خطأ في فك تشفير الإعدادات"
- تحقق من صحة التشفير base64
- تأكد من عدم وجود مسافات زائدة

### مشكلة: "تم اكتشاف تعديل في الإعدادات"
- لا تعدل أي شيء خارج ENCRYPTED_CREDENTIALS
- لا تحذف أو تعدل دوال الحماية

## 📞 الدعم

📺 **يوتيوب**: https://www.youtube.com/@CS_Discord  
💬 **ديسكورد**: https://discord.gg/yqTn2EwVsd

---
**تطوير: CS Discord** 🛡️
