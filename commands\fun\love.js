// ========================================
// 💕 أمر نسبة الحب - CS Discord
// ========================================
// 📺 يوتيوب: https://www.youtube.com/@CS_Discord
// 💬 ديسكورد: https://discord.gg/yqTn2EwVsd
// ========================================

const { EmbedBuilder } = require('discord.js');

module.exports = {
  name: 'love',
  aliases: ['حب', 'نسبة_الحب', 'love_rate'],
  description: 'احسب نسبة الحب بينك وبين شخص آخر 💕',
  usage: '<@المستخدم>',
  category: 'fun',
  cooldown: 5,
  
  async execute(message, args) {
    try {
      // التحقق من وجود المستخدم المذكور
      const targetUser = message.mentions.users.first();
      
      if (!targetUser) {
        return await message.reply('❌ يجب ذكر المستخدم الذي تريد حساب نسبة الحب معه!\nمثال: `love @أحمد`');
      }
      
      // التحقق من عدم حساب الحب مع النفس
      if (targetUser.id === message.author.id) {
        return await message.reply('😅 حب النفس شيء جميل، لكن جرب مع شخص آخر!');
      }
      
      // التحقق من عدم حساب الحب مع البوت
      if (targetUser.bot) {
        return await message.reply('🤖 البوتات لا تفهم الحب! جرب مع إنسان!');
      }
      
      // حساب نسبة الحب (ثابتة لنفس الأشخاص)
      const lovePercentage = calculateLovePercentage(message.author.id, targetUser.id);
      
      // تحديد مستوى الحب
      let loveLevel = '';
      let loveEmoji = '';
      let loveColor = '';
      let loveMessage = '';
      
      if (lovePercentage >= 90) {
        loveLevel = 'حب أسطوري';
        loveEmoji = '💖✨';
        loveColor = '#ff1493';
        loveMessage = 'هذا حب حقيقي! أنتما مخلوقان لبعضكما البعض! 💕';
      } else if (lovePercentage >= 75) {
        loveLevel = 'حب قوي جداً';
        loveEmoji = '💕💫';
        loveColor = '#ff69b4';
        loveMessage = 'حب رائع! هناك كيمياء قوية بينكما! 😍';
      } else if (lovePercentage >= 60) {
        loveLevel = 'حب جميل';
        loveEmoji = '💗💝';
        loveColor = '#ff6347';
        loveMessage = 'حب لطيف! يمكن أن تكونا زوجين رائعين! 🥰';
      } else if (lovePercentage >= 40) {
        loveLevel = 'إعجاب متبادل';
        loveEmoji = '💘💌';
        loveColor = '#ffa500';
        loveMessage = 'هناك إعجاب! ربما يتطور إلى شيء أكبر! 😊';
      } else if (lovePercentage >= 20) {
        loveLevel = 'صداقة جميلة';
        loveEmoji = '💛🤝';
        loveColor = '#ffff00';
        loveMessage = 'صداقة رائعة! الحب يبدأ بالصداقة! 😄';
      } else {
        loveLevel = 'مجرد معارف';
        loveEmoji = '💙🤷';
        loveColor = '#87ceeb';
        loveMessage = 'لا يوجد حب... حتى الآن! ربما في المستقبل! 😅';
      }
      
      // إنشاء شريط الحب
      const loveBar = createLoveBar(lovePercentage);
      
      // إنشاء الـ embed
      const embed = new EmbedBuilder()
        .setColor(loveColor)
        .setTitle(`${loveEmoji} حاسبة الحب ${loveEmoji}`)
        .setDescription(`💕 **نسبة الحب بين ${message.author.username} و ${targetUser.username}**`)
        .addFields(
          {
            name: '💖 النسبة',
            value: `**${lovePercentage}%**`,
            inline: true
          },
          {
            name: '📊 المستوى',
            value: `**${loveLevel}**`,
            inline: true
          },
          {
            name: '📈 شريط الحب',
            value: loveBar,
            inline: false
          },
          {
            name: '💌 الرسالة',
            value: loveMessage,
            inline: false
          }
        )
        .setFooter({
          text: 'CS Discord Fun Commands | هذا مجرد ترفيه!',
          iconURL: message.client.user.displayAvatarURL()
        })
        .setTimestamp();
      
      // إضافة صورة حسب النسبة
      if (lovePercentage >= 75) {
        embed.setImage('https://media.giphy.com/media/3o6fJeJGYpJWvzgbQs/giphy.gif');
      } else if (lovePercentage >= 50) {
        embed.setImage('https://media.giphy.com/media/l0HlNQ03J5JxX6lva/giphy.gif');
      }
      
      // إضافة نصائح عشوائية
      if (Math.random() < 0.3) {
        const loveTips = [
          '💡 نصيحة: الحب الحقيقي يحتاج وقت لينمو!',
          '💡 تذكر: هذا مجرد ترفيه، الحب الحقيقي أعمق من الأرقام!',
          '💡 معلومة: الصداقة هي أساس كل علاقة ناجحة!',
          '💡 نصيحة: كن نفسك دائماً في العلاقات!',
          '💡 تذكر: الاحترام المتبادل أهم من كل شيء!'
        ];
        
        embed.addFields({
          name: '💡 نصيحة في الحب',
          value: loveTips[Math.floor(Math.random() * loveTips.length)],
          inline: false
        });
      }
      
      await message.reply({ embeds: [embed] });
      
      // إضافة ردود فعل حسب النسبة
      const reactions = lovePercentage >= 75 ? ['💕', '😍', '💖'] : 
                       lovePercentage >= 50 ? ['💗', '😊', '💘'] : 
                       ['💛', '😄', '🤝'];
      
      try {
        for (const reaction of reactions) {
          await message.react(reaction);
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      } catch (error) {
        // تجاهل خطأ الردود إذا لم تكن متاحة
      }
      
    } catch (error) {
      console.error('خطأ في أمر نسبة الحب:', error);
      await message.reply('❌ حدث خطأ أثناء حساب نسبة الحب!');
    }
  }
};

// حساب نسبة الحب (ثابتة لنفس الأشخاص)
function calculateLovePercentage(userId1, userId2) {
  // ترتيب المعرفات لضمان نفس النتيجة دائماً
  const sortedIds = [userId1, userId2].sort();
  const combinedId = sortedIds.join('');
  
  // إنشاء hash بسيط
  let hash = 0;
  for (let i = 0; i < combinedId.length; i++) {
    const char = combinedId.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // تحويل إلى 32bit integer
  }
  
  // تحويل إلى نسبة مئوية
  return Math.abs(hash) % 101; // 0-100
}

// إنشاء شريط الحب
function createLoveBar(percentage) {
  const totalBars = 20;
  const filledBars = Math.round((percentage / 100) * totalBars);
  const emptyBars = totalBars - filledBars;
  
  let bar = '';
  
  // القلوب الممتلئة
  for (let i = 0; i < filledBars; i++) {
    if (percentage >= 75) {
      bar += '💖';
    } else if (percentage >= 50) {
      bar += '💗';
    } else if (percentage >= 25) {
      bar += '💛';
    } else {
      bar += '💙';
    }
  }
  
  // القلوب الفارغة
  for (let i = 0; i < emptyBars; i++) {
    bar += '🤍';
  }
  
  return `${bar} **${percentage}%**`;
}
