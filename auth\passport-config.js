const passport = require('passport');
const DiscordStrategy = require('passport-discord').Strategy;
const { DashboardUsers } = require('../models');
const config = require('../config');

// إعداد استراتيجية Discord
passport.use(new DiscordStrategy({
  clientID: config.oauth.clientId,
  clientSecret: config.oauth.clientSecret,
  callbackURL: config.oauth.redirectUri,
  scope: config.oauth.scopes
}, async (accessToken, refreshToken, profile, done) => {
  try {
    // البحث عن المستخدم في قاعدة البيانات
    let user = await DashboardUsers.findOne({ discordId: profile.id });

    if (user) {
      // تحديث معلومات المستخدم الموجود
      user.username = profile.username;
      user.discriminator = profile.discriminator;
      user.avatar = profile.avatar;
      user.email = profile.email;
      
      await user.save();
    } else {
      // إنشاء مستخدم جديد
      user = new DashboardUsers({
        discordId: profile.id,
        username: profile.username,
        discriminator: profile.discriminator,
        avatar: profile.avatar,
        email: profile.email,
        account: {
          isVerified: true
        },
        permissions: {
          system: {
            isAdmin: false,
            isModerator: false,
            canAccessLogs: false,
            canManageUsers: false,
            canViewAnalytics: false
          },
          guilds: []
        },
        preferences: {
          language: 'ar',
          theme: 'dark',
          timezone: 'Asia/Riyadh'
        },
        metadata: {
          source: 'discord'
        }
      });

      await user.save();
    }

    return done(null, user);
  } catch (error) {
    console.error('خطأ في مصادقة Discord:', error);
    return done(error, null);
  }
}));

// تسلسل المستخدم للجلسة
passport.serializeUser((user, done) => {
  done(null, user.discordId);
});

// إلغاء تسلسل المستخدم من الجلسة
passport.deserializeUser(async (discordId, done) => {
  try {
    const user = await DashboardUsers.findOne({ discordId });
    done(null, user);
  } catch (error) {
    console.error('خطأ في إلغاء تسلسل المستخدم:', error);
    done(error, null);
  }
});

module.exports = passport;
