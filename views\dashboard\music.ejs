<%- include('../partials/header') %>

<div class="main-content">
    <div class="content-header">
        <h1 class="page-title">
            <i class="fas fa-music"></i>
            نظام الموسيقى
        </h1>
        <p class="page-description">
            تشغيل وإدارة الموسيقى في القنوات الصوتية
        </p>
    </div>

    <!-- حالة المشغل -->
    <div class="music-player">
        <div class="player-info">
            <div class="track-artwork">
                <img src="/static/images/music-placeholder.jpg" alt="Track" id="trackArtwork">
                <div class="play-overlay">
                    <i class="fas fa-play" id="playIcon"></i>
                </div>
            </div>
            <div class="track-details">
                <h3 id="trackTitle">لا يوجد تشغيل حالياً</h3>
                <p id="trackArtist">اختر أغنية للبدء</p>
                <div class="track-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="time-info">
                        <span id="currentTime">0:00</span>
                        <span id="totalTime">0:00</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="player-controls">
            <button class="control-btn" onclick="previousTrack()">
                <i class="fas fa-step-backward"></i>
            </button>
            <button class="control-btn play-btn" onclick="togglePlay()">
                <i class="fas fa-play" id="mainPlayIcon"></i>
            </button>
            <button class="control-btn" onclick="nextTrack()">
                <i class="fas fa-step-forward"></i>
            </button>
            <button class="control-btn" onclick="toggleShuffle()">
                <i class="fas fa-random" id="shuffleIcon"></i>
            </button>
            <button class="control-btn" onclick="toggleRepeat()">
                <i class="fas fa-redo" id="repeatIcon"></i>
            </button>
        </div>
        
        <div class="volume-control">
            <i class="fas fa-volume-up"></i>
            <input type="range" class="volume-slider" min="0" max="100" value="50" onchange="setVolume(this.value)">
            <span id="volumeValue">50%</span>
        </div>
    </div>

    <!-- إحصائيات الموسيقى -->
    <div class="stats-grid">
        <div class="stat-card playing">
            <div class="stat-number" id="current-listeners">0</div>
            <div class="stat-label">مستمعين حالياً</div>
        </div>
        <div class="stat-card queue">
            <div class="stat-number" id="queue-count">0</div>
            <div class="stat-label">أغاني في القائمة</div>
        </div>
        <div class="stat-card total">
            <div class="stat-number" id="total-played">247</div>
            <div class="stat-label">إجمالي المشغل</div>
        </div>
        <div class="stat-card favorites">
            <div class="stat-number" id="favorites-count">45</div>
            <div class="stat-label">المفضلة</div>
        </div>
    </div>

    <!-- البحث وإضافة الموسيقى -->
    <div class="card">
        <div class="card-header">
            <div class="card-icon">
                <i class="fas fa-search"></i>
            </div>
            <div>
                <h3 class="card-title">البحث عن الموسيقى</h3>
            </div>
        </div>
        <div class="card-body">
            <div class="search-container">
                <div class="search-input-group">
                    <input type="text" class="form-control" id="musicSearch" placeholder="ابحث عن أغنية، فنان، أو رابط YouTube...">
                    <button class="btn btn-primary" onclick="searchMusic()">
                        <i class="fas fa-search"></i>
                        بحث
                    </button>
                </div>
                
                <div class="search-filters">
                    <button class="filter-btn active" onclick="setSearchType('all')">الكل</button>
                    <button class="filter-btn" onclick="setSearchType('youtube')">YouTube</button>
                    <button class="filter-btn" onclick="setSearchType('spotify')">Spotify</button>
                    <button class="filter-btn" onclick="setSearchType('soundcloud')">SoundCloud</button>
                </div>
            </div>
            
            <div class="search-results" id="searchResults">
                <div class="no-results">
                    <i class="fas fa-music"></i>
                    <p>ابحث عن الموسيقى لإضافتها إلى القائمة</p>
                </div>
            </div>
        </div>
    </div>

    <!-- قائمة التشغيل -->
    <div class="card">
        <div class="card-header">
            <div class="card-icon">
                <i class="fas fa-list"></i>
            </div>
            <div>
                <h3 class="card-title">قائمة التشغيل</h3>
            </div>
            <div class="card-actions">
                <button class="btn btn-sm btn-warning" onclick="shuffleQueue()">
                    <i class="fas fa-random"></i>
                    خلط
                </button>
                <button class="btn btn-sm btn-danger" onclick="clearQueue()">
                    <i class="fas fa-trash"></i>
                    مسح
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="queue-container" id="queueContainer">
                <div class="empty-queue">
                    <i class="fas fa-list-music"></i>
                    <h4>قائمة التشغيل فارغة</h4>
                    <p>ابحث عن الموسيقى وأضفها إلى القائمة</p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.music-player {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    color: white;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
}

.player-info {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 20px;
}

.track-artwork {
    position: relative;
    width: 80px;
    height: 80px;
    border-radius: 15px;
    overflow: hidden;
}

.track-artwork img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.play-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    cursor: pointer;
}

.track-artwork:hover .play-overlay {
    opacity: 1;
}

.track-details {
    flex: 1;
}

.track-details h3 {
    margin: 0 0 5px 0;
    font-size: 1.3rem;
}

.track-details p {
    margin: 0 0 15px 0;
    opacity: 0.8;
}

.track-progress {
    width: 100%;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    margin-bottom: 8px;
    cursor: pointer;
}

.progress-fill {
    height: 100%;
    background: white;
    border-radius: 3px;
    width: 0%;
    transition: width 0.1s ease;
}

.time-info {
    display: flex;
    justify-content: space-between;
    font-size: 0.9rem;
    opacity: 0.8;
}

.player-controls {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 20px;
}

.control-btn {
    width: 50px;
    height: 50px;
    border: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.control-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.play-btn {
    width: 60px;
    height: 60px;
    background: white;
    color: #667eea;
    font-size: 1.5rem;
}

.volume-control {
    display: flex;
    align-items: center;
    gap: 10px;
    justify-content: center;
}

.volume-slider {
    width: 100px;
    height: 6px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    outline: none;
    cursor: pointer;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.stat-card.playing {
    background: linear-gradient(135deg, #00ff88, #00d4aa);
    color: white;
}

.stat-card.queue {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.stat-card.total {
    background: linear-gradient(135deg, #feca57, #ff9ff3);
    color: white;
}

.stat-card.favorites {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
}

.search-container {
    margin-bottom: 20px;
}

.search-input-group {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.search-input-group input {
    flex: 1;
}

.search-filters {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 8px 16px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.8);
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-btn:hover, .filter-btn.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.search-results {
    min-height: 200px;
}

.no-results {
    text-align: center;
    padding: 50px;
    color: rgba(255, 255, 255, 0.6);
}

.no-results i {
    font-size: 3rem;
    margin-bottom: 15px;
    opacity: 0.5;
}

.result-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    margin-bottom: 10px;
    transition: all 0.3s ease;
}

.result-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(5px);
}

.result-thumbnail {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    overflow: hidden;
}

.result-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.result-info {
    flex: 1;
}

.result-title {
    color: white;
    font-weight: bold;
    margin-bottom: 5px;
}

.result-artist {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.result-duration {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.8rem;
}

.result-actions {
    display: flex;
    gap: 10px;
}

.queue-container {
    min-height: 300px;
}

.empty-queue {
    text-align: center;
    padding: 50px;
    color: rgba(255, 255, 255, 0.6);
}

.empty-queue i {
    font-size: 3rem;
    margin-bottom: 15px;
    opacity: 0.5;
}

.queue-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    margin-bottom: 10px;
    transition: all 0.3s ease;
}

.queue-item:hover {
    background: rgba(255, 255, 255, 0.1);
}

.queue-item.playing {
    background: rgba(102, 126, 234, 0.3);
    border-left: 4px solid #667eea;
}

.queue-number {
    width: 30px;
    text-align: center;
    color: rgba(255, 255, 255, 0.6);
    font-weight: bold;
}

@media (max-width: 768px) {
    .player-info {
        flex-direction: column;
        text-align: center;
    }
    
    .search-input-group {
        flex-direction: column;
    }
    
    .result-item, .queue-item {
        flex-direction: column;
        text-align: center;
    }
}
</style>

<script>
// حالة المشغل
let isPlaying = false;
let currentTrack = null;
let queue = [];
let currentIndex = 0;
let isShuffled = false;
let isRepeating = false;

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadMusicData();
});

// تحميل بيانات الموسيقى
function loadMusicData() {
    // بيانات تجريبية
    console.log('🎵 تم تحميل نظام الموسيقى');
}

// تبديل التشغيل/الإيقاف
function togglePlay() {
    isPlaying = !isPlaying;
    const playIcon = document.getElementById('mainPlayIcon');
    const overlayIcon = document.getElementById('playIcon');
    
    if (isPlaying) {
        playIcon.className = 'fas fa-pause';
        overlayIcon.className = 'fas fa-pause';
        showNotification('🎵 تم بدء التشغيل', 'success');
    } else {
        playIcon.className = 'fas fa-play';
        overlayIcon.className = 'fas fa-play';
        showNotification('⏸️ تم إيقاف التشغيل', 'info');
    }
}

// الأغنية السابقة
function previousTrack() {
    if (currentIndex > 0) {
        currentIndex--;
        playTrack(queue[currentIndex]);
    }
    showNotification('⏮️ الأغنية السابقة', 'info');
}

// الأغنية التالية
function nextTrack() {
    if (currentIndex < queue.length - 1) {
        currentIndex++;
        playTrack(queue[currentIndex]);
    }
    showNotification('⏭️ الأغنية التالية', 'info');
}

// تبديل الخلط
function toggleShuffle() {
    isShuffled = !isShuffled;
    const shuffleIcon = document.getElementById('shuffleIcon');
    
    if (isShuffled) {
        shuffleIcon.style.color = '#667eea';
        showNotification('🔀 تم تفعيل الخلط', 'success');
    } else {
        shuffleIcon.style.color = '';
        showNotification('🔀 تم إلغاء الخلط', 'info');
    }
}

// تبديل التكرار
function toggleRepeat() {
    isRepeating = !isRepeating;
    const repeatIcon = document.getElementById('repeatIcon');
    
    if (isRepeating) {
        repeatIcon.style.color = '#667eea';
        showNotification('🔁 تم تفعيل التكرار', 'success');
    } else {
        repeatIcon.style.color = '';
        showNotification('🔁 تم إلغاء التكرار', 'info');
    }
}

// تعديل مستوى الصوت
function setVolume(value) {
    document.getElementById('volumeValue').textContent = value + '%';
    showNotification(`🔊 مستوى الصوت: ${value}%`, 'info');
}

// البحث عن الموسيقى
function searchMusic() {
    const query = document.getElementById('musicSearch').value.trim();
    
    if (!query) {
        showNotification('يرجى إدخال كلمة البحث!', 'warning');
        return;
    }
    
    showNotification('🔍 جاري البحث...', 'info');
    
    // محاكاة نتائج البحث
    setTimeout(() => {
        const results = [
            {
                title: 'أغنية تجريبية 1',
                artist: 'فنان تجريبي',
                duration: '3:45',
                thumbnail: '/static/images/music-placeholder.jpg',
                url: 'https://example.com/song1'
            },
            {
                title: 'أغنية تجريبية 2',
                artist: 'فنان آخر',
                duration: '4:12',
                thumbnail: '/static/images/music-placeholder.jpg',
                url: 'https://example.com/song2'
            }
        ];
        
        displaySearchResults(results);
        showNotification(`✅ تم العثور على ${results.length} نتيجة`, 'success');
    }, 1500);
}

// عرض نتائج البحث
function displaySearchResults(results) {
    const container = document.getElementById('searchResults');
    
    if (results.length === 0) {
        container.innerHTML = `
            <div class="no-results">
                <i class="fas fa-search"></i>
                <p>لم يتم العثور على نتائج</p>
            </div>
        `;
        return;
    }
    
    container.innerHTML = results.map(result => `
        <div class="result-item">
            <div class="result-thumbnail">
                <img src="${result.thumbnail}" alt="${result.title}">
            </div>
            <div class="result-info">
                <div class="result-title">${result.title}</div>
                <div class="result-artist">${result.artist}</div>
                <div class="result-duration">${result.duration}</div>
            </div>
            <div class="result-actions">
                <button class="btn btn-sm btn-success" onclick="addToQueue('${result.title}', '${result.artist}', '${result.duration}')">
                    <i class="fas fa-plus"></i>
                    إضافة
                </button>
                <button class="btn btn-sm btn-primary" onclick="playNow('${result.title}', '${result.artist}', '${result.duration}')">
                    <i class="fas fa-play"></i>
                    تشغيل
                </button>
            </div>
        </div>
    `).join('');
}

// إضافة إلى القائمة
function addToQueue(title, artist, duration) {
    const track = { title, artist, duration };
    queue.push(track);
    
    updateQueueDisplay();
    updateStats();
    showNotification(`✅ تم إضافة "${title}" إلى القائمة`, 'success');
}

// تشغيل الآن
function playNow(title, artist, duration) {
    const track = { title, artist, duration };
    queue.unshift(track);
    currentIndex = 0;
    
    playTrack(track);
    updateQueueDisplay();
    updateStats();
}

// تشغيل أغنية
function playTrack(track) {
    currentTrack = track;
    
    document.getElementById('trackTitle').textContent = track.title;
    document.getElementById('trackArtist').textContent = track.artist;
    
    isPlaying = true;
    document.getElementById('mainPlayIcon').className = 'fas fa-pause';
    document.getElementById('playIcon').className = 'fas fa-pause';
    
    showNotification(`🎵 يتم تشغيل: ${track.title}`, 'success');
}

// تحديث عرض القائمة
function updateQueueDisplay() {
    const container = document.getElementById('queueContainer');
    
    if (queue.length === 0) {
        container.innerHTML = `
            <div class="empty-queue">
                <i class="fas fa-list-music"></i>
                <h4>قائمة التشغيل فارغة</h4>
                <p>ابحث عن الموسيقى وأضفها إلى القائمة</p>
            </div>
        `;
        return;
    }
    
    container.innerHTML = queue.map((track, index) => `
        <div class="queue-item ${index === currentIndex ? 'playing' : ''}">
            <div class="queue-number">${index + 1}</div>
            <div class="result-thumbnail">
                <img src="/static/images/music-placeholder.jpg" alt="${track.title}">
            </div>
            <div class="result-info">
                <div class="result-title">${track.title}</div>
                <div class="result-artist">${track.artist}</div>
                <div class="result-duration">${track.duration}</div>
            </div>
            <div class="result-actions">
                <button class="btn btn-sm btn-primary" onclick="playTrackAt(${index})">
                    <i class="fas fa-play"></i>
                </button>
                <button class="btn btn-sm btn-danger" onclick="removeFromQueue(${index})">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `).join('');
}

// تشغيل أغنية في موضع معين
function playTrackAt(index) {
    currentIndex = index;
    playTrack(queue[index]);
    updateQueueDisplay();
}

// إزالة من القائمة
function removeFromQueue(index) {
    const track = queue[index];
    queue.splice(index, 1);
    
    if (index < currentIndex) {
        currentIndex--;
    } else if (index === currentIndex && currentIndex >= queue.length) {
        currentIndex = queue.length - 1;
    }
    
    updateQueueDisplay();
    updateStats();
    showNotification(`❌ تم إزالة "${track.title}" من القائمة`, 'info');
}

// خلط القائمة
function shuffleQueue() {
    if (queue.length <= 1) return;
    
    const currentTrackData = queue[currentIndex];
    queue.splice(currentIndex, 1);
    
    // خلط باقي الأغاني
    for (let i = queue.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [queue[i], queue[j]] = [queue[j], queue[i]];
    }
    
    // إعادة الأغنية الحالية للمقدمة
    queue.unshift(currentTrackData);
    currentIndex = 0;
    
    updateQueueDisplay();
    showNotification('🔀 تم خلط القائمة', 'success');
}

// مسح القائمة
function clearQueue() {
    if (queue.length === 0) return;
    
    if (confirm('هل أنت متأكد من مسح قائمة التشغيل؟')) {
        queue = [];
        currentIndex = 0;
        currentTrack = null;
        
        document.getElementById('trackTitle').textContent = 'لا يوجد تشغيل حالياً';
        document.getElementById('trackArtist').textContent = 'اختر أغنية للبدء';
        
        isPlaying = false;
        document.getElementById('mainPlayIcon').className = 'fas fa-play';
        document.getElementById('playIcon').className = 'fas fa-play';
        
        updateQueueDisplay();
        updateStats();
        showNotification('🗑️ تم مسح قائمة التشغيل', 'info');
    }
}

// تحديث الإحصائيات
function updateStats() {
    document.getElementById('queue-count').textContent = queue.length;
    document.getElementById('current-listeners').textContent = isPlaying ? '1' : '0';
}

// تعيين نوع البحث
function setSearchType(type) {
    document.querySelectorAll('.filter-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.classList.add('active');
    
    showNotification(`🔍 تم تعيين البحث إلى: ${type}`, 'info');
}

// إظهار الإشعارات
function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type} notification`;
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        animation: slideIn 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}
</script>

<%- include('../partials/footer') %>
